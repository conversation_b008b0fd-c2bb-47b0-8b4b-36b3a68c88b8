import { defineStore } from 'pinia';
import mallApi from '@/api/mall';
import addressApi from '@/api/mall/address';

export const useMallUserStore = defineStore('mallUser', {
  state: () => ({
    token: null,
    user: null,
    isLoggedIn: false,
    loginError: null,
    registerError: null,
    registerSuccess: false,
    captchaTime: null, // 新增验证码获取时间
    forgotPasswordCaptchaTime: null, // 忘记密码验证码获取时间
  }),

  getters: {
    loggedIn: (state) => state.isLoggedIn,
  },

  actions: {
    /**
     * 初始化用户状态
     * 从localStorage中恢复用户登录状态
     */
    initUserState() {
      try {
        // 从 localStorage 和 sessionStorage 中获取 token 和用户信息
        const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token');
        const userStr = localStorage.getItem('mall_user');

        if (token && userStr) {
          const user = JSON.parse(userStr);

          // 设置状态
          this.token = token;
          this.user = user;
          this.isLoggedIn = true;

          // 注意：使用 API 模块时不需要手动设置请求头，request.js 会自动处理
        }
      } catch (error) {
        console.error('初始化用户状态失败:', error);
        // 清除可能损坏的数据
        localStorage.removeItem('mall_token');
        localStorage.removeItem('mall_user');
      }
    },

    /**
     * 用户登录
     * @param {Object} loginForm - 登录表单数据
     */
    async login(loginForm) {
      console.log('Store: 开始处理登录请求', loginForm.username);

      try {
        this.loginError = null;

        // 检查是否是手机号
        const phoneRegex = /^1[3-9]\d{9}$/;
        const isPhone = phoneRegex.test(loginForm.username);

        console.log('登录表单数据:', loginForm, '是否是手机号:', isPhone);

        // 构建登录数据
        const loginData = {
          username: loginForm.username,
          password: loginForm.password
        };

        // 如果是手机号，添加标记
        if (isPhone) {
          console.log('Store: 检测到手机号登录');
          loginData.isPhone = true;
        }

        console.log('Store: 发送登录请求参数:', {
          ...loginData,
          password: loginData.password ? '******' : null
        });

        // 发送登录请求
        const response = await mallApi.user.auth.login(loginData);
        console.log('Store: 登录响应:', response);

        if (response.code === 200) {
          const { token, user } = response.data;

          // 根据是否记住我，保存到不同的存储位置
          if (loginForm.remember && process.client) {
            // 记住我选项，保存到localStorage（持久存储）
            localStorage.setItem('mall_user_token', token);
            localStorage.setItem('mall_user', JSON.stringify(user));
            console.log('Store: 用户选择了记住我，token保存到localStorage');
          } else if (process.client) {
            // 未选择记住我，保存到sessionStorage（会话存储）
            sessionStorage.setItem('mall_user_token', token);
            localStorage.setItem('mall_user', JSON.stringify(user)); // 用户信息仍然保存到localStorage
            console.log('Store: 用户未选择记住我，token保存到sessionStorage');
          }

          // 更新状态
          this.token = token;
          this.user = user;
          this.isLoggedIn = true;

          // 设置axios默认请求头
          // 使用 Nuxt 的 $axios 实例
          const { $axios } = useNuxtApp();
          if ($axios) {
            $axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          }

          console.log('Store: 登录成功，用户信息已保存');
          return { success: true };
        } else {
          console.log('Store: 登录失败:', response.message);
          this.loginError = response.message || '登录失败';
          return { success: false, message: response.message };
        }
      } catch (error) {
        console.error('Store: 登录请求出错:', error);
        const message = error.response?.data?.message || '登录失败，请稍后再试';
        this.loginError = message;
        return { success: false, message };
      }
    },

    /**
     * 用户注册
     * @param {Object} registerData - 注册数据
     */
    async register(registerData) {
      console.log('开始处理注册请求:', registerData.username);

      try {
        this.registerError = null;
        this.registerSuccess = false;

        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(registerData.phone)) {
          this.registerError = '手机号格式不正确';
          return { success: false, message: '手机号格式不正确' };
        }

        // 验证密码长度
        if (registerData.password.length < 6) {
          this.registerError = '密码长度不能少于6位';
          return { success: false, message: '密码长度不能少于6位' };
        }

        // 发送注册请求
        const response = await mallApi.user.auth.register(registerData);
        console.log('Store: 注册响应:', response);

        if (response.code === 200) {
          this.registerSuccess = true;
          return { success: true, message: '注册成功' };
        } else {
          this.registerError = response.message || '注册失败';
          return { success: false, message: response.message };
        }
      } catch (error) {
        console.error('注册请求失败:', error);
        const message = error.response?.data?.message || '注册请求失败，请稍后重试';
        this.registerError = message;
        return { success: false, message };
      }
    },

    /**
     * 获取验证码
     * @param {String} phone - 手机号
     * @returns {Promise<Object>} - 请求结果
     */
    async getCaptcha(phone) {
      try {
        console.log('获取验证码，手机号:', phone);

        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phone)) {
          return { success: false, message: '手机号格式不正确' };
        }

        // 记录验证码获取时间
        const captchaTime = Date.now();

        // 发送请求
        const data = await mallApi.user.auth.getCaptcha(phone);
        console.log('验证码响应:', data);

        if (data.code === 200) {
          // 保存验证码获取时间
          this.captchaTime = captchaTime;
          return {
            success: true,
            captcha: data.data.captcha,
            captchaTime: captchaTime
          };
        } else {
          return { success: false, message: data.message || '获取验证码失败' };
        }
      } catch (error) {
        console.error('获取验证码失败:', error);
        return { success: false, message: '获取验证码失败，请稍后再试' };
      }
    },

    /**
     * 发送短信验证码（通用方法）
     * @param {String} phone - 手机号
     * @param {String} type - 验证码类型，默认为'login'
     * @returns {Promise<Object>} - 请求结果
     */
    async sendSmsVerificationCode(phone, type = 'login') {
      try {
        console.log('发送短信验证码，手机号:', phone, '类型:', type);

        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phone)) {
          return { success: false, message: '手机号格式不正确' };
        }

        // 调用发送短信验证码接口
        const data = await mallApi.user.auth.sendSmsVerificationCode(phone, type);
        console.log('短信验证码响应:', data);

        if (data.code === 200) {
          // 处理新的返回结构：真正的消息在 data.data.message 中
          const messageText = (data.data && data.data.message)
            ? data.data.message
            : data.message || '验证码发送成功';

          return {
            success: true,
            message: messageText
          };
        } else {
          const errorMessage = data.message || '发送验证码失败';

          return { success: false, message: errorMessage };
        }
      } catch (error) {
        console.error('发送短信验证码失败:', error);
        return { success: false, message: '发送验证码失败，请稍后再试' };
      }
    },

    /**
     * 发送短信验证码（用于登录）
     * @param {String} phone - 手机号
     * @returns {Promise<Object>} - 请求结果
     */
    async sendSmsCode(phone) {
      return await this.sendSmsVerificationCode(phone, 'login');
    },

    /**
     * 发送注册短信验证码
     * @param {String} phone - 手机号
     * @returns {Promise<Object>} - 请求结果
     */
    async sendRegisterSmsCode(phone) {
      return await this.sendSmsVerificationCode(phone, 'register');
    },

    /**
     * 短信验证码登录
     * @param {Object} loginData - 登录数据，包含手机号和验证码
     * @returns {Promise<Object>} - 请求结果
     */
    async smsLogin(loginData) {
      try {
        console.log('Store: 开始处理短信登录请求', loginData.phone);

        this.loginError = null;

        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(loginData.phone)) {
          return { success: false, message: '手机号格式不正确' };
        }

        // 验证验证码
        if (!loginData.smsCode) {
          return { success: false, message: '验证码不能为空' };
        }

        console.log('Store: 发送短信登录请求参数:', {
          phone: loginData.phone,
          smsCode: '******'
        });

        // 发送短信登录请求
        const response = await mallApi.user.auth.smsLogin({
          phone: loginData.phone,
          smsCode: loginData.smsCode
        });

        console.log('Store: 短信登录响应:', response);

        if (response.code === 200 && response.data) {
          const { token, user } = response.data;

          // 设置用户状态
          this.token = token;
          this.user = user;
          this.isLoggedIn = true;
          this.loginError = null;

          // 保存用户信息到localStorage
          if (process.client) {
            localStorage.setItem('mall_user', JSON.stringify(user));
            console.log('Store: 用户信息已保存到localStorage');
          }

          console.log('Store: 短信登录成功，用户状态已更新');
          return {
            success: true,
            message: response.message || '登录成功',
            user: user,
            token: token
          };
        } else {
          const errorMessage = response.message || '短信登录失败';
          this.loginError = errorMessage;
          console.log('Store: 短信登录失败:', errorMessage);
          return { success: false, message: errorMessage };
        }
      } catch (error) {
        console.error('Store: 短信登录过程中出错:', error);
        const errorMessage = error.response?.data?.message || error.message || '短信登录失败，请稍后再试';
        this.loginError = errorMessage;
        return { success: false, message: errorMessage };
      }
    },

    /**
     * 用户登出
     */
    logout() {
      try {
        // 清除存储
        if (process.client) {
          // 清除新版存储
          localStorage.removeItem('mall_user_token');
          sessionStorage.removeItem('mall_user_token');
          localStorage.removeItem('mall_user');

          // 为兼容性考虑，也清除旧版存储
          localStorage.removeItem('mall_token');
          //删除浏览历史
          localStorage.removeItem('mall_history_items');
        }

        // 重置状态
        this.token = null;
        this.user = null;
        this.isLoggedIn = false;

        // 注意：使用 API 模块时不需要手动管理请求头，request.js 会自动处理

        return { success: true };
      } catch (error) {
        console.error('登出失败:', error);
        return { success: false, message: '登出失败' };
      }
    },

    /**
     * 更新用户信息
     * @param {Object} userData - 用户数据
     */
    async updateUserInfo(userData) {
      try {
        console.log('更新用户信息:', userData);

        // 发送更新用户信息请求
        const response = await mallApi.user.profile.update(userData);
        console.log('Store: 更新用户信息响应:', response);

        if (response.code === 200) {
          // 更新本地存储的用户信息
          if (process.client) {
            const userStr = localStorage.getItem('mall_user');
            if (userStr) {
              const user = JSON.parse(userStr);
              const updatedUser = { ...user, ...userData };
              localStorage.setItem('mall_user', JSON.stringify(updatedUser));

              // 更新状态
              this.user = updatedUser;
            }
          }

          return { success: true, message: '用户信息更新成功' };
        } else {
          return { success: false, message: response.message || '更新失败' };
        }
      } catch (error) {
        const message = error.response?.data?.message || '更新用户信息失败，请稍后重试';
        return { success: false, message };
      }
    },

    /**
     * 恢复用户会话
     * 从 localStorage 或 sessionStorage 中恢复用户登录状态
     */
    async restoreSession() {
      try {
        console.log('开始恢复用户会话...');

        // 如果已经登录，直接返回
        if (this.isLoggedIn && this.token && this.user) {
          console.log('已经处于登录状态，无需恢复会话');
          return { success: true };
        }

        // 尝试从 localStorage 和 sessionStorage 中获取 token
        let token = null;
        if (process.client) {
          // 优先从 sessionStorage 和 localStorage 中获取新版 token
          token = sessionStorage.getItem('mall_user_token') || localStorage.getItem('mall_user_token');

          // 兼容旧版存储方式
          if (!token) {
            token = localStorage.getItem('mall_token');
            if (token) {
              console.log('从旧版存储中获取 token 成功，建议重新登录以更新存储格式');
              // 将旧版 token 迁移到新版存储中
              localStorage.setItem('mall_user_token', token);
              // 清除旧版存储
              localStorage.removeItem('mall_token');
            }
          }

          console.log(token ? 'token 获取成功' : '未找到有效的 token');
        }

        if (!token) {
          console.log('未找到有效的 token，无法恢复会话');
          this._clearLoginState();
          return { success: false, message: '未登录' };
        }

        // 从本地存储中获取用户信息
        let user = null;
        if (process.client) {
          const userStr = localStorage.getItem('mall_user');
          if (userStr) {
            try {
              user = JSON.parse(userStr);
              console.log('从本地存储获取并解析用户信息成功');
            } catch (parseError) {
              console.error('用户信息解析失败:', parseError);
              this._clearLoginState();
              return { success: false, message: '用户信息解析失败' };
            }
          }
        }

        // 如果没有获取到用户信息，则清除登录状态
        if (!user) {
          console.log('本地没有用户信息，清除登录状态');
          this._clearLoginState();
          return { success: false, message: '未找到用户信息' };
        }

        // 设置登录状态
        this.token = token;
        this.user = user;
        this.isLoggedIn = true;

        console.log('会话恢复成功，用户已登录');
        return { success: true };
      } catch (error) {
        console.error('恢复会话失败:', error);
        this._clearLoginState();
        return { success: false, message: '会话恢复失败' };
      }
    },

    /**
     * 清除登录状态
     * @private
     */
    _clearLoginState() {
      // 重置状态
      this.token = null;
      this.user = null;
      this.isLoggedIn = false;

      // 清除存储
      if (process.client) {
        // 清除新版存储
        localStorage.removeItem('mall_user_token');
        sessionStorage.removeItem('mall_user_token');
        localStorage.removeItem('mall_user');

        // 为兼容性考虑，也清除旧版存储
        localStorage.removeItem('mall_token');

        // 注意：使用 API 模块时不需要手动管理请求头，request.js 会自动处理
      }
    },

    /**
     * 发送忘记密码短信验证码
     * @param {String} phone - 手机号
     * @returns {Promise<Object>} - 请求结果
     */
    async sendForgotPasswordSmsCode(phone) {
      return await this.sendSmsVerificationCode(phone, 'forget_password');
    },

    /**
     * 获取忘记密码验证码（保留兼容性）
     * @param {String} phone - 手机号
     * @returns {Promise<Object>} - 请求结果
     */
    async getForgotPasswordCaptcha(phone) {
      // 直接调用新的短信验证码方法
      return await this.sendForgotPasswordSmsCode(phone);
    },

    /**
     * 发送手机绑定短信验证码
     * @param {String} phone - 手机号
     * @param {String} type - 验证码类型，默认为'change_phone'
     * @returns {Promise<Object>} - 请求结果
     */
    async sendPhoneBindSmsCode(phone, type = 'change_phone') {
      return await this.sendSmsVerificationCode(phone, type);
    },

    /**
     * 验证忘记密码验证码
     * @param {Object} verifyData - 验证数据，包含手机号和验证码
     * @returns {Promise<Object>} - 请求结果
     */
    async verifyForgotPasswordCaptcha(verifyData) {
      try {
        console.log('验证忘记密码验证码:', verifyData);

        // 验证手机号格式
        const phoneRegex = /^1[3-9]\\d{9}$/;
        if (!phoneRegex.test(verifyData.phone)) {
          return { success: false, message: '手机号格式不正确' };
        }

        // 验证验证码是否为空
        if (!verifyData.captcha) {
          return { success: false, message: '验证码不能为空' };
        }

        // 发送请求验证验证码
        const data = await mallApi.user.auth.verifyForgotPasswordCaptcha({
          phone: verifyData.phone,
          captcha: verifyData.captcha
        });

        console.log('验证忘记密码验证码响应:', data);

        if (data.code === 200) {
          return { success: true, message: '验证码验证成功' };
        } else {
          return { success: false, message: data.message || '验证码验证失败' };
        }
      } catch (error) {
        console.error('验证忘记密码验证码失败:', error);
        return { success: false, message: '验证失败，请稍后再试' };
      }
    },

    /**
     * 重置密码
     * @param {Object} resetData - 重置密码数据，包含手机号、验证码和新密码
     * @returns {Promise<Object>} - 请求结果
     */
    async resetPassword(resetData) {
      try {
        console.log('重置密码:', resetData);

        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(resetData.phone)) {
          return { success: false, message: '手机号格式不正确' };
        }

        // 验证验证码是否为空
        if (!resetData.captcha) {
          return { success: false, message: '验证码不能为空' };
        }

        // 验证密码是否为空
        if (!resetData.newPassword) {
          return { success: false, message: '新密码不能为空' };
        }

        // 发送重置密码请求
        const data = await mallApi.user.auth.resetPassword({
          username: resetData.phone,
          captcha: resetData.captcha,
          newPassword: resetData.newPassword
        });

        console.log('重置密码响应:', data);

        if (data.code === 200) {
          return { success: true, message: '密码重置成功' };
        } else {
          return { success: false, message: data.message || '密码重置失败' };
        }
      } catch (error) {
        console.error('重置密码失败:', error);
        return { success: false, message: '重置密码失败，请稍后再试' };
      }
    },

    /**
     * 获取用户地址列表
     * @returns {Promise<Object>} - 请求结果，包含地址列表数据
     */
    async getUserAddresses() {
      try {
        console.log('获取用户地址列表');

        // 检查用户是否已登录
        if (!this.isLoggedIn || !this.token) {
          return { success: false, message: '用户未登录', data: [] };
        }

        // 发送获取地址列表请求
        const response = await addressApi.address.getList();
        console.log('获取用户地址列表响应:', response);

        if (response.code === 200) {
          return {
            success: true,
            message: '获取地址列表成功',
            data: response.data || []
          };
        } else {
          return {
            success: false,
            message: response.message || '获取地址列表失败',
            data: []
          };
        }
      } catch (error) {
        console.error('获取用户地址列表失败:', error);
        return {
          success: false,
          message: '获取地址列表失败，请稍后再试',
          data: []
        };
      }
    },

    /**
     * 更新用户实名认证信息
     * @param {Object} authInfo - 实名认证信息
     * @param {Number|Boolean} authInfo.isVerified - 认证状态
     * @param {String} authInfo.realName - 真实姓名
     * @param {String} authInfo.idCardNumber - 身份证号码
     */
    updateUserRealnameAuthInfo(authInfo) {
      console.log('更新用户实名认证信息:', authInfo);

      if (!this.user) {
        console.warn('用户信息不存在，无法更新实名认证信息');
        return;
      }

      // 更新内存中的用户信息
      if (authInfo.isVerified !== undefined) {
        this.user.isVerified = authInfo.isVerified;
      }

      if (authInfo.realName) {
        this.user.realName = authInfo.realName;
      }

      if (authInfo.idCardNumber) {
        this.user.idCardNumber = authInfo.idCardNumber;
      }

      // 尝试更新本地存储
      try {
        if (typeof localStorage !== 'undefined') {
          const userStr = localStorage.getItem('mall_user');
          if (userStr) {
            const userData = JSON.parse(userStr);

            // 更新实名认证信息
            if (authInfo.isVerified !== undefined) {
              userData.isVerified = authInfo.isVerified;
            }

            if (authInfo.realName) {
              userData.realName = authInfo.realName;
            }

            if (authInfo.idCardNumber) {
              userData.idCardNumber = authInfo.idCardNumber;
            }

            // 保存回本地存储
            localStorage.setItem('mall_user', JSON.stringify(userData));
            console.log('本地用户实名认证信息已更新');
          }
        }
      } catch (error) {
        console.error('更新本地存储实名认证信息失败:', error);
      }
    },

    /**
     * 更新用户邮箱信息
     * @param {Object} emailInfo - 邮箱信息
     * @param {String} emailInfo.email - 邮箱地址
     */
    /**
     * 直接更新用户信息
     * @param {Object} userData - 完整的用户数据
     */
    updateUser(userData) {
      console.log('直接更新用户信息:', userData);

      if (!userData) {
        console.warn('更新数据为空，无法更新用户信息');
        return;
      }

      // 更新内存中的用户信息
      this.user = { ...this.user, ...userData };

      // 尝试更新本地存储
      try {
        if (process.client && localStorage) {
          const userStr = localStorage.getItem('mall_user');
          if (userStr) {
            const storedUser = JSON.parse(userStr);
            // 合并用户数据
            const updatedUser = { ...storedUser, ...userData };
            // 保存回本地存储
            localStorage.setItem('mall_user', JSON.stringify(updatedUser));
            console.log('本地用户信息已更新，包含微信绑定状态');
          }
        }
      } catch (error) {
        console.error('更新本地存储用户信息失败:', error);
      }
    },

    updateUserEmail(emailInfo) {
      console.log('更新用户邮箱信息:', emailInfo);

      if (!this.user) {
        console.warn('用户信息不存在，无法更新邮箱信息');
        return;
      }

      // 更新内存中的用户邮箱信息
      if (emailInfo.email) {
        this.user.email = emailInfo.email;
      }

      // 尝试更新本地存储
      try {
        if (typeof localStorage !== 'undefined') {
          const userStr = localStorage.getItem('mall_user');
          if (userStr) {
            const userData = JSON.parse(userStr);

            // 更新邮箱信息
            if (emailInfo.email) {
              userData.email = emailInfo.email;
            }

            // 保存回本地存储
            localStorage.setItem('mall_user', JSON.stringify(userData));
            console.log('本地用户邮箱信息已更新');
          }
        }
      } catch (error) {
        console.error('更新本地存储邮箱信息失败:', error);
      }
    },

    /**
     * 更新用户安全问题信息
     * @param {Object} securityInfo - 安全问题信息
     * @param {Boolean} securityInfo.hasSecurityQuestions - 是否设置了安全问题
     * @param {Array} securityInfo.securityQuestions - 安全问题列表
     */
    updateUserSecurityQuestion(securityInfo) {
      console.log('更新用户安全问题信息:', securityInfo);

      if (!this.user) {
        console.warn('用户信息不存在，无法更新安全问题信息');
        return;
      }

      // 更新内存中的用户安全问题信息
      if (securityInfo.hasSecurityQuestions !== undefined) {
        this.user.hasSecurityQuestions = securityInfo.hasSecurityQuestions;
      }

      if (securityInfo.securityQuestions) {
        this.user.securityQuestions = securityInfo.securityQuestions;
      }

      // 尝试更新本地存储
      try {
        if (typeof localStorage !== 'undefined') {
          const userStr = localStorage.getItem('mall_user');
          if (userStr) {
            const userData = JSON.parse(userStr);

            // 更新安全问题信息
            if (securityInfo.hasSecurityQuestions !== undefined) {
              userData.hasSecurityQuestions = securityInfo.hasSecurityQuestions;
            }

            if (securityInfo.securityQuestions) {
              userData.securityQuestions = securityInfo.securityQuestions;
            }

            // 保存回本地存储
            localStorage.setItem('mall_user', JSON.stringify(userData));
            console.log('本地用户安全问题信息已更新');
          }
        }
      } catch (error) {
        console.error('更新本地存储安全问题信息失败:', error);
      }
    },

    /**
     * 获取订单详情
     * @param {String|Number} orderId - 订单ID
     * @returns {Promise<Object>} - 请求结果，包含订单详情数据
     */
    async getOrderDetail(orderId) {
      try {
        console.log('获取订单详情:', orderId);

        // 检查用户是否已登录
        if (!this.isLoggedIn || !this.token) {
          return { success: false, message: '用户未登录', data: null };
        }

        // 检查订单ID是否有效
        if (!orderId) {
          return { success: false, message: '订单ID不能为空', data: null };
        }

        // 发送获取订单详情请求
        const response = await mallApi.order.getOrderDetail(orderId);
        console.log('获取订单详情响应:', response);

        if (response.code === 200) {
          console.log('API响应成功，返回数据:', response.data);
          return {
            success: true,
            message: '获取订单详情成功',
            data: response.data || null
          };
        } else {
          console.log('API响应失败，错误信息:', response.message);
          return {
            success: false,
            message: response.message || '获取订单详情失败',
            data: null
          };
        }
      } catch (error) {
        console.error('获取订单详情失败:', error);
        return {
          success: false,
          message: '获取订单详情失败，请稍后再试',
          data: null
        };
      }
    },
    /**
     * 验证用户名并获取用户信息
     * @param {String} username - 用户名
     * @returns {Promise<Object>} - 请求结果，包含用户信息和安全问题
     */
    async verifyUsername(username) {
      try {
        console.log('验证用户名:', username);

        // 验证用户名是否为空
        if (!username || username.trim() === '') {
          return { success: false, message: '用户名不能为空' };
        }

        // 使用API方法获取用户安全问题和邮箱
        const response = await mallApi.user.auth.getSecurityQuestionsByUsername(username);
        console.log('获取安全问题响应:', response);

        if (response.code === 200) {
          return {
            success: true,
            message: '用户验证成功',
            data: {
              email: response.data.email || '',
              securityQuestions: response.data.questions || []
            }
          };
        } else {
          return { success: false, message: response.message || '用户名验证失败' };
        }
      } catch (error) {
        console.error('验证用户名失败:', error);
        return { success: false, message: '验证用户名失败，请稍后再试' };
      }
    },

    /**
     * 发送重置密码邮件
     * @param {Object} data - 请求数据，包含用户名
     * @returns {Promise<Object>} - 请求结果
     */
    async sendResetPasswordEmail(data) {
      try {
        console.log('发送重置密码邮件:', data);

        // 验证用户名是否为空
        if (!data.username || data.username.trim() === '') {
          return { success: false, message: '用户名不能为空' };
        }

        // 发送请求发送重置密码邮件
        const response = await mallApi.user.auth.sendResetPasswordEmail({ username: data.username });
        console.log('发送重置密码邮件响应:', response);

        if (response.code === 200) {
          return {
            success: true,
            message: '重置密码邮件发送成功'
          };
        } else {
          return { success: false, message: response.message || '发送重置密码邮件失败' };
        }
      } catch (error) {
        console.error('发送重置密码邮件失败:', error);
        return { success: false, message: '发送重置密码邮件失败，请稍后再试' };
      }
    },

    /**
     * 获取用户安全问题
     * @param {String} username - 用户名
     * @returns {Promise<Object>} - 请求结果，包含安全问题列表
     */
    async getSecurityQuestions(username) {
      try {
        console.log('获取用户安全问题，用户名:', username);

        // 验证用户名是否为空
        if (!username || username.trim() === '') {
          return { success: false, message: '用户名不能为空' };
        }

        // 发送请求获取安全问题
        const response = await mallApi.user.profile.getSecurityQuestions({ username });
        console.log('获取安全问题响应:', response);

        if (response.code === 200) {
          return {
            success: true,
            message: '获取安全问题成功',
            data: response.data.questions || []
          };
        } else {
          return { success: false, message: response.message || '获取安全问题失败' };
        }
      } catch (error) {
        console.error('获取安全问题失败:', error);
        return { success: false, message: '获取安全问题失败，请稍后再试' };
      }
    },

    /**
     * 验证安全问题答案
     * @param {Object} verifyData - 验证数据，包含用户名和安全问题答案
     * @returns {Promise<Object>} - 请求结果
     */
    async verifySecurityQuestion(verifyData) {
      try {
        console.log('验证安全问题答案:', verifyData);

        // 验证用户名是否为空
        if (!verifyData.username || verifyData.username.trim() === '') {
          return { success: false, message: '用户名不能为空' };
        }

        // 验证安全问题答案是否为空
        if (!verifyData.securityAnswer || verifyData.securityAnswer.trim() === '') {
          return { success: false, message: '安全问题答案不能为空' };
        }

        // 发送请求验证安全问题答案
        const response = await mallApi.user.auth.verifySecurityQuestion({
          username: verifyData.username,
          securityAnswer: verifyData.securityAnswer,
          questionId: verifyData.questionId
        });

        console.log('验证安全问题答案响应:', response);

        if (response.code === 200) {
          return { success: true, message: '安全问题验证成功' };
        } else {
          return { success: false, message: response.message || '安全问题验证失败' };
        }
      } catch (error) {
        console.error('验证安全问题答案失败:', error);
        return { success: false, message: '验证失败，请稍后再试' };
      }
    },
    
    /**
     * 处理微信扫码登录
     * @param {Object} wechatData - 微信登录数据，包含 token 和用户信息
     * @returns {Promise<Object>} - 处理结果
     */
    async handleWechatLogin(wechatData) {
      try {
        console.log('处理微信扫码登录:', wechatData);
        
        if (!wechatData || !wechatData.token) {
          return { success: false, message: '缺少必要的登录信息' };
        }
        
        // 设置 token 和登录状态
        this.token = wechatData.token;
        this.isLoggedIn = true;
        
        // 如果有用户信息，则设置用户信息
        if (wechatData.userInfo) {
          this.user = wechatData.userInfo;
          
          // 将用户信息保存到 localStorage
          if (process.client) {
            localStorage.setItem('mall_user', JSON.stringify(wechatData.userInfo));
          }
        } else {
          // 如果没有用户信息，尝试获取用户信息
          try {
            const userResponse = await mallApi.user.getUserInfo();
            if (userResponse.code === 200 && userResponse.data) {
              this.user = userResponse.data;
              
              if (process.client) {
                localStorage.setItem('mall_user', JSON.stringify(userResponse.data));
              }
            }
          } catch (userError) {
            console.error('获取微信用户信息失败:', userError);
          }
        }
        
        // 将 token 保存到 localStorage
        if (process.client) {
          localStorage.setItem('mall_user_token', wechatData.token);
          console.log('微信登录 token 已保存到 localStorage:', wechatData.token);
        }
        
        return { success: true, message: '微信登录成功' };
      } catch (error) {
        console.error('处理微信登录失败:', error);
        return { success: false, message: '微信登录失败，请稍后再试' };
      }
    }
  },
}, {
  // 持久化配置
  persist: true
});
