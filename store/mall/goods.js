import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import mallApi from '@/api/mall'

/**
 * 商城商品状态管理
 */
export const useMallGoodsStore = defineStore('mallGoods', () => {
  // 商品列表
  const goodsList = ref([])
  
  // 搜索参数
  const searchParams = ref({
    categoryId: '',
    brandId:null,
    keyword: '',
    minPrice:null,
    maxPrice:null,
    tags:null,
    sortBy: '',
    sortOrder: 'desc',
    page: 1,
    pageSize: 20,
  })
  
  // 分页信息
  const pagination = ref({
    total: 0,
    current: 1,
    pageSize: 20
  })
  
  // 加载状态
  const loading = ref(false)
  
  // 计算属性
  // 是否有商品
  const hasGoods = computed(() => goodsList.value.length > 0)
  
  /**
   * 查询商品列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  const queryGoods = async (params = {}) => {
    try {
      loading.value = true
      
      // 合并查询参数
      const queryParams = { ...searchParams.value, ...params }
      
      // 更新搜索参数
      searchParams.value = queryParams
      
      const response = await mallApi.goods.queryGoods(queryParams)
      
      if (response && response.code === 200) {
        goodsList.value = response.data.items || []

        return {
          success: true,
          data: response.data
        }
      }
      
      return {
        success: false,
        message: response?.message || '查询商品失败'
      }
    } catch (error) {
      console.error('查询商品出错:', error)
      return {
        success: false,
        message: '查询商品出错'
      }
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 获取商品详情
   * @param {string} id - 商品ID
   * @returns {Promise} - 返回请求结果
   */
  const getGoodsDetail = async (id) => {
    try {
      loading.value = true
      
      const response = await mallApi.goods.getGoodsDetail(id)
      
      if (response && response.code === 200) {
        return {
          success: true,
          data: response.data
        }
      }
      
      return {
        success: false,
        message: response?.message || '获取商品详情失败'
      }
    } catch (error) {
      console.error('获取商品详情出错:', error)
      return {
        success: false,
        message: '获取商品详情出错'
      }
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 重置搜索参数
   */
  const resetSearchParams = () => {
    searchParams.value = {
      keyword: '',
      page: 1,
      pageSize: 20,
      categoryId: '',
      sortField: '',
      sortOrder: 'desc'
    }
  }
  
  /**
   * 清空商品列表
   */
  const clearGoodsList = () => {
    goodsList.value = []
    pagination.value = {
      total: 0,
      current: 1,
      pageSize: 20
    }
  }
  
  return {
    // 状态
    goodsList,
    searchParams,
    pagination,
    loading,
    
    // 计算属性
    hasGoods,
    
    // 方法
    queryGoods,
    getGoodsDetail,
    resetSearchParams,
    clearGoodsList
  }
})
