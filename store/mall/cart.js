import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import mallApi from '@/api/mall'

/**
 * 商城购物车状态管理
 */
export const useMallCartStore = defineStore('mallCart', () => {
  // 购物车商品列表
  const cartItems = ref([])
  // 购物车总运费
  const totalFreight = ref(0)
  // 已选商品运费
  const selectedFreight = ref(0)
  
  // 计算属性
  // 购物车商品总数
  const totalCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
  })
  
  // 购物车商品总价
  const totalPrice = computed(() => {
    return cartItems.value.reduce((total, item) => {
      return total + (parseFloat(item.price) * item.quantity)
    }, 0).toFixed(2)
  })
  
  // 是否全选
  const isAllSelected = computed(() => {
    if (cartItems.value.length === 0) return false
    return cartItems.value.every(item => item.selected)
  })
  
  // 已选商品数量
  const selectedCount = computed(() => {
    return cartItems.value.filter(item => item.selected).reduce((total, item) => {
      return total + item.quantity
    }, 0)
  })
  
  // 已选商品总价
  const selectedTotalPrice = computed(() => {
    return cartItems.value
      .filter(item => item.selected)
      .reduce((total, item) => {
        return total + (parseFloat(item.price) * item.quantity)
      }, 0)
      .toFixed(2)
  })
  
  // 已选商品运费合计 (兼容旧版本)
  const selectedTotalFreight = computed(() => {
    return selectedFreight.value.toFixed(2)
  })
  
  /**
   * 添加商品到购物车
   * @param {Object} product 商品信息
   * @param {Number} quantity 数量
   * @param {Object} selectedSpecs 已选规格
   * @returns {Promise<{success: boolean, message: string}>}
   */
  async function addToCart(product, quantity = 1, selectedSpecs = {}) {
    if(quantity > product.stock){
      return { success: false, message: '库存不足' }
    }
    try {
      let message = '成功加入购物车'
      console.log('添加商品到购物车:', product.name, '数量:', quantity, '规格:', selectedSpecs)
      
      // 生成规格描述文本
      const specText = Object.entries(selectedSpecs)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ')
      
      // 生成唯一标识（商品ID + 规格）
      const itemKey = `${product.id}-${specText}`
      
      // 检查购物车中是否已存在相同商品和规格
      const existingItemIndex = cartItems.value.findIndex(item => item.itemKey === itemKey)
      
      if (existingItemIndex > -1) {
        // 更新库存
        cartItems.value[existingItemIndex].stock = product.stock
        // 如果已存在，增加数量
        let chachStock = cartItems.value[existingItemIndex].quantity + quantity
        if(chachStock > cartItems.value[existingItemIndex].stock){
          cartItems.value[existingItemIndex].quantity = cartItems.value[existingItemIndex].stock
          message = '库存不足,已自动调整为最大库存数量'
        }else{
          cartItems.value[existingItemIndex].quantity += quantity
        }
      } else {
        // 检查购物车中不同商品的数量是否已达到上限(10条)
        if (cartItems.value.length >= 10) {
          return { success: false, message: '购物车已满，请先清理购物车后再添加新商品' }
        }
        
        // 如果不存在，添加新商品
        if(Array.isArray(product.images)){
          product.imageUrl = product.images[0]
        }
        const newItem = {
          id: product.id,
          spuId: product.id,
          skuId: product.skuId,
          itemKey,
          name: product.name,
          price: product.price,
          originalPrice: product.originalPrice,
          image: product.imageUrl,
          quantity,
          selected: true,
          stock: product.stock || 999,
          specs: selectedSpecs,
          specText,
          freightInfo: {},
          totalFreight: 0
        }
        
        cartItems.value.push(newItem)
      }
      
      // 保存到本地存储
      saveCartToLocalStorage()
      
      return { success: true, message }
    } catch (error) {
      console.error('添加商品到购物车失败:', error)
      return { success: false, message: '添加商品失败，请稍后再试' }
    }
  }
  
  /**
   * 从购物车移除商品
   * @param {String} itemKey 商品唯一标识
   * @returns {Promise<{success: boolean, message: string}>}
   */
  async function removeFromCart(itemKey) {
    try {
      const index = cartItems.value.findIndex(item => item.itemKey === itemKey)
      
      if (index > -1) {
        cartItems.value.splice(index, 1)
        saveCartToLocalStorage()
        return { success: true, message: '成功移除商品' }
      }
      
      return { success: false, message: '商品不存在' }
    } catch (error) {
      console.error('从购物车移除商品失败:', error)
      return { success: false, message: '移除商品失败，请稍后再试' }
    }
  }
  
  /**
   * 更新购物车商品数量
   * @param {String} itemKey 商品唯一标识
   * @param {Number} quantity 新数量
   * @returns {Promise<{success: boolean, message: string}>}
   */
  async function updateQuantity(itemKey, quantity) {
    try {
      // 确保 quantity 是数字类型
      const numericQuantity = Number(quantity)
      
      // 检查是否为有效数字
      if (isNaN(numericQuantity)) {
        console.error('无效的数量值:', quantity)
        return { success: false, message: '无效的数量值' }
      }
      
      const index = cartItems.value.findIndex(item => item.itemKey === itemKey)
      
      if (index > -1) {
        const item = cartItems.value[index]
        
        // 检查是否超出库存
        if (numericQuantity > item.stock) {
          // 直接设置为库存上限，并保存更改
          const stockLimit = Number(item.stock)
          item.quantity = stockLimit
          saveCartToLocalStorage()
          return { 
            success: true, 
            message: `已自动调整为最大库存数量（${item.stock}）` 
          }
        }
        
        // 确保数量在有效范围内
        const newQuantity = Math.max(1, numericQuantity)
        
        if (newQuantity !== item.quantity) {
          item.quantity = newQuantity
          saveCartToLocalStorage()
        }
        
        return { success: true, message: '成功更新商品数量' }
      }
      
      return { success: false, message: '商品不存在' }
    } catch (error) {
      console.error('更新购物车商品数量失败:', error)
      return { success: false, message: '更新商品数量失败，请稍后再试' }
    }
  }
  
  /**
   * 切换商品选中状态
   * @param {String} itemKey 商品唯一标识
   * @returns {Promise<{success: boolean, message: string}>}
   */
  async function toggleItemSelection(itemKey) {
    try {
      const index = cartItems.value.findIndex(item => item.itemKey === itemKey)
      
      if (index > -1) {
        cartItems.value[index].selected = !cartItems.value[index].selected
        saveCartToLocalStorage()
        return { success: true }
      }
      
      return { success: false, message: '商品不存在' }
    } catch (error) {
      console.error('切换商品选中状态失败:', error)
      return { success: false, message: '操作失败，请稍后再试' }
    }
  }
  
  /**
   * 切换全选/全不选
   * @returns {Promise<{success: boolean, message: string}>}
   */
  async function toggleSelectAll() {
    try {
      const newStatus = !isAllSelected.value
      
      cartItems.value.forEach(item => {
        item.selected = newStatus
      })
      
      saveCartToLocalStorage()
      return { success: true }
    } catch (error) {
      console.error('切换全选状态失败:', error)
      return { success: false, message: '操作失败，请稍后再试' }
    }
  }
  
  /**
   * 清空购物车
   * @returns {Promise<{success: boolean, message: string}>}
   */
  async function clearCart() {
    try {
      cartItems.value = []
      saveCartToLocalStorage()
      return { success: true, message: '购物车已清空' }
    } catch (error) {
      console.error('清空购物车失败:', error)
      return { success: false, message: '清空购物车失败，请稍后再试' }
    }
  }
  
  /**
   * 保存购物车到本地存储
   */
  function saveCartToLocalStorage() {
    if (process.client) {
      localStorage.setItem('mall_cart', JSON.stringify(cartItems.value))
    }
  }
  
  /**
   * 从本地存储加载购物车
   */
  function loadCartFromLocalStorage() {
    if (process.client) {
      const savedCart = localStorage.getItem('mall_cart')
      if (savedCart) {
        try {
          cartItems.value = JSON.parse(savedCart)
        } catch (e) {
          console.error('解析购物车数据失败:', e)
          cartItems.value = []
        }
      }
    }
  }
  
  // 初始化时从本地存储加载购物车
  if (process.client) {
    loadCartFromLocalStorage()
  }
  
  return {
    // 状态
    cartItems,
    totalFreight,
    selectedFreight,
    
    // 计算属性
    totalCount,
    totalPrice,
    isAllSelected,
    selectedCount,
    selectedTotalPrice,
    selectedTotalFreight,
    
    // 方法
    addToCart,
    removeFromCart,
    updateQuantity,
    toggleItemSelection,
    toggleSelectAll,
    clearCart,
    loadCartFromLocalStorage,
    saveCartToLocalStorage,
  }
}, {
  // 持久化配置
  persist: true
})
