import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 商城立即购买状态管理
 * 用于存储用户从商品详情页直接购买的商品信息
 */
export const useMallImmediatePurchaseStore = defineStore('mallImmediatePurchase', () => {
  // 立即购买的商品
  const purchaseItem = ref(null)
  
  // 是否是立即购买模式
  const isImmediatePurchase = computed(() => purchaseItem.value !== null)
  
  // 商品总价
  const totalPrice = computed(() => {
    if (!purchaseItem.value) return '0.00'
    return (parseFloat(purchaseItem.value.price) * purchaseItem.value.quantity).toFixed(2)
  })
  
  // 商品数量
  const itemCount = computed(() => {
    if (!purchaseItem.value) return 0
    return purchaseItem.value.quantity
  })
  
  /**
   * 设置立即购买的商品
   * @param {Object} product 商品信息
   * @param {Number} quantity 数量
   * @param {Object} selectedSpecs 已选规格
   */
  function setPurchaseItem(product, quantity = 1, selectedSpecs = {}) {
    try {
      if(product.stock < quantity){
        return { success: false, message: '库存不足' }
      }
      console.log('设置立即购买商品:', product.name, '数量:', quantity, '规格:', selectedSpecs)
      
      // 生成规格描述文本
      const specText = Object.entries(selectedSpecs)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ')
      
      // 生成唯一标识（商品ID + 规格）
      const itemKey = `${product.id}-${specText}`
      
      // 创建购买项
      purchaseItem.value = {
        id: product.id,
        spuId: product.id,
        skuId: product.skuId,
        itemKey,
        name: product.name,
        price: product.price,
        originalPrice: product.originalPrice,
        image: Array.isArray(product.images) ? product.images[0] : product.image,
        quantity,
        selected: true,
        stock: product.stock,
        specs: selectedSpecs,
        specText,
        specifications: specText
      }
      
      console.log('立即购买商品已设置:', purchaseItem.value)
      
      return { success: true, message: '成功设置立即购买商品' }
    } catch (error) {
      console.error('设置立即购买商品失败:', error)
      return { success: false, message: '设置立即购买商品失败' }
    }
  }
  
  /**
   * 清除立即购买的商品
   */
  function clearPurchaseItem() {
    purchaseItem.value = null
  }
  
  /**
   * 获取立即购买的商品列表（为了与购物车接口兼容）
   */
  function getPurchaseItems() {
    return purchaseItem.value ? [purchaseItem.value] : []
  }
  
  return {
    // 状态
    purchaseItem,
    
    // 计算属性
    isImmediatePurchase,
    totalPrice,
    itemCount,
    
    // 方法
    setPurchaseItem,
    clearPurchaseItem,
    getPurchaseItems
  }
}, {
  // 不需要持久化，每次都是新的购买
  persist: false
})
