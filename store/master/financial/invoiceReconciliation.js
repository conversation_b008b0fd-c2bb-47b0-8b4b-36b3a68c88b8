import { defineStore } from 'pinia'
import tax from '@/mock/tax.js'

/**
 * 将树形结构的税收分类数据扁平化
 * @param {Array} data 树形结构数据
 * @returns {Array} 扁平化后的数据
 */
const flattenTaxData = (data) => {
  let result = [];
  
  const flatten = (items, parentPath = []) => {
    if (!items || !items.length) return;
    
    items.forEach(item => {
      // 构建当前节点的完整路径
      const currentPath = [...parentPath, item.label];
      
      // 添加到结果数组，包含完整路径信息
      result.push({
        ...item,
        path: currentPath.join(' / ')
      });
      
      // 递归处理子节点
      if (item.children && item.children.length) {
        flatten(item.children, currentPath);
      }
    });
  };
  
  if (data && data.slide_data) {
    flatten(data.slide_data);
  }
  
  return result;
};

// 预先扁平化税收分类数据
const flattenedTaxData = flattenTaxData(tax);

/**
 * 发票对账状态管理
 */
const useInvoiceReconciliationStore = defineStore('invoiceReconciliation', {
  state: () => ({
    // 当前选中的发票
    selectedInvoice: null,
    // 当前选中的对账状态
    currentStatus: 'all',
    // 对账统计数据
    statistics: {
      all: 0,
      reconciled: 0,
      unreconciled: 0
    },
    // 税收分类数据
    taxClassification: tax,
    // 扁平化的税收分类数据
    flattenedTaxData: flattenedTaxData,
    // 发票对账模拟数据
    mockData: [
      {
        id: "10001",
        invoiceCode: "202505200001",
        invoiceTitle: "北京科技有限公司",
        invoiceTime: "2025-05-20 10:30:25",
        totalAmount: 1299.00,
        isReconciled: true,
        reconciledAmount: 1299.00,
        reconciledTime: "2025-05-21 09:15:30",
        relatedInputInvoices: "20250520001,20250520002",
        outputInvoiceId: "O10001",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10001-1",
            name: "笔记本电脑",
            model: "X15-Pro",
            unit: "台",
            quantity: 1,
            price: 1150.44,
            amount: 1150.44
          },
          {
            id: "G10001-2",
            name: "软件授权",
            model: "Office",
            unit: "套",
            quantity: 1,
            price: 148.56,
            amount: 148.56
          }
        ],
        // 添加关联的进项发票详情
        relatedInputInvoiceDetails: [
          {
            id: "I001",
            invoiceCode: "20250520001",
            invoiceTime: "2025-05-15 10:20:30",
            supplierName: "硬件供应商有限公司",
            reconcileQuantity: 1,
            reconcileAmount: 1150.44,
            availableAmount: 1150.44
          },
          {
            id: "I002",
            invoiceCode: "20250520002",
            invoiceTime: "2025-05-16 14:30:25",
            supplierName: "软件科技有限公司",
            reconcileQuantity: 1,
            reconcileAmount: 148.56,
            availableAmount: 148.56
          }
        ]
      },
      {
        id: "10002",
        invoiceCode: "202505270001",
        invoiceTitle: "上海贸易有限公司",
        invoiceTime: "2025-05-27 14:22:10",
        totalAmount: 2499.50,
        isReconciled: true,
        reconciledAmount: 2499.50,
        reconciledTime: "2025-05-28 10:45:12",
        relatedInputInvoices: "20250527001",
        outputInvoiceId: "O10002",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10002-1",
            name: "服务器",
            model: "S2000",
            unit: "台",
            quantity: 1,
            price: 2212.83,
            amount: 2212.83
          },
          {
            id: "G10002-2",
            name: "网络设备",
            model: "N100",
            unit: "套",
            quantity: 1,
            price: 286.67,
            amount: 286.67
          }
        ],
        // 添加关联的进项发票详情
        relatedInputInvoiceDetails: [
          {
            id: "I003",
            invoiceCode: "20250527001",
            invoiceTime: "2025-05-25 09:30:15",
            supplierName: "网络设备制造商",
            reconcileQuantity: 1,
            reconcileAmount: 2499.50,
            availableAmount: 2499.50
          }
        ]
      },
      {
        id: "10003",
        invoiceCode: "202505260001",
        invoiceTitle: "广州电子科技有限公司",
        invoiceTime: "2025-05-26 09:15:30",
        totalAmount: 899.00,
        isReconciled: false,
        reconciledAmount: 0,
        reconciledTime: "",
        relatedInputInvoices: "",
        outputInvoiceId: "O10003",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10003-1",
            name: "打印机",
            model: "P500",
            unit: "台",
            quantity: 1,
            price: 796.46,
            amount: 796.46
          },
          {
            id: "G10003-2",
            name: "墨盒",
            model: "I-255",
            unit: "个",
            quantity: 2,
            price: 51.27,
            amount: 102.54
          }
        ],
        // 添加关联的进项发票详情
        relatedInputInvoiceDetails: []
      }
    ]
  }),
  
  actions: {
    /**
     * 设置发票对账的选中发票
     * @param {Object} invoice 发票对象
     */
    setSelectedInvoice(invoice) {
      this.selectedInvoice = invoice
    },
    
    /**
     * 设置发票对账的当前状态
     * @param {String} status 状态值
     */
    setStatus(status) {
      this.currentStatus = status
    },
    
    /**
     * 更新发票对账统计数据
     * @param {Object} statistics 统计数据对象
     */
    updateStatistics(statistics) {
      this.statistics = {
        ...this.statistics,
        ...statistics
      }
    },
    
    /**
     * 获取发票勾兑数据
     * @param {Object} params 查询参数
     * @returns {Object} 分页数据结果
     */
    getReconciliationData(params = {}) {
      const { page = 1, limit = 10, ...filters } = params
      
      // 过滤数据
      let filteredData = [...this.mockData]
      
      // 根据商户名称过滤
      if (filters.merchantName) {
        filteredData = filteredData.filter(item => item.merchantName.includes(filters.merchantName))
      }
      
      // 根据发票抬头过滤
      if (filters.invoiceTitle) {
        filteredData = filteredData.filter(item => item.invoiceTitle.includes(filters.invoiceTitle))
      }
      
      // 根据发票代码过滤
      if (filters.invoiceCode) {
        filteredData = filteredData.filter(item => item.invoiceCode.includes(filters.invoiceCode))
      }
      
      // 根据系统订单号过滤
      if (filters.systemOrderNo) {
        filteredData = filteredData.filter(item => item.systemOrderNo.includes(filters.systemOrderNo))
      }
      
      // 根据勾兑状态过滤
      if (filters.isReconciled !== undefined && filters.isReconciled !== '') {
        const isReconciled = filters.isReconciled === true || filters.isReconciled === 'true'
        filteredData = filteredData.filter(item => item.isReconciled === isReconciled)
      }
      
      // 根据勾兑时间范围过滤
      if (filters.startReconciledTime && filters.endReconciledTime) {
        filteredData = filteredData.filter(item => {
          if (!item.reconciledTime) return false
          const itemTime = new Date(item.reconciledTime).getTime()
          return itemTime >= filters.startReconciledTime && itemTime <= filters.endReconciledTime
        })
      }
      
      // 计算统计数据
      const all = this.mockData.length
      const reconciled = this.mockData.filter(item => item.isReconciled).length
      const unreconciled = this.mockData.filter(item => !item.isReconciled).length
      
      // 更新统计数据
      this.updateStatistics({ all, reconciled, unreconciled })
      
      // 分页处理
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedData = filteredData.slice(startIndex, endIndex)
      
      return {
        list: paginatedData,
        total: filteredData.length
      }
    },
    
    /**
     * 勾兑发票
     * @param {Object} reconcileData 勾兑数据
     * @returns {Boolean} 是否勾兑成功
     */
    reconcileInvoice(reconcileData) {
      const { invoiceId, reconciledAmount, inputInvoices } = reconcileData
      
      // 查找对账发票
      const index = this.mockData.findIndex(item => item.id === invoiceId)
      if (index === -1) return false
      
      // 计算新的勾兑金额（原有勾兑金额 + 本次勾兑金额）
      const originalReconciledAmount = this.mockData[index].reconciledAmount || 0
      const newReconciledAmount = originalReconciledAmount + reconciledAmount
      
      // 更新对账发票数据
      this.mockData[index] = {
        ...this.mockData[index],
        isReconciled: true,
        reconciledAmount: newReconciledAmount,
        reconciledTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        relatedInputInvoices: inputInvoices.map(item => item.invoiceCode).join(',')
      }
      
      return true
    },
    
    /**
     * 更新发票状态
     * @param {String} id 发票ID
     * @param {String} status 新状态
     * @returns {Boolean} 是否更新成功
     */
    updateInvoiceStatus(id, status) {
      const index = this.mockData.findIndex(item => item.id === id)
      if (index === -1) return false
      
      this.mockData[index] = {
        ...this.mockData[index],
        invoiceStatus: status,
        updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
      
      return true
    },
    
    /**
     * 根据关键字搜索税收分类数据
     * @param {String} keyword 搜索关键字
     * @returns {Array} 最多十个匹配的税收分类项
     */
    searchTaxClassification(keyword) {
      if (!keyword || typeof keyword !== 'string' || keyword.trim() === '') {
        return [];
      }
      
      const searchKey = keyword.toLowerCase().trim();
      const results = [];
      
      // 遍历扁平化的税收分类数据，查找匹配项
      for (const item of this.flattenedTaxData) {
        // 如果已经找到十个匹配项，则停止搜索
        if (results.length >= 10) {
          break;
        }
        
        // 在标签、编码和路径中搜索关键字
        if (item.label && item.label.toLowerCase().includes(searchKey)) {
          results.push(item);
        } else if (item.codeid && item.codeid.toLowerCase().includes(searchKey)) {
          results.push(item);
        } else if (item.path && item.path.toLowerCase().includes(searchKey)) {
          results.push(item);
        }
      }
      
      return results;
    }
  }
})

export { useInvoiceReconciliationStore }
