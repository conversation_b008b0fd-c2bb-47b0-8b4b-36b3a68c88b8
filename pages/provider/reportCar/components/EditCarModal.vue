<!--
 - 报备车编辑弹窗组件
 - 用于编辑报备车辆信息
-->
<template>
  <a-modal
    :visible="modelValue"
    :title="isEdit ? '编辑报备车辆' : '新增报备车辆'"
    @cancel="handleCancel"
    @before-ok="handleSubmit"
    :mask-closable="false"
    :width="700"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-align="right"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <!-- 基本信息 -->
      <a-divider>基本信息</a-divider>
      <a-form-item field="name" label="车辆名称" required>
        <a-input v-model="formData.name" placeholder="请输入车辆名称" />
      </a-form-item>
      <a-form-item field="image" label="车辆图片" required>
        <ma-upload v-model="formData.image" :limit="1" />
      </a-form-item>
      <a-form-item field="id" label="车辆编号">
        <a-input v-model="formData.id" placeholder="请输入车辆编号" disabled />
      </a-form-item>
      
      <!-- 供应商信息 -->
      <a-divider>供应商信息</a-divider>
      <a-form-item field="provider_id" label="供应商ID" required>
        <a-input v-model="formData.provider_id" placeholder="请输入供应商ID" />
      </a-form-item>
      <a-form-item field="provider_name" label="供应商名称" required>
        <a-input v-model="formData.provider_name" placeholder="请输入供应商名称" />
      </a-form-item>
      <a-form-item field="category" label="分类" required>
        <a-select v-model="formData.category" placeholder="请选择分类">
          <a-option value="汽车">汽车</a-option>
          <a-option value="摩托车">摩托车</a-option>
          <a-option value="电动车">电动车</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="order_source" label="订单来源" required>
        <a-select v-model="formData.order_source" placeholder="请选择订单来源">
          <a-option value="线上">线上</a-option>
          <a-option value="线下">线下</a-option>
        </a-select>
      </a-form-item>
      
      <!-- 商品信息 -->
      <a-divider>商品信息</a-divider>
      <a-form-item field="goods_id" label="商品ID" required>
        <a-input v-model="formData.goods_id" placeholder="请输入商品ID" />
      </a-form-item>
      <a-form-item field="brand" label="品牌" required>
        <a-input v-model="formData.brand" placeholder="请输入品牌" />
      </a-form-item>
      <a-form-item field="goods_category" label="商品分类" required>
        <a-select v-model="formData.goods_category" placeholder="请选择商品分类">
          <a-option value="轿车">轿车</a-option>
          <a-option value="SUV">SUV</a-option>
          <a-option value="MPV">MPV</a-option>
          <a-option value="跑车">跑车</a-option>
        </a-select>
      </a-form-item>
      
      <!-- 销售信息 -->
      <a-divider>销售信息</a-divider>
      <a-form-item field="stock" label="库存" required>
        <a-input-number v-model="formData.stock" placeholder="请输入库存" :min="0" />
      </a-form-item>
      <a-form-item field="price" label="单价" required>
        <a-input-number v-model="formData.price" placeholder="请输入单价" :min="0" :precision="2" />
      </a-form-item>
      <a-form-item field="sales" label="销量">
        <a-input-number v-model="formData.sales" placeholder="请输入销量" :min="0" disabled />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'success']);

const formRef = ref(null);
const formData = reactive({
  id: '',
  name: '',
  image: '',
  provider_id: '',
  provider_name: '',
  category: '',
  order_source: '',
  goods_id: '',
  brand: '',
  goods_category: '',
  stock: 0,
  price: 0,
  sales: 0
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入车辆名称' }],
  image: [{ required: true, message: '请上传车辆图片' }],
  provider_id: [{ required: true, message: '请输入供应商ID' }],
  provider_name: [{ required: true, message: '请输入供应商名称' }],
  category: [{ required: true, message: '请选择分类' }],
  order_source: [{ required: true, message: '请选择订单来源' }],
  goods_id: [{ required: true, message: '请输入商品ID' }],
  brand: [{ required: true, message: '请输入品牌' }],
  goods_category: [{ required: true, message: '请选择商品分类' }],
  stock: [{ required: true, message: '请输入库存' }],
  price: [{ required: true, message: '请输入单价' }],
};

// 监听record变化，填充表单
watch(
  () => props.record,
  (val) => {
    if (val && Object.keys(val).length) {
      Object.keys(formData).forEach(key => {
        if (val[key] !== undefined) {
          formData[key] = val[key];
        }
      });
    }
  },
  { immediate: true, deep: true }
);

// 取消
const handleCancel = () => {
  emit('update:modelValue', false);
  resetForm();
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.keys(formData).forEach(key => {
    formData[key] = key === 'stock' || key === 'price' || key === 'sales' ? 0 : '';
  });
};

// 提交表单
const handleSubmit = async (done) => {
  formRef.value.validate(async (errors) => {
    if (errors) {
      done(false);
      return;
    }
    
    try {
      // 这里可以调用API提交数据
      const apiPath = '/api/provider/reportCar';
      const url = props.isEdit ? `${apiPath}/update` : `${apiPath}/create`;
      
      // 模拟API调用
      console.log('提交数据:', formData);
      
      // 成功后通知父组件
      Message.success(props.isEdit ? '编辑成功' : '新增成功');
      emit('success');
      emit('update:modelValue', false);
      resetForm();
      done(true);
    } catch (error) {
      console.error('提交失败:', error);
      Message.error('操作失败，请重试');
      done(false);
    }
  });
};
</script>

<style scoped>
.arco-divider {
  margin: 16px 0;
}
</style>
