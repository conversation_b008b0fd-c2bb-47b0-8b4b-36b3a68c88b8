<!--
 - 报备车页面
 - 显示报备车辆列表及相关信息
-->
<template>
  <div class="ma-content-block p-4">
    <!-- 标签切换 -->
    <a-tabs :active-key="activeTab" @update:active-key="(key) => activeTab = key" @change="handleTabChange">
      <a-tab-pane v-for="tab in tabList" :key="tab.key" :title="tab.title"></a-tab-pane>
    </a-tabs>
    
    <!-- 顶部标题和操作按钮 -->
    <div class="flex justify-between items-center w-1/2" >
      <div class="text-lg font-medium">报备商品 ({{ total }})</div>
    </div>
    
    <ma-crud style="position: relative; bottom: 24px;" :options="crud" :columns="columns" ref="crudRef" class="report-car-table" :scroll="{ x: '100%' }">
      <!-- 自定义选择列 -->
      <template #id="{ record }">
        <a-checkbox 
          :model-value="isSelected(record.id)" 
          @update:model-value="(checked) => handleCheckboxChange(checked, record)" 
        />
      </template>

      <!-- 自定义图片/信息列 -->
      <template #image="{ record }">
        <div class="flex items-center">
          <a-image :src="record.image || '/placeholder.jpg'" width="60" height="40" />
          <div class="ml-2">
            <div class="font-medium">{{ record.name }}</div>
            <div class="text-xs text-gray-500">编号：{{ record.id }}</div>
          </div>
        </div>
      </template>
      
      <!-- 基本信息列 -->
      <template #base_info="{ record }">
        <div class="text-sm p-1">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-1">
              <div class="flex">
                <span class="text-gray-500 w-20">平台编码：</span>
                <span class="font-medium">{{ record.provider_id }}</span>
              </div>
              <div class="flex">
                <span class="text-gray-500 w-20">分类：</span>
                <span class="font-medium">{{ record.category_name }}</span>
              </div>
              <div class="flex">
                <span class="text-gray-500 w-24">平台参考价格：</span>
                <span class="font-medium">{{ record.goods_category }}</span>
              </div>
              <!-- <div class="flex">
                <span class="text-gray-500 w-20">平台库存：</span>
                <span class="font-medium">{{ record.stock }}</span>
              </div> -->
            </div>
            <div class="space-y-1">
              <div class="flex">
                <span class="text-gray-500 w-24">商品状态：</span>
                <span class="font-medium">{{ record.goods_status }}</span>
              </div>
              <div class="flex">
                <span class="text-gray-500 w-24">品牌：</span>
                <span class="font-medium">{{ record.brand }}</span>
              </div>

            </div>
          </div>
        </div>
      </template>
      
      <!-- 价格列 -->
      <template #price="{ record }">
        <div class="text-red-500 font-medium">¥{{ record.price }}</div>
      </template>
      
      <!-- 数量列 -->
      <template #stock="{ record }">
        <div class="flex items-center">
          <a-input-number
            :model-value="record.quantity || 1"
            :min="1"
            :max="record.stock"
            size="small"
            style="width: 80px"
            @change="(value) => handleStockChange(record, value)"
          />
        </div>
      </template>
      
      <!-- 报价列 -->
      <template #quote="{ record }">
        <div class="flex items-center">
          <span class="text-red-500 font-medium text-lg">¥{{ record.quote }}</span>
          <a-button type="text" class="ml-1" @click="openQuoteModal(record)">
            <template #icon>
              <icon-edit />
            </template>
          </a-button>
        </div>
      </template>
      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space>
          <a-button type="text" class="ml-1" @click="handleDelete(record)">
            <template #icon>
              <icon-delete />
            </template>
            删除
          </a-button>
        </a-space>
      </template>
    </ma-crud>
    
    <!-- 收货信息表单 -->
    <ShippingInfoForm />
    
    <!-- 底部备注区域 -->
    <RemarkSection :selected-rows="selectedRows" />
    
    <!-- 报价编辑弹窗 -->
    <EditQuoteModal
      :model-value="quoteModalVisible"
      :record="currentRecord"
      @update:model-value="quoteModalVisible = $event"
      @success="handleQuoteSuccess"
    />
    
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, computed, watch } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import { IconEdit, IconDelete } from "@arco-design/web-vue/es/icon";
import RemarkSection from "./components/RemarkSection.vue";
import ShippingInfoForm from "./components/ShippingInfoForm.vue";
import EditQuoteModal from "./components/EditQuoteModal.vue";

// 定义页面元数据
definePageMeta({
  name: 'provider-reportCar',
  path: '/provider/reportCar'
});

const crudRef = ref();
const total = ref(0);
const activeTab = ref('all'); // 默认选中全部标签
const quoteModalVisible = ref(false);
const currentRecord = ref({});
const selectedRows = ref([]); // 存储选中的行数据

// 标签数据
const tabList = [
  { key: 'all', title: '君网BG' },
  { key: 'online', title: '政采云BL' },
  { key: 'offline', title: '京东' },
  { key: 'pending', title: '赫兹' }
];

// 页面初始化
onMounted(() => {
  // 加载数据
  fetchData();
});

// 处理标签切换
const handleTabChange = (key) => {
  activeTab.value = key;
  
  // 延迟执行搜索，确保组件已经渲染
  setTimeout(() => {
    // 根据标签设置搜索条件
    if (crudRef.value && crudRef.value.searchForm) {
      const searchForm = crudRef.value.searchForm;
      
      // 根据标签设置不同的搜索条件
      switch (key) {
        case 'all':
          searchForm.setFieldValue('order_source', '君网BG');
          searchForm.setFieldValue('audit_status', '');
          break;
        case 'online':
          searchForm.setFieldValue('order_source', '政采云BL');
          searchForm.setFieldValue('audit_status', '');
          break;
        case 'offline':
          searchForm.setFieldValue('order_source', '京东');
          searchForm.setFieldValue('audit_status', '');
          break;
        case 'pending':
          searchForm.setFieldValue('order_source', '');
          searchForm.setFieldValue('audit_status', '0');
          break;
        case 'approved':
          searchForm.setFieldValue('order_source', '');
          searchForm.setFieldValue('audit_status', '1');
          break;
        case 'rejected':
          searchForm.setFieldValue('order_source', '');
          searchForm.setFieldValue('audit_status', '2');
          break;
        default:
          searchForm.setFieldValue('order_source', '');
          searchForm.setFieldValue('audit_status', '');
          break;
      }
      
      try {
        // 触发搜索
        crudRef.value.refresh();
      } catch (error) {
        console.error('刷新数据失败:', error);
        // 如果刷新失败，尝试重新获取数据
        fetchData();
      }
    }
  }, 0);
};

// 获取数据
const fetchData = async () => {
  try {
    const response = await reportCarApi.getList();
    total.value = response.data.total;
    
    // 如果 crudRef 已经初始化完成，则刷新数据
    if (crudRef.value && typeof crudRef.value.refresh === 'function') {
      // 使用setTimeout确保在下一个事件循环中执行，避免组件未完全初始化
      setTimeout(() => {
        try {
          crudRef.value.refresh();
        } catch (err) {
          console.error('刷新数据失败:', err);
        }
      }, 0);
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    Message.error('获取数据失败');
  }
};

// 创建API对象
const reportCarApi = {
  // 获取列表
  getList: async (params = {}) => {
    console.log('查询参数:', params);
    
    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 平台名称列表
    const platforms = ['君网BG', '政采云BL', '京东', '赫兹'];
    // 商品状态列表
    const statusList = ['在售', '已下架', '待审核'];
    // 分类列表
    const categories = ['轿车', 'SUV', '货车', '新能源车','笔记本','手机','电脑','家电','家具','家居','服饰','鞋包','美妆','个护','母婴','运动','户外','宠物','家装','汽车','数码','办公','礼品','其他'];
    
    return {
      code: 200,
      msg: '获取成功',
      data: {
        total: 10,
        items: Array.from({ length: 10 }, (_, index) => {
          // 基础价格
          const basePrice = Math.floor(Math.random() * 10000 + 5000);
          // 库存数量
          const stock = Math.floor(Math.random() * 50) + 10;
          // 当前选择数量
          const quantity = Math.floor(Math.random() * stock) + 1;
          // 报价（比基础价格高一些）
          const quotePrice = Math.floor(basePrice * (1 + Math.random() * 0.3));
          // 总金额
          const amount = quotePrice * quantity;
          
          return {
            id: `SPU${100000 + index}`,
            name: `商品 ${index + 1}`,
            image: 'https://img12.360buyimg.com/imagetools/jfs/t1/158054/3/45410/37057/662b1030Fddb8470d/a20b6af2770d2632.png',
            // 基本信息字段
            provider_id: `PLT${200000 + index}`,
            category_name: categories[index % categories.length],
            stock: stock,
            goods_status: statusList[index % statusList.length],
            brand: `品牌${String.fromCharCode(65 + index % 10)}`,
            goods_category: `${basePrice.toFixed(2)}`,
            // 其他字段
            order_source: platforms[index % platforms.length],
            audit_status: String(index % 3),
            // 数量和价格相关
            quantity: quantity,
            price: basePrice.toFixed(2),
            quote: quotePrice.toFixed(2),
            amount: amount.toFixed(2),
            sales: Math.floor(Math.random() * 10),
          };
        })
      }
    };
  },
  create: async (params) => {
    console.log('创建参数:', params);
    return { code: 200, msg: '创建成功' };
  },
  update: async (params) => {
    console.log('更新参数:', params);
    return { code: 200, msg: '更新成功' };
  },
  delete: async (params) => {
    console.log('删除参数:', params);
    return { code: 200, msg: '删除成功' };
  }
};

// CRUD配置
const crud = reactive({
  // API配置
  api: reportCarApi.getList,
  showIndex: false,
  border: true,
  stripe: false,
  pageLayout: 'fixed',
  title: '报备车',
  add: { show: false, api: reportCarApi.create },
  edit: { show: false, api: reportCarApi.update },
  delete: { show: true, api: reportCarApi.delete },
  
  // 添加前处理参数
  beforeAdd: (params) => {
    console.log('添加前原始参数:', params);
    return params;
  },

  // 编辑前处理参数
  beforeEdit: (params) => {
    console.log('编辑前原始参数:', params);
    return params;
  },
});

// 表格列配置
const columns = reactive([
{
    title: '选择',
    dataIndex: 'id',
    width: 60,
    hide: false,
    align: "center",
    slot: true,
  },
  {
    title: '图片/信息',
    dataIndex: 'image',
    width: 150,
    align: "center",
    slot: true,
  },
  {
    title: '基本信息',
    dataIndex: 'base_info',
    align: "center",
    width: 330,
    slot: true,
    search: false,
  },
  {
    title: '销售价',
    dataIndex: 'price',
    align: "center",
    width: 120,
    search: false,
  },
  {
    title: '数量',
    dataIndex: 'stock',
    align: "center",
    width: 120,
    search: false,
  },
  {
    title: '报价',
    align: "center",
    dataIndex: 'quote',
    width: 120,
    slot: true,
    search: false,
  },
  {
    title: '金额',
    align: "center",
    dataIndex: 'amount',
    width: 120,
  },
  {
    title: '单价',
    align: "center",
    dataIndex: 'price',
    width: 120,
    slot: true,
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    width: 120,
    fixed: "right",
    slotName: "operation",
  }
]);

// 导出数据方法
const handleExport = async () => {
  try {
    await crudRef.value.exportTable();
    Message.success('导出成功');
  } catch (error) {
    Message.error('导出失败');
    console.error('导出失败:', error);
  }
};

// 处理数量变化
const handleStockChange = (record, value) => {
  console.log('数量变化:', record.id, value);
  
  // 验证数量不能超过库存
  // if (value > record.stock) {
  //   Message.error(`数量不能超过库存数量 ${record.stock}`);
  //   return;
  // }
  
  // 更新数量
  record.quantity = value;
  
  // 计算新的总金额
  record.amount = (parseFloat(record.quote) * value).toFixed(2);
  
  // 实际项目中应调用API更新数据
  Message.success(`商品 ${record.name} 数量已更新为 ${value}`);
};

// 打开报价编辑弹窗
const openQuoteModal = (record) => {
  currentRecord.value = { ...record };
  quoteModalVisible.value = true;
};

// 处理报价修改成功
const handleQuoteSuccess = (data) => {
  console.log('报价更新:', data);
  
  try {
    // 直接更新当前记录
    if (currentRecord.value && currentRecord.value.id === data.id) {
      // 更新当前记录的报价和总金额
      currentRecord.value.quote = data.quote;
      currentRecord.value.amount = data.amount;
      
      // 在实际项目中应调用API更新数据
      // 这里仅更新当前记录的展示数据
      
      Message.success(`商品 ${currentRecord.value.name} 报价已更新为 ¥${data.quote}`);
      
      // 延迟刷新数据，确保消息提示先显示
      setTimeout(() => {
        fetchData();
      }, 300);
    }
  } catch (error) {
    console.error('更新报价失败:', error);
    Message.error('更新报价失败');
  }
};

// 计算已选择的商品数量
const selectedCount = computed(() => {
  return selectedRows.value.length;
});

// 计算已选择商品的总金额
const selectedTotalAmount = computed(() => {
  if (!selectedRows.value.length) return '0.00';
  
  const total = selectedRows.value.reduce((sum, item) => {
    return sum + parseFloat(item.amount || 0);
  }, 0);
  
  return total.toFixed(2);
});

// 检查某个商品是否被选中
const isSelected = (id) => {
  return selectedRows.value.some(item => item.id === id);
};

// 处理复选框变化
const handleCheckboxChange = (checked, record) => {
  if (checked) {
    // 选中商品，添加到选中数组
    if (!isSelected(record.id)) {
      selectedRows.value.push(record);
      console.log('选中商品:', record.name, '当前选中数量:', selectedRows.value.length);
    }
  } else {
    // 取消选中，从数组中移除
    const index = selectedRows.value.findIndex(item => item.id === record.id);
    if (index !== -1) {
      selectedRows.value.splice(index, 1);
      console.log('取消选中商品:', record.name, '当前选中数量:', selectedRows.value.length);
    }
  }
};

// 处理删除操作
const handleDelete = async (record) => {
  try {
    // 显示确认对话框
    Modal.warning({
      title: '确认删除',
      content: `确定要从报备车中移除商品 ${record.name} 吗？`,
      okText: '确认',
      cancelText: '取消',
      hideCancel: false,
      onOk: async () => {
        try {
          // 调用删除API
          const res = await reportCarApi.delete({ id: record.id });
          
          if (res && res.code === 200) {
            Message.success(res.msg || '删除成功');
            // 刷新数据
            fetchData();
          } else {
            Message.error(res?.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除API调用失败:', error);
          Message.error('删除操作失败，请重试');
        }
      }
    });
  } catch (error) {
    console.error('删除对话框显示失败:', error);
    Message.error('删除操作失败，请重试');
  }
};


</script>

<script>
export default { name: "provider-reportCar" };
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
}

/* 表头居中对齐 */
.report-car-table :deep(.arco-table-th) {
  text-align: center;
}

/* 数字类型列右对齐 */
.report-car-table :deep(.arco-table-cell) {
  /* 默认居中对齐 */
  text-align: center;
}

/* 数字类型列右对齐 */
.report-car-table :deep(.arco-table-col-price) .arco-table-cell,
.report-car-table :deep(.arco-table-col-stock) .arco-table-cell,
.report-car-table :deep(.arco-table-col-quote) .arco-table-cell,
.report-car-table :deep(.arco-table-col-amount) .arco-table-cell {
  text-align: right;
}

/* 图片信息列左对齐 */
.report-car-table :deep(.arco-table-col-image) .arco-table-cell {
  text-align: left;
}

/* 确保图片按比例填充 */
.report-car-table :deep(.arco-image-img) {
  object-fit: cover;
}
:deep(._crud-header){
  height: 0;
}
:deep(.operation-tools){
  position: relative;
  bottom: 24px;
}
</style>
