<template>
  <div class="dashboard-container">
    <!-- 工作台区域 - 单独一行在最上面 -->
    <div class="grid grid-cols-1 gap-4 mb-4">
      <div>
        <WorkbenchItems :items="pendingItems" />
      </div>
    </div>

    <!-- 顶部KPI区 -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 xl:grid-cols-3">
      <div>
        <KpiCard title="销售金额" prefix="¥" :value="kpiData.salesAmount.value" :growth="kpiData.salesAmount.growth"
          :trend="kpiData.salesAmount.trend" color="#165DFF" />
      </div>
      <div>
        <KpiCard title="订单数量" :value="kpiData.orderCount.value" suffix="单" :growth="kpiData.orderCount.growth"
          :trend="kpiData.orderCount.trend" color="#0FC6C2" />
      </div>
      <div>
        <SalesTargetGauge :target="kpiData.salesTarget.target" :current="kpiData.salesTarget.current"
          :rate="kpiData.salesTarget.rate" color="#722ED1" />
      </div>
    </div>

    <!-- 销售数据区 -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
      <div>
        <ChannelSales title="渠道销售额" :channels="channelSalesData.channels" :selected="channelSalesData.selected"
          :data="channelSalesData.data" />
      </div>
      <div>
        <PlatformSalesPie title="平台销售额" :data="platformSalesData.data" />
      </div>
      <div>
        <OrderFlowDonut title="订单流量对比" :total="orderFlowData.total" :data="orderFlowData.data" />
      </div>
    </div>

    <!-- 热销类目与销售排行 -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 xl:grid-cols-4">
      <div class="md:col-span-1 xl:col-span-1">
        <HotCategoriesCloud title="热销类目" :data="hotCategoriesData" />
      </div>
      <div class="md:col-span-2 xl:col-span-1">
        <SalesRanking title="业绩排行" :data="salesRankingData" />
      </div>
      <div class="xl:col-span-1">
        <SalesRanking title="热销商品排行" :data="topProductsData" />
      </div>
      <div class="xl:col-span-1">
        <TopProvinces title="本月热销省份TOP5" :data="topProvincesData.data" :type="topProvincesData.type" />
      </div>
    </div>

    <!-- 月度销售数据 -->
    <div class="grid grid-cols-1 gap-4">
      <div>
        <MonthlySalesChart title="月度销售数据" :months="monthlySalesData.months" :data="monthlySalesData.data" />
      </div>
    </div>

    <!-- 财务数据概览 -->
    <div class="grid grid-cols-1 gap-4">
      <div>
        <FinancialOverview title="财务数据概览" :receivables="financialOverviewData.receivables"
          :payables="financialOverviewData.payables" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import getSalesData from '~/data/salesData';
import KpiCard from '~/components/dashboard/sales/kpi-card.vue';
import SalesTargetGauge from '~/components/dashboard/sales/sales-target-gauge.vue';
import WorkbenchItems from '~/components/dashboard/sales/workbench-items.vue';
import ChannelSales from '~/components/dashboard/sales/channel-sales.vue';
import PlatformSalesPie from '~/components/dashboard/sales/platform-sales-pie.vue';
import OrderFlowDonut from '~/components/dashboard/sales/order-flow-donut.vue';
import HotCategoriesCloud from '~/components/dashboard/sales/hot-categories-cloud.vue';
import SalesRanking from '~/components/dashboard/sales/sales-ranking.vue';
import TopProvinces from '~/components/dashboard/sales/top-provinces.vue';
import MonthlySalesChart from '~/components/dashboard/sales/monthly-sales-chart.vue';
import FinancialOverview from '~/components/dashboard/sales/financial-overview.vue';
definePageMeta({
  name: 'providerdashboard',
  path: '/provider/dashboard'
})
// 获取销售数据
const {
  getKpiData,
  getPendingItems,
  getChannelSalesData,
  getPlatformSalesData,
  getOrderFlowData,
  getHotCategoriesData,
  getSalesRankingData,
  getTopProductsData,
  getTopProvincesData,
  getMonthlySalesData,
  getFinancialOverviewData
} = getSalesData();

// 初始化数据
const kpiData = ref(getKpiData());
const pendingItems = ref(getPendingItems());
const channelSalesData = ref(getChannelSalesData());
const platformSalesData = ref(getPlatformSalesData());
const orderFlowData = ref(getOrderFlowData());
const hotCategoriesData = ref(getHotCategoriesData());
const salesRankingData = ref(getSalesRankingData());
const topProductsData = ref(getTopProductsData());
const topProvincesData = ref(getTopProvincesData());
const monthlySalesData = ref(getMonthlySalesData());
const financialOverviewData = ref(getFinancialOverviewData());
</script>

<style scoped>
h1 {
  font-size: 20px;
  font-weight: 600;
  color: #1D2129;
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1D2129;
  margin: 24px 0 16px 0;
}

.grid {
  display: grid;
  margin-bottom: 16px;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-3 {
    grid-column: span 3 / span 3;
  }
}
</style>