<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应商入驻门户</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
        }
        .hero-pattern {
            background-image: radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
                              radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .tab-active {
            background-color: #e53e3e;
            color: white;
        }
        .step-line::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -25px;
            width: 50px;
            height: 2px;
            background: #e53e3e;
            transform: translateY(-50%);
        }
        .step-line:last-child::after {
            display: none;
        }
    </style>
</head>
<body class="font-sans">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="text-2xl font-bold text-red-600">
                        <i class="fas fa-store mr-2"></i>供应商招商平台
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#home" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md">首页</a>
                    <a href="#process" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md">入驻流程</a>
                    <a href="#benefits" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md">入驻优势</a>
                    <a href="#contact" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md">联系我们</a>
                    <button onclick="showLoginModal()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">登录</button>
                    <button onclick="showRegisterModal()" class="bg-white text-red-600 border border-red-600 px-4 py-2 rounded-md hover:bg-red-50">注册</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主页面内容 -->
    <div id="mainContent">
        <!-- Hero Section -->
        <section id="home" class="gradient-bg hero-pattern min-h-screen flex items-center pt-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                <div class="flex flex-col lg:flex-row items-center justify-between">
                    <div class="lg:w-1/2 text-white mb-8 lg:mb-0">
                        <h1 class="text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                            来平台<br>
                            <span class="text-yellow-300">共赢成功</span>
                        </h1>
                        <p class="text-xl mb-8 opacity-90">
                            携手优质供应商，共建生态圈，实现互利共赢
                        </p>
                        <div class="flex space-x-4">
                            <button onclick="showRegisterModal()" class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-yellow-300 transition-colors">
                                立即入驻
                            </button>
                            <button onclick="scrollToSection('process')" class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-red-600 transition-colors">
                                了解流程
                            </button>
                        </div>
                    </div>
                    <div class="lg:w-1/2 flex justify-center">
                        <div class="bg-white rounded-2xl p-8 shadow-2xl max-w-md w-full">
                            <div class="text-center mb-6">
                                <h3 class="text-2xl font-bold text-gray-800 mb-2">平台自营成熟品质供应商</h3>
                                <div class="flex justify-center space-x-8 mt-4">
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                            <i class="fas fa-check text-white text-xl"></i>
                                        </div>
                                        <span class="text-sm text-gray-600">正道成功</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
                                            <i class="fas fa-chart-line text-white text-xl"></i>
                                        </div>
                                        <span class="text-sm text-gray-600">共同成长</span>
                                    </div>
                                </div>
                            </div>
                            <button onclick="showRegisterModal()" class="w-full bg-gradient-to-r from-red-600 to-orange-500 text-white py-4 rounded-lg text-lg font-semibold hover:from-red-700 hover:to-orange-600 transition-all">
                                立即入驻
                            </button>
                            <p class="text-sm text-gray-500 text-center mt-4">
                                成为优质供应商  共创美好</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 入驻流程 -->
        <section id="process" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-gray-800 mb-4">入驻流程</h2>
                    <p class="text-xl text-gray-600">简单4步，快速入驻</p>
                </div>

                <!-- 流程步骤 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
                    <div class="text-center relative step-line">
                        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">1</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">账号注册</h3>
                        <p class="text-gray-600">使用可在邮箱或手机号，快速注册供应商账号</p>
                        <div class="mt-4 text-sm text-gray-500">预计2分钟</div>
                    </div>

                    <div class="text-center relative step-line">
                        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">2</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">提交资料</h3>
                        <p class="text-gray-600">填写企业信息、产品信息、经营信息</p>
                        <div class="mt-4 text-sm text-gray-500">预计2个工作日</div>
                    </div>

                    <div class="text-center relative step-line">
                        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">3</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">等待审核</h3>
                        <p class="text-gray-600">平台审核信息及初审，机器审核行审核</p>
                        <div class="mt-4 text-sm text-gray-500">预计5个工作日</div>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">4</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">开始合作</h3>
                        <p class="text-gray-600">审核通过后即可开始销售，实现共赢</p>
                        <div class="mt-4 text-sm text-gray-500">立即开始</div>
                    </div>
                </div>

                <!-- 详细说明 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-xl p-6 shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user-plus text-red-600"></i>
                            </div>
                            <h4 class="text-lg font-bold text-gray-800">账号注册</h4>
                        </div>
                        <div class="space-y-3 text-sm text-gray-600">
                            <div class="flex items-start">
                                <span class="font-semibold text-red-600 mr-2">01</span>
                                <div>
                                    <p class="font-medium mb-1">企业信息</p>
                                    <p>请填写好企业名称、注册地址确，清晰营好与系统的联系人及合同签署人联系方式；请准备好营业执照、法人身份证明等企业资质所需文件；</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-6 shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-file-alt text-red-600"></i>
                            </div>
                            <h4 class="text-lg font-bold text-gray-800">提交资料</h4>
                        </div>
                        <div class="space-y-3 text-sm text-gray-600">
                            <div class="flex items-start">
                                <span class="font-semibold text-red-600 mr-2">02</span>
                                <div>
                                    <p class="font-medium mb-1">产品信息</p>
                                    <p>请填写好产品、类目、产品的相关信息，包括但不限于产品基本信息及商品在主要电商平台的及京东的报价</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <span class="font-semibold text-red-600 mr-2">03</span>
                                <div>
                                    <p class="font-medium mb-1">经营信息</p>
                                    <p>请填写好经营规模、运营团队、仓储能力提供的相关信息，包括线上线下经营规模、自运营团队规模及经验等信息</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-6 shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-tasks text-red-600"></i>
                            </div>
                            <h4 class="text-lg font-bold text-gray-800">等待审核</h4>
                        </div>
                        <div class="space-y-3 text-sm text-gray-600">
                            <div class="flex items-start">
                                <span class="font-semibold text-red-600 mr-2">01</span>
                                <div>
                                    <p class="font-medium mb-1">机器审核</p>
                                    <p>依据条类目的准入阈值对您交资料进行初步审核，机器审核耗时1-2小时</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <span class="font-semibold text-red-600 mr-2">02</span>
                                <div>
                                    <p class="font-medium mb-1">类型审核</p>
                                    <p>类目审核根据品类商品类及品品类型程分类审核，预计2-3天</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <span class="font-semibold text-red-600 mr-2">03</span>
                                <div>
                                    <p class="font-medium mb-1">类经经理审核</p>
                                    <p>类经经理确保品类商品类运营规则与次价格入驻行审，预计等2-3天</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 入驻优势 -->
        <section id="benefits" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-gray-800 mb-4">入驻优势</h2>
                    <p class="text-xl text-gray-600">多重保障，助力业务增长</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="bg-white border border-gray-200 rounded-xl p-8 card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                            <i class="fas fa-users text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">庞大用户群体</h3>
                        <p class="text-gray-600">亿级活跃用户，为您的产品提供广阔的市场空间</p>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-xl p-8 card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                            <i class="fas fa-rocket text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">流量扶持</h3>
                        <p class="text-gray-600">平台精准流量推荐，多渠道曝光，助力销量提升</p>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-xl p-8 card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                            <i class="fas fa-truck text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">物流保障</h3>
                        <p class="text-gray-600">完善的物流网络，快速配送，提升用户体验</p>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-xl p-8 card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                            <i class="fas fa-headset text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">专业服务</h3>
                        <p class="text-gray-600">专属客服团队，7x24小时在线，解决运营问题</p>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-xl p-8 card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                            <i class="fas fa-chart-line text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">数据分析</h3>
                        <p class="text-gray-600">详细的销售数据分析，帮助优化运营策略</p>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-xl p-8 card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
                            <i class="fas fa-shield-alt text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">品牌保护</h3>
                        <p class="text-gray-600">严格的品牌保护措施，维护您的品牌权益</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系我们 -->
        <section id="contact" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-gray-800 mb-4">联系我们</h2>
                    <p class="text-xl text-gray-600">多种方式，随时为您服务</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-xl p-8 text-center shadow-lg card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-phone text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">客服热线</h3>
                        <p class="text-2xl font-bold text-red-600 mb-2">************</p>
                        <p class="text-gray-600">工作时间：9:00-21:00</p>
                    </div>

                    <div class="bg-white rounded-xl p-8 text-center shadow-lg card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-envelope text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">邮箱咨询</h3>
                        <p class="text-xl font-bold text-red-600 mb-2"><EMAIL></p>
                        <p class="text-gray-600">24小时内回复</p>
                    </div>

                    <div class="bg-white rounded-xl p-8 text-center shadow-lg card-hover">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fab fa-weixin text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">微信咨询</h3>
                        <p class="text-xl font-bold text-red-600 mb-2">SupplierHelper</p>
                        <p class="text-gray-600">扫码添加微信</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 登录模态框 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">登录</h2>
                <button onclick="hideLoginModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <form>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">用户名/邮箱</label>
                    <input type="text" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入用户名或邮箱">
                </div>
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2">密码</label>
                    <input type="password" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入密码">
                </div>
                <button type="submit" class="w-full bg-red-600 text-white py-3 rounded-lg text-lg font-semibold hover:bg-red-700 transition-colors">
                    登录
                </button>
                <div class="text-center mt-4">
                    <span class="text-gray-600">还没有账号？</span>
                    <button type="button" onclick="hideLoginModal(); showRegisterModal();" class="text-red-600 hover:text-red-700 font-semibold">立即注册</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div id="registerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-2xl p-8 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">供应商注册</h2>
                <button onclick="hideRegisterModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- 注册步骤指示器 -->
            <div class="flex justify-center mb-8">
                <div class="flex items-center space-x-8">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                        <span class="ml-2 text-red-600 font-semibold">基本信息</span>
                    </div>
                    <div class="w-16 h-0.5 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-bold">2</div>
                        <span class="ml-2 text-gray-600">企业信息</span>
                    </div>
                    <div class="w-16 h-0.5 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        <span class="ml-2 text-gray-600">完成注册</span>
                    </div>
                </div>
            </div>

            <form>
                <!-- 基本信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2">联系人姓名 *</label>
                        <input type="text" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入联系人姓名" required>
                    </div>
                    <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2">手机号码 *</label>
                        <input type="tel" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入手机号码" required>
                    </div>
                    <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2">邮箱地址 *</label>
                        <input type="email" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入邮箱地址" required>
                    </div>
                    <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2">公司名称 *</label>
                        <input type="text" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入公司名称" required>
                    </div>
                </div>

                <!-- 企业信息 -->
                <div class="mb-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">企业信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">统一社会信用代码 *</label>
                            <input type="text" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入统一社会信用代码" required>
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">注册资金</label>
                            <input type="text" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入注册资金">
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">主营类目 *</label>
                            <select class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" required>
                                <option value="">请选择主营类目</option>
                                <option value="electronics">电子产品</option>
                                <option value="clothing">服装服饰</option>
                                <option value="home">家居生活</option>
                                <option value="food">食品饮料</option>
                                <option value="beauty">美妆个护</option>
                                <option value="books">图书文教</option>
                                <option value="sports">运动户外</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">经营年限</label>
                            <select class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500">
                                <option value="">请选择经营年限</option>
                                <option value="1">1年以下</option>
                                <option value="1-3">1-3年</option>
                                <option value="3-5">3-5年</option>
                                <option value="5-10">5-10年</option>
                                <option value="10+">10年以上</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">公司地址 *</label>
                        <input type="text" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500" placeholder="请输入详细地址" required>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">公司简介</label>
                        <textarea class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-red-500 h-24" placeholder="请简要介绍您的公司"></textarea>
                    </div>
                </div>

                <!-- 资质上传 -->
                <div class="mb-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">资质证明</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">营业执照 *</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                                <p class="text-gray-600">点击上传或拖拽文件</p>
                                <p class="text-xs text-gray-500 mt-1">支持 JPG、PNG、PDF，大小不超过5MB</p>
                            </div>
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">税务登记证</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                                <p class="text-gray-600">点击上传或拖拽文件</p>
                                <p class="text-xs text-gray-500 mt-1">支持 JPG、PNG、PDF，大小不超过5MB</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 协议确认 -->
                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-3" required>
                        <span class="text-gray-700">我已阅读并同意</span>
                        <a href="#" class="text-red-600 hover:text-red-700 mx-1">《供应商入驻协议》</a>
                        <span class="text-gray-700">和</span>
                        <a href="#" class="text-red-600 hover:text-red-700 mx-1">《隐私政策》</a>
                    </label>
                </div>

                <button type="submit" class="w-full bg-red-600 text-white py-3 rounded-lg text-lg font-semibold hover:bg-red-700 transition-colors">
                    提交注册申请
                </button>

                <div class="text-center mt-4">
                    <span class="text-gray-600">已有账号？</span>
                    <button type="button" onclick="hideRegisterModal(); showLoginModal();" class="text-red-600 hover:text-red-700 font-semibold">立即登录</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">关于我们</h3>
                    <p class="text-gray-300">专业的B2B供应商入驻平台，致力于为优质供应商提供最佳的销售渠道和服务支持。</p>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-300 hover:text-white">首页</a></li>
                        <li><a href="#process" class="text-gray-300 hover:text-white">入驻流程</a></li>
                        <li><a href="#benefits" class="text-gray-300 hover:text-white">入驻优势</a></li>
                        <li><a href="#contact" class="text-gray-300 hover:text-white">联系我们</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-4">服务支持</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white">帮助中心</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">平台规则</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">费用说明</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">开票服务</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-4">联系方式</h3>
                    <div class="space-y-2">
                        <p class="text-gray-300"><i class="fas fa-phone mr-2"></i>************</p>
                        <p class="text-gray-300"><i class="fas fa-envelope mr-2"></i><EMAIL></p>
                        <p class="text-gray-300"><i class="fas fa-map-marker-alt mr-2"></i>北京市朝阳区xxx大厦</p>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">&copy; 2024 供应商入驻平台. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // 模态框控制
        function showLoginModal() {
            document.getElementById('loginModal').classList.remove('hidden');
        }

        function hideLoginModal() {
            document.getElementById('loginModal').classList.add('hidden');
        }

        function showRegisterModal() {
            document.getElementById('registerModal').classList.remove('hidden');
        }

        function hideRegisterModal() {
            document.getElementById('registerModal').classList.add('hidden');
        }

        // 平滑滚动
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth'
            });
        }

        // 点击外部关闭模态框
        window.onclick = function(event) {
            const loginModal = document.getElementById('loginModal');
            const registerModal = document.getElementById('registerModal');
            
            if (event.target === loginModal) {
                hideLoginModal();
            }
            if (event.target === registerModal) {
                hideRegisterModal();
            }
        }

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 50) {
                nav.classList.add('bg-opacity-95');
            } else {
                nav.classList.remove('bg-opacity-95');
            }
        });

        // 表单验证
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // 简单的表单验证
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;
                    
                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            isValid = false;
                            field.classList.add('border-red-500');
                        } else {
                            field.classList.remove('border-red-500');
                        }
                    });
                    
                    if (isValid) {
                        alert('提交成功！我们会尽快处理您的申请。');
                        form.reset();
                        hideLoginModal();
                        hideRegisterModal();
                    } else {
                        alert('请填写所有必填字段');
                    }
                });
            });
        });

        // 导航链接点击处理
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
