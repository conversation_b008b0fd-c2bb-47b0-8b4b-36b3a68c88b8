<template>
  <div class="claim-application-container">
    <div class="page-header">
      <h1>认款申请</h1>
    </div>
    
    <div class="page-content">
      <!-- 订单信息区域 -->
      <div class="order-info-section">
        <div class="section-header">
          <h2>订单信息</h2>
        </div>
        <div class="order-table-container">
          <a-table :data="orderData" :bordered="true" :pagination="false">
            <template #columns>
              <a-table-column title="订单编号" data-index="orderNo" align="center" />
              <a-table-column title="下单时间" data-index="orderTime" align="center" />
              <a-table-column title="收货人" data-index="receiver" align="center" />
              <a-table-column title="收货地址" data-index="receiverAddress" align="center" />
              <a-table-column title="商品编码" data-index="productCode" align="center" />
              <a-table-column title="商品名称" data-index="productName" align="center" />
              <a-table-column title="应收金额" data-index="actualAmount" align="center" />
              <a-table-column title="数量" data-index="quantity" align="center" />
              <a-table-column title="实付金额" data-index="paidAmount" align="center" />
            </template>
            <template #footer>
              <div class="table-footer">
                <div class="total-amount" style="display: flex; justify-content: flex-end;">
                  <span class="label">合计：</span>
                  <span class="value">{{ orderTotalAmount }}</span>
                </div>
              </div>
            </template>
          </a-table>
        </div>
      </div>
      
      <!-- 非本司订单区域 -->
      <ClaimRecordList 
        :records="pendingOrders" 
        @remove="removeOrder"
        @add="showAddRecordModal"
        title="非本司订单"
      />
      
      <!-- 添加记录弹窗 -->
      <AddRecordModal
        v-model:visible="addRecordModalVisible"
        @confirm="handleAddRecord"
      />
      
      <!-- 认款申请表单区域 -->
      <div class="claim-form-section">
        <div class="section-header">
          <h2>认款申请</h2>
        </div>
        
        <!-- 收款记录选项卡 -->
        <ClaimReceiptTabs
          v-model:activeTab="activeReceiptTab"
          :system-records="systemReceipts"
          :manual-records="manualReceipts"
          @remove-system="removeSystemReceipt"
          @remove-manual="removeManualReceipt"
          @add-system="showAddSystemModal"
          @add-manual="showAddManualModal"
        />
        
        <!-- 认款申请备注和附件 -->
        <ClaimRemarkUpload
          v-model="remarkData"
        />
        
        <!-- 添加系统收款记录弹窗 -->
        <AddSystemReceiptModal
          v-model:visible="addSystemModalVisible"
          @confirm="handleAddSystemReceipts"
        />
        
        <!-- 添加手动收款记录弹窗 -->
        <AddManualReceiptModal
          v-model:visible="addManualModalVisible"
          @confirm="handleAddManualReceipt"
        />
        
      </div>
      
      <!-- 底部操作区域 -->
      <div class="action-buttons">
        <a-space>
          <a-button @click="goBack">取消</a-button>
          <a-button type="primary" @click="showConfirmModal">确定</a-button>
        </a-space>
      </div>
      
      <!-- 确认弹窗 -->
      <ClaimConfirmModal
        v-model:visible="confirmModalVisible"
        :form-data="formData"
        @confirm="submitForm"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h, computed } from 'vue';
import ClaimRecordList from './components/ClaimRecordList.vue';
import ClaimConfirmModal from './components/ClaimConfirmModal.vue';
import AddRecordModal from './components/AddRecordModal.vue';
import ClaimReceiptTabs from './components/ClaimReceiptTabs.vue';
import AddSystemReceiptModal from './components/AddSystemReceiptModal.vue';
import AddManualReceiptModal from './components/AddManualReceiptModal.vue';
import ClaimRemarkUpload from './components/ClaimRemarkUpload.vue';
import { Message } from '@arco-design/web-vue';
import { useRouter } from '#app';

// 定义页面路由元信息
definePageMeta({
  name: 'provider-finance-claim-application',
  title: "认款申请",
  icon: "icon-check-circle",
});

// 路由
const router = useRouter();

// 表单引用
const formRef = ref(null);

// 确认弹窗可见性
const confirmModalVisible = ref(false);

// 添加记录弹窗可见性
const addRecordModalVisible = ref(false);

// 收款记录相关状态
const activeReceiptTab = ref('system');
const systemReceipts = ref([]);
const manualReceipts = ref([]);
const remarkData = ref({ remark: '', attachments: [] });
const addSystemModalVisible = ref(false);
const addManualModalVisible = ref(false);

// 订单数据
const orderData = ref([]);

// 计算订单合计金额
const orderTotalAmount = computed(() => {
  if (!orderData.value || orderData.value.length === 0) {
    return '0.00';
  }
  
  const total = orderData.value.reduce((sum, item) => {
    const amount = parseFloat(item.paidAmount) || 0;
    return sum + amount;
  }, 0);
  
  return total.toFixed(2);
});

// 待处理订单
const pendingOrders = ref([]);

// 表单数据
const formData = reactive({
  payerName: '',
  payerAccount: '',
  receiverName: '',
  receiverAccount: '',
  receiptDate: null,
  amount: null,
  voucherList: [],
  remark: ''
});

// 表单验证规则
const rules = {
  payerName: [
    { required: true, message: '请输入付款人名称' }
  ],
  payerAccount: [
    { required: true, message: '请输入付款人账号' }
  ],
  receiverName: [
    { required: true, message: '请输入收款人名称' }
  ],
  receiverAccount: [
    { required: true, message: '请输入收款人账号' }
  ],
  receiptDate: [
    { required: true, message: '请选择收款日期' }
  ],
  amount: [
    { required: true, message: '请输入收款金额' }
  ],
  vouchers: [
    { 
      required: true, 
      validator: (value, cb) => {
        if (formData.voucherList && formData.voucherList.length > 0) {
          cb();
        } else {
          cb(new Error('请上传凭证'));
        }
      }
    }
  ]
};

// 初始化数据
onMounted(() => {
  // 模拟加载订单数据
  loadOrderData();
});

// 加载订单数据
const loadOrderData = () => {
  // 模拟数据
  orderData.value = [
    {
      orderNo: '**********',
      orderTime: '2024-05-15 11:57:00',
      receiver: '李容',
      receiverAddress: '福建宁德市蕉城区飞鸾镇大营街道下营4号楼501号(4号楼)',
      productCode: '**********',
      productName: '洗衣机 海尔(Haier)VISION DS-8B2ZN-K5K/ZC 滚筒洗衣机 8.5公斤 蓝色',
      actualAmount: '200.00',
      quantity: '1',
      paidAmount: '200.00'
    },
    {
      orderNo: '**********',
      orderTime: '2024-05-15 11:57:00',
      receiver: '李容',
      receiverAddress: '福建宁德市蕉城区飞鸾镇大营街道下营4号楼501号(4号楼)',
      productCode: '0001001422',
      productName: '洗衣机 海尔(Haier)VISION DS-8B2ZN-K516/ks 滚筒洗衣机 8KG 白色',
      actualAmount: '100.00',
      quantity: '2',
      paidAmount: '200.00'
    },
    {
      orderNo: '2023110903',
      orderTime: '2024-05-15 11:57:00',
      receiver: '李容',
      receiverAddress: '福建宁德市蕉城区飞鸾镇大营街道下营4号楼501号(4号楼)',
      productCode: '**********',
      productName: '洗衣机 海尔(Haier)VISION DS-8B2ZN-K5K/ZC 滚筒洗衣机 8.5公斤 蓝色',
      actualAmount: '200.00',
      quantity: '2',
      paidAmount: '400.00'
    }
  ];
  
  // 初始化待处理订单
  pendingOrders.value = [
    { orderNo: '2023110906', totalPrice: '200.00' },
    { orderNo: '2023110912', totalPrice: '200.00' }
  ];
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 删除订单
const removeOrder = (record) => {
  const index = pendingOrders.value.findIndex(item => item.orderNo === record.orderNo);
  if (index !== -1) {
    pendingOrders.value.splice(index, 1);
    Message.success('删除成功');
  }
};

// 显示添加记录弹窗
const showAddRecordModal = () => {
  addRecordModalVisible.value = true;
};

// 处理添加记录
const handleAddRecord = (record) => {
  // 检查是否已存在相同订单编号
  const existingOrder = pendingOrders.value.find(item => item.orderNo === record.orderNo);
  if (existingOrder) {
    Message.warning('该订单编号已存在');
    return;
  }
  
  // 添加新记录
  pendingOrders.value.push({
    ...record,
    totalPrice: parseFloat(record.totalPrice).toFixed(2)
  });
  
  Message.success('添加成功');
};

// 显示添加系统收款记录弹窗
const showAddSystemModal = () => {
  addSystemModalVisible.value = true;
};

// 显示添加手动收款记录弹窗
const showAddManualModal = () => {
  addManualModalVisible.value = true;
};

// 处理添加系统收款记录
const handleAddSystemReceipts = (records) => {
  // 检查是否已存在相同收款单号
  const newRecords = records.filter(record => {
    return !systemReceipts.value.some(item => item.receiptNo === record.receiptNo);
  });
  
  if (newRecords.length === 0) {
    Message.warning('选中的收款记录已存在');
    return;
  }
  
  // 添加新记录
  systemReceipts.value.push(...newRecords);
  
  // 更新表单数据
  updateFormDataFromReceipts();
  
  Message.success('添加成功');
};

// 处理添加手动收款记录
const handleAddManualReceipt = (record) => {
  // 检查是否已存在相同收款单号
  const existingReceipt = manualReceipts.value.find(item => item.receiptNo === record.receiptNo);
  if (existingReceipt) {
    Message.warning('该收款单号已存在');
    return;
  }
  
  // 添加新记录
  manualReceipts.value.push(record);
  
  // 更新表单数据
  updateFormDataFromReceipts();
  
  Message.success('添加成功');
};

// 删除系统收款记录
const removeSystemReceipt = (record) => {
  const index = systemReceipts.value.findIndex(item => item.receiptNo === record.receiptNo);
  if (index !== -1) {
    systemReceipts.value.splice(index, 1);
    updateFormDataFromReceipts();
    Message.success('删除成功');
  }
};

// 删除手动收款记录
const removeManualReceipt = (record) => {
  const index = manualReceipts.value.findIndex(item => item.receiptNo === record.receiptNo);
  if (index !== -1) {
    manualReceipts.value.splice(index, 1);
    updateFormDataFromReceipts();
    Message.success('删除成功');
  }
};

// 根据收款记录更新表单数据
const updateFormDataFromReceipts = () => {
  // 如果有收款记录，使用第一条记录的信息填充表单
  let receipt;
  
  if (activeReceiptTab.value === 'system' && systemReceipts.value.length > 0) {
    receipt = systemReceipts.value[0];
  } else if (activeReceiptTab.value === 'manual' && manualReceipts.value.length > 0) {
    receipt = manualReceipts.value[0];
  }
  
  if (receipt) {
    // 更新付款人信息
    formData.payerName = receipt.payerName || formData.payerName;
    formData.payerAccount = receipt.payerAccount || formData.payerAccount;
    
    // 计算总金额
    let totalAmount = 0;
    
    if (activeReceiptTab.value === 'system') {
      totalAmount = systemReceipts.value.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0);
    } else {
      totalAmount = manualReceipts.value.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0);
    }
    
    formData.amount = totalAmount;
  }
};

// 处理上传凭证
const handleUploadVoucher = (options) => {
  const { file } = options;
  
  // 检查文件类型
  const acceptTypes = ['.jpg', '.jpeg', '.png', '.pdf'];
  const fileType = file.name.substring(file.name.lastIndexOf('.'));
  if (!acceptTypes.includes(fileType.toLowerCase())) {
    Message.error('只支持 jpg、jpeg、png、pdf 格式的文件');
    return;
  }
  
  // 检查文件大小
  if (file.size > 2 * 1024 * 1024) { // 2MB
    Message.error('文件大小不能超过2MB');
    return;
  }
  
  // 检查上传数量限制
  if (formData.voucherList.length >= 5) {
    Message.error('最多只能上传5张凭证');
    return;
  }
  
  // 模拟上传
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = () => {
    // 模拟上传成功，实际项目中应该调用上传API
    formData.voucherList.push({
      name: file.name,
      url: reader.result,
      file: file
    });
    Message.success('上传成功');
  };
};

// 删除凭证
const removeVoucher = (index) => {
  formData.voucherList.splice(index, 1);
};

// 显示确认弹窗
const showConfirmModal = async () => {
  try {
    const validResult = await formRef.value.validate();
    if (validResult) {
      confirmModalVisible.value = true;
    }
  } catch (error) {
    Message.error('表单验证失败，请检查填写内容');
  }
};

// 提交表单
const submitForm = () => {
  // 构建提交数据
  const submitData = {
    ...formData,
    orderList: [...orderData.value, ...pendingOrders.value],
    // 处理凭证数据，只保留必要的信息
    vouchers: formData.voucherList.map(item => ({
      name: item.name,
      url: item.url
    })),
    // 添加备注和附件信息
    remark: remarkData.value.remark,
    attachments: remarkData.value.attachments.map(item => ({
      name: item.name,
      url: item.url
    }))
  };
  
  // 实际项目中这里应该调用API提交数据
  console.log('提交数据:', submitData);
  
  Message.success('认款申请提交成功');
  router.push('/provider/finance/claim');
};
</script>

<style scoped lang="less">
.claim-application-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  
  .page-header {
    margin-bottom: 16px;
    
    h1 {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
    }
  }
  
  .page-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 16px;
    
    .section-header {
      margin-bottom: 16px;
      
      h2 {
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
        position: relative;
        padding-left: 12px;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 16px;
          background-color: rgb(var(--primary-6));
          border-radius: 2px;
        }
      }
    }
    
    .order-info-section,
    .pending-orders-section,
    .claim-form-section {
      margin-bottom: 24px;
    }
    
    .form-row {
      display: flex;
      margin: 0 -8px;
      
      .form-col {
        flex: 1;
        padding: 0 8px;
      }
    }
    
    .voucher-upload-container {
      .upload-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        border: 1px dashed var(--color-neutral-3);
        border-radius: 2px;
        cursor: pointer;
        color: var(--color-text-3);
        
        &:hover {
          color: rgb(var(--primary-6));
          border-color: rgb(var(--primary-6));
        }
        
        .upload-text {
          margin-top: 8px;
          font-size: 12px;
        }
      }
      
      .voucher-list {
        display: flex;
        flex-wrap: wrap;
        margin-top: 8px;
        gap: 8px;
        
        .voucher-item {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 2px;
          overflow: hidden;
          
          .voucher-preview {
            width: 100%;
            height: 100%;
            position: relative;
          }
          
          .delete-btn {
            position: absolute;
            top: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.5);
            color: #fff;
            border-radius: 0 0 0 4px;
            padding: 2px;
            display: none;
          }
          
          &:hover .delete-btn {
            display: block;
          }
        }
      }
    }
    
    .upload-tip {
      margin-top: 4px;
      font-size: 12px;
      color: #86909c;
    }
    
    .action-buttons {
      display: flex;
      justify-content: center;
      margin-top: 32px;
    }
  }
}</style>