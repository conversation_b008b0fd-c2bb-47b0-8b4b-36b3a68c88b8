<template>
  <a-modal
    :visible="visible"
    :title="'认款申请确认'"
    :mask-closable="false"
    :unmount-on-close="false"
    @cancel="handleCancel"
    @before-ok="handleConfirm"
    width="600px"
  >
    <div class="confirm-content">
      <div class="confirm-header">
        <a-alert type="warning" show-icon>
          <template #message>请确认以下认款信息无误，提交后将无法修改</template>
        </a-alert>
      </div>
      
      <div class="confirm-info">
        <div class="info-item">
          <span class="label">付款人名称：</span>
          <span class="value">{{ formData.payerName }}</span>
        </div>
        <div class="info-item">
          <span class="label">付款人账号：</span>
          <span class="value">{{ formData.payerAccount }}</span>
        </div>
        <div class="info-item">
          <span class="label">收款人名称：</span>
          <span class="value">{{ formData.receiverName }}</span>
        </div>
        <div class="info-item">
          <span class="label">收款人账号：</span>
          <span class="value">{{ formData.receiverAccount }}</span>
        </div>
        <div class="info-item">
          <span class="label">收款日期：</span>
          <span class="value">{{ formatDate(formData.receiptDate) }}</span>
        </div>
        <div class="info-item">
          <span class="label">收款金额：</span>
          <span class="value amount">{{ formData.amount }}</span>
        </div>
        <div class="info-item">
          <span class="label">认款备注：</span>
          <span class="value">{{ formData.remark || '无' }}</span>
        </div>
      </div>
      
      <div class="confirm-vouchers" v-if="formData.voucherList && formData.voucherList.length > 0">
        <div class="voucher-title">凭证预览：</div>
        <div class="voucher-list">
          <div class="voucher-item" v-for="(item, index) in formData.voucherList" :key="index">
            <a-image :src="item.url" width="60px" height="60px" />
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';
import { Message } from '@arco-design/web-vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'confirm']);

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  
  if (typeof date === 'string') {
    return date;
  }
  
  try {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    return '';
  }
};

// 取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 确认提交
const handleConfirm = (done) => {
  emit('confirm');
  done();
};
</script>

<style scoped lang="less">
.confirm-content {
  padding: 8px 0;
  
  .confirm-header {
    margin-bottom: 16px;
  }
  
  .confirm-info {
    margin-bottom: 16px;
    
    .info-item {
      display: flex;
      margin-bottom: 8px;
      
      .label {
        width: 100px;
        color: #86909c;
        text-align: right;
        padding-right: 12px;
      }
      
      .value {
        flex: 1;
        color: #1d2129;
        
        &.amount {
          color: #f53f3f;
          font-weight: 500;
        }
      }
    }
  }
  
  .confirm-vouchers {
    .voucher-title {
      margin-bottom: 8px;
      color: #86909c;
    }
    
    .voucher-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .voucher-item {
        border: 1px solid #e5e6eb;
        border-radius: 2px;
        overflow: hidden;
      }
    }
  }
}
</style>
