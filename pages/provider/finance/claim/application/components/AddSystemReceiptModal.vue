<template>
  <a-modal
    :visible="visible"
    :title="'添加系统收款记录'"
    :mask-closable="false"
    :unmount-on-close="false"
    @cancel="handleCancel"
    @before-ok="handleConfirm"
    width="1200px"
  >
    <div class="system-receipt-modal">
      <a-table 
        :data="receiptData" 
        :bordered="true" 
        :pagination="false"
        v-model:selected-keys="selectedRowKeys"
        :row-selection="{
          type: 'checkbox',
          showCheckedAll: true,
          onlyCurrent: false
        }"
        @select="handleSelect"
      >
        <template #columns>
          <a-table-column title="收款单号" data-index="receiptNo" align="center" />
          <a-table-column title="交易日期" data-index="tradeDate" align="center" />
          <a-table-column title="收款金额" data-index="amount" align="center" />
          <a-table-column title="付款人名称" data-index="payerName" align="center" />
          <a-table-column title="付款人账号" data-index="payerAccount" align="center" />
          <a-table-column title="付款人开户行" data-index="payerBank" align="center" />
          <a-table-column title="交易流水号" data-index="tradeNo" align="center" />
          <a-table-column title="收款凭证" data-index="voucher" align="center" />
          <a-table-column title="收款备注" data-index="remark" align="center" />
          <a-table-column title="录入人" data-index="inputUser" align="center" />
          <a-table-column title="收款人名称" data-index="payerName" align="center" />
        </template>
      </a-table>
      
      <div class="pagination-container">
        <a-pagination
          v-model:current="current"
          v-model:page-size="pageSize"
          :total="total"
          show-total
          show-jumper
          show-page-size
          @change="handlePageChange"
        />
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Message } from '@arco-design/web-vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'confirm']);

// 分页参数
const current = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 选中的行
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

// 表格数据
const receiptData = ref([]);

// 处理选择行事件
const handleSelect = (rowKeys, rows) => {
  selectedRowKeys.value = rowKeys || [];
  selectedRows.value = rows || [];
  console.log('选中的行:', selectedRows.value);
};

// 模拟加载数据
const loadData = () => {
  // 模拟数据，实际项目中应该从API获取
  receiptData.value = [
    {
      receiptNo: 'SKDZ4051517525',
      tradeDate: '2024-06-15 11:37:00',
      amount: '400.00',
      payerName: '广东八维科技有限公司',
      payerAccount: '3700023590808100314',
      payerBank: '中国银行',
      tradeNo: '****************',
      voucher: '收据编号***********',
      remark: '预付款项',
      inputUser: '张三',
      receiverName: '深圳市科技有限公司',
      receiverAccount: '****************',
      receiverBank: '建设银行'
    },
    {
      receiptNo: 'SKDZ4051517526',
      tradeDate: '2024-06-16 09:25:12',
      amount: '1200.00',
      payerName: '北京创新科技有限公司',
      payerAccount: '****************',
      payerBank: '工商银行',
      tradeNo: '****************',
      voucher: '收据编号***********',
      remark: '服务费',
      inputUser: '李四',
      receiverName: '深圳市科技有限公司',
      receiverAccount: '****************',
      receiverBank: '建设银行'
    },
    {
      receiptNo: 'SKDZ4051517527',
      tradeDate: '2024-06-17 14:30:45',
      amount: '850.00',
      payerName: '上海数字科技有限公司',
      payerAccount: '****************',
      payerBank: '农业银行',
      tradeNo: '****************',
      voucher: '收据编号***********',
      remark: '技术服务费',
      inputUser: '王五',
      receiverName: '深圳市科技有限公司',
      receiverAccount: '****************',
      receiverBank: '建设银行'
    },
    {
      receiptNo: 'SKDZ4051517528',
      tradeDate: '2024-06-18 16:45:22',
      amount: '3500.00',
      payerName: '广州互联网科技有限公司',
      payerAccount: '****************',
      payerBank: '招商银行',
      tradeNo: '****************',
      voucher: '收据编号***********',
      remark: '年度服务费',
      inputUser: '赵六',
      receiverName: '深圳市科技有限公司',
      receiverAccount: '****************',
      receiverBank: '建设银行'
    },
    {
      receiptNo: 'SKDZ4051517529',
      tradeDate: '2024-06-19 10:15:36',
      amount: '1800.00',
      payerName: '深圳电子科技有限公司',
      payerAccount: '****************',
      payerBank: '交通银行',
      tradeNo: '****************',
      voucher: '收据编号***********',
      remark: '产品采购款',
      inputUser: '钱七',
      receiverName: '深圳市科技有限公司',
      receiverAccount: '****************',
      receiverBank: '建设银行'
    }
  ];
  
  total.value = receiptData.value.length;
};

// 处理页码变化
const handlePageChange = (page) => {
  current.value = page;
  loadData();
};

// 取消
const handleCancel = () => {
  selectedRowKeys.value = [];
  selectedRows.value = [];
  emit('update:visible', false);
};

// 确认提交
const handleConfirm = (done) => {
  // 确保 selectedRows.value 不为 undefined
  if (!selectedRows.value) {
    selectedRows.value = [];
  }
  
  console.log('提交时选中的行数:', selectedRows.value.length);
  
  if (selectedRows.value.length === 0) {
    Message.warning('请至少选择一条收款记录');
    done(false);
    return;
  }
  
  emit('confirm', selectedRows.value);
  selectedRowKeys.value = [];
  selectedRows.value = [];
  done();
};

// 组件挂载时加载数据
loadData();
</script>

<style scoped lang="less">
.system-receipt-modal {
  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
