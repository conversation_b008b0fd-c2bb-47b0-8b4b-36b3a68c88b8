<template>
  <a-modal
    :visible="visible"
    :title="'添加非本司订单'"
    :mask-closable="false"
    :unmount-on-close="false"
    @cancel="handleCancel"
    @before-ok="handleConfirm"
    width="500px"
  >
    <a-form :model="formData" ref="formRef" :rules="rules" layout="vertical">
      <a-form-item field="orderNo" label="订单编号" :rules="[{ required: true, message: '请输入订单编号' }]">
        <a-input v-model="formData.orderNo" placeholder="请输入订单编号" allow-clear />
      </a-form-item>
      
      <a-form-item field="totalPrice" label="订单总价" :rules="[{ required: true, message: '请输入订单总价' }]">
        <a-input-number
          v-model="formData.totalPrice"
          placeholder="请输入订单总价"
          :precision="2"
          :min="0"
          :step="0.01"
          mode="button"
          style="width: 100%"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'confirm']);

// 表单引用
const formRef = ref(null);

// 表单数据
const formData = reactive({
  orderNo: '',
  totalPrice: null
});

// 表单验证规则
const rules = {
  orderNo: [
    { required: true, message: '请输入订单编号' }
  ],
  totalPrice: [
    { required: true, message: '请输入订单总价' },
    { 
      validator: (value, cb) => {
        if (value <= 0) {
          cb('订单总价必须大于0');
        }
        cb();
      }
    }
  ]
};

// 取消
const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

// 确认提交
const handleConfirm = async (done) => {
  try {
    const validResult = await formRef.value.validate();
    if (validResult) {
      emit('confirm', { ...formData });
      resetForm();
      done();
    } else {
      done(false);
    }
  } catch (error) {
    done(false);
  }
};

// 重置表单
const resetForm = () => {
  formData.orderNo = '';
  formData.totalPrice = null;
  formRef.value?.resetFields();
};
</script>

<style scoped lang="less">
// 弹窗样式
</style>
