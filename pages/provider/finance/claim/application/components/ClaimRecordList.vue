<template>
  <div class="claim-record-list">
    <div class="section-header">
      <h2>{{ title }}</h2>
      <div class="header-actions">
        <a-button type="primary" size="small" @click="handleAdd">
          <template #icon>
            <icon-plus />
          </template>
          添加记录
        </a-button>
      </div>
    </div>
    
    <div class="record-table-container">
      <a-table :data="recordData" :bordered="true" :pagination="false">
        <template #columns>
          <a-table-column title="订单编号" data-index="orderNo" align="center" />
          <a-table-column title="订单总价" data-index="totalPrice" align="center" />
          <a-table-column title="操作" align="center">
            <template #cell="{ record }">
              <a-link status="danger" @click="handleRemove(record)">删除</a-link>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    
    <div class="total-amount">
      <span class="label">合计：</span>
      <span class="value">{{ totalAmount }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  records: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: '认款记录'
  }
});

const emit = defineEmits(['remove', 'add']);

// 记录数据
const recordData = ref([]);

// 监听外部传入的记录数据变化
watch(() => props.records, (newVal) => {
  recordData.value = newVal;
}, { immediate: true, deep: true });

// 计算总金额
const totalAmount = computed(() => {
  if (!recordData.value || recordData.value.length === 0) {
    return '0.00';
  }
  
  const total = recordData.value.reduce((sum, item) => {
    const price = parseFloat(item.totalPrice) || 0;
    return sum + price;
  }, 0);
  
  return total.toFixed(2);
});

// 删除记录
const handleRemove = (record) => {
  emit('remove', record);
};

// 添加记录
const handleAdd = () => {
  emit('add');
};
</script>

<style scoped lang="less">
.claim-record-list {
  margin-bottom: 24px;
  
  .section-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: rgb(var(--primary-6));
        border-radius: 2px;
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
  }
  
  .record-table-container {
    margin-bottom: 16px;
  }
  
  .total-amount {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 8px 16px;
    
    .label {
      font-size: 14px;
      color: #1d2129;
      margin-right: 8px;
    }
    
    .value {
      font-size: 16px;
      font-weight: 500;
      color: #f53f3f;
    }
  }
}
</style>
