<template>
  <a-modal
    :visible="visible"
    :title="'认款备注'"
    :mask-closable="false"
    :unmount-on-close="false"
    @cancel="handleCancel"
    @before-ok="handleSubmit"
  >
    <a-form :model="formData" ref="formRef" layout="vertical">
      <a-form-item field="remark" label="认款备注">
        <a-textarea 
          v-model="formData.remark" 
          placeholder="请输入认款备注信息" 
          :max-length="500" 
          show-word-limit 
          :auto-size="{ minRows: 4, maxRows: 6 }" 
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  claimData: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit']);

// 表单引用
const formRef = ref(null);

// 表单数据
const formData = reactive({
  remark: ''
});

// 监听数据变化，初始化表单
watch(() => props.claimData, (newVal) => {
  if (newVal && newVal.remark) {
    formData.remark = newVal.remark;
  } else {
    formData.remark = '';
  }
}, { immediate: true });

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
};

// 提交表单
const handleSubmit = async (done) => {
  try {
    // 构建提交数据
    const submitData = {
      id: props.claimData.id,
      remark: formData.remark
    };
    
    // 提交表单
    emit('submit', submitData);
    Message.success('认款备注保存成功');
    handleCancel();
    done();
  } catch (error) {
    Message.error('提交失败，请重试');
    done(false);
  }
};
</script>

<style scoped lang="less">
</style>
