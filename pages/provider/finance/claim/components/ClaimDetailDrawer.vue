<template>
  <a-drawer
    :visible="visible"
    :width="800"
    :unmount-on-close="false"
    @cancel="handleClose"
    @close="handleClose"
    :footer="false"
    title="认款申请详情"
  >
    <div class="claim-detail-container">
      <a-descriptions :data="descriptionData" :column="2" title="基本信息" bordered />
      
      <div class="section-title">认款进度</div>
      <a-steps :current="getStepsCurrent(claimData)" direction="vertical">
        <a-step title="提交认款申请" :description="getStepDescription(claimData, 'apply')" />
        <a-step title="审核" :description="getStepDescription(claimData, 'audit')" />
        <a-step title="完成" :description="getStepDescription(claimData, 'complete')" />
      </a-steps>
      
      <div class="section-title">认款备注</div>
      <div class="remark-content">
        {{ claimData.remark || '暂无备注' }}
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { computed } from 'vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  claimData: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'close']);

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 描述列表数据
const descriptionData = computed(() => {
  return [
    {
      label: '订单编号',
      value: props.claimData.orderNo || '-'
    },
    {
      label: '商户名称',
      value: props.claimData.merchantName || '-'
    },
    {
      label: '订单来源',
      value: props.claimData.orderSource || '-'
    },
    {
      label: '订单状态',
      value: props.claimData.orderStatus || '-'
    },
    {
      label: '订单实付金额',
      value: props.claimData.actualAmount ? `¥${props.claimData.actualAmount}` : '-'
    },
    {
      label: '跟单员',
      value: props.claimData.merchandiser || '-'
    },
    {
      label: '收货人',
      value: props.claimData.receiver || '-'
    },
    {
      label: '收货地址',
      value: props.claimData.receiverAddress || '-'
    },
    {
      label: '下单时间',
      value: props.claimData.orderTime || '-'
    },
    {
      label: '认款申请人',
      value: props.claimData.applicant || '-'
    },
    {
      label: '认款申请单号',
      value: props.claimData.claimNo || '-'
    },
    {
      label: '认款申请时间',
      value: props.claimData.applyTime || '-'
    },
    {
      label: '付款人名称',
      value: props.claimData.payerName || '-'
    }
  ];
});

// 获取步骤条当前步骤
const getStepsCurrent = (data) => {
  const status = data.claimStatus;
  if (!status) return 0;
  
  // 根据状态返回当前步骤
  if (['待认款'].includes(status)) {
    return 0;
  } else if (['待审核', '审核驳回'].includes(status)) {
    return 1;
  } else if (['审核通过', '无需认款', '单据异常'].includes(status)) {
    return 2;
  }
  return 0;
};

// 获取步骤描述
const getStepDescription = (data, step) => {
  if (!data) return '';
  
  switch (step) {
    case 'apply':
      return data.applyTime ? `申请人：${data.applicant || '-'}，申请时间：${data.applyTime}` : '未提交申请';
    case 'audit':
      if (data.claimStatus === '审核驳回') {
        return `审核人：${data.auditor || '-'}，审核时间：${data.auditTime || '-'}，结果：驳回`;
      } else if (data.claimStatus === '待审核') {
        return '等待审核';
      } else if (['审核通过', '无需认款', '单据异常'].includes(data.claimStatus)) {
        return `审核人：${data.auditor || '-'}，审核时间：${data.auditTime || '-'}`;
      }
      return '未审核';
    case 'complete':
      if (['审核通过', '无需认款', '单据异常'].includes(data.claimStatus)) {
        return `完成时间：${data.completeTime || '-'}，状态：${data.claimStatus}`;
      }
      return '未完成';
    default:
      return '';
  }
};
</script>

<style scoped lang="less">
.claim-detail-container {
  padding: 16px 0;
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin: 24px 0 16px;
    color: #1d2129;
  }
  
  .remark-content {
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    min-height: 80px;
    color: #4e5969;
  }
}
</style>
