<template>
  <div>
    <a-drawer
      :visible="visible"
      :width="800"
      :title="'开票申请详情'"
      @cancel="handleClose"
      @close="handleClose"
      unmountOnClose
    >
      <div class="invoice-detail-container">
        <!-- 顶部主要信息 -->
        <div class="invoice-header">
          <div class="invoice-header-item">
            <span class="label">客户名称：</span>
            <span class="value">{{ props.invoiceData?.customerName || '--' }}</span>
          </div>
          <div class="invoice-header-item">
            <span class="label">订单编号：</span>
            <span class="value">{{ props.invoiceData?.orderNo || '--' }}</span>
          </div>
          <div class="invoice-header-item">
            <span class="label">发票类型：</span>
            <span class="value">
              <a-tag :color="getInvoiceTypeColor(props.invoiceData?.invoiceType)">
                {{ getInvoiceTypeName(props.invoiceData?.invoiceType) }}
              </a-tag>
            </span>
          </div>
        </div>
        
        <!-- 发票列表 -->
        <div class="invoice-table-container">
          <div class="section-title">发票信息</div>
          <a-table :data="invoiceTableData" :loading="false" :pagination="false">
            <template #columns>
              <a-table-column title="发票号码" data-index="invoiceNo" />
              <a-table-column title="发票抬头" data-index="invoiceTitle" />
              <a-table-column title="税价合计" data-index="totalAmount">
                <template #cell="{ record }">
                  <span>¥{{ record.totalAmount || '0.00' }}</span>
                </template>
              </a-table-column>
              <a-table-column title="开票时间" data-index="invoiceTime" />
              <a-table-column title="开票状态" data-index="invoiceStatus">
                <template #cell="{ record }">
                  <a-tag :color="getInvoiceStatusColor(record.invoiceStatus)">
                    {{ getInvoiceStatusName(record.invoiceStatus) }}
                  </a-tag>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
        
        <!-- 基本信息 -->
        <a-descriptions :data="invoiceDetailData" title="订单基本信息" layout="inline-vertical" :column="2" />
        
        <!-- 审核信息 -->
        <a-descriptions :data="invoiceStatusData" title="审核信息" layout="inline-vertical" :column="2" />
        
        <!-- 金额信息 -->
        <a-descriptions :data="invoiceAmountData" title="金额信息" layout="inline-vertical" :column="2" />
      </div>
      
      <template #footer>
        <div class="footer-btns">
          <a-button @click="handleClose">关闭</a-button>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  invoiceData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'close']);

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 基本信息
const invoiceDetailData = computed(() => {
  // 如果 invoiceData 为 null 或者 undefined，返回空数组
  if (!props.invoiceData) {
    return [];
  }
  
  return [
    {
      label: '订单号',
      value: props.invoiceData.orderNo || '--'
    },
    {
      label: '订单来源',
      value: props.invoiceData.orderSource || '--'
    },
    {
      label: '买家/收货人',
      value: props.invoiceData.buyer || '--'
    },
    {
      label: '业务员',
      value: props.invoiceData.salesman || '--'
    },
    {
      label: '订单状态',
      value: props.invoiceData.orderStatus || '--'
    },
    {
      label: '跟单员',
      value: props.invoiceData.merchandiser || '--'
    }
  ];
});

// 审核信息
const invoiceStatusData = computed(() => {
  // 如果 invoiceData 为 null 或者 undefined，返回空数组
  if (!props.invoiceData) {
    return [];
  }
  
  return [
    {
      label: '开票申请时间',
      value: props.invoiceData.applyTime || '--'
    },
    {
      label: '商务审核状态',
      value: props.invoiceData.businessAuditStatus || '--'
    },
    {
      label: '商务审核时间',
      value: props.invoiceData.businessAuditTime || '--'
    },
    {
      label: '商务审核人',
      value: props.invoiceData.businessAuditor || '--'
    },
    {
      label: '财务审核状态',
      value: props.invoiceData.financeAuditStatus || '--'
    },
    {
      label: '财务审核时间',
      value: props.invoiceData.financeAuditTime || '--'
    },
    {
      label: '财务审核人',
      value: props.invoiceData.financeAuditor || '--'
    }
  ];
});

// 金额信息
const invoiceAmountData = computed(() => {
  // 如果 invoiceData 为 null 或者 undefined，返回空数组
  if (!props.invoiceData) {
    return [];
  }
  
  return [
    {
      label: '价税合计金额',
      value: props.invoiceData.totalAmount ? `¥${props.invoiceData.totalAmount}` : '--'
    },
    {
      label: '税额',
      value: props.invoiceData.taxAmount ? `¥${props.invoiceData.taxAmount}` : '--'
    },
    {
      label: '不含税金额',
      value: props.invoiceData.amountWithoutTax ? `¥${props.invoiceData.amountWithoutTax}` : '--'
    }
  ];
});

// 发票信息
const invoiceTypeData = computed(() => {
  // 如果 invoiceData 为 null 或者 undefined，返回空数组
  if (!props.invoiceData) {
    return [];
  }
  
  return [
    {
      label: '发票号',
      value: props.invoiceData.invoiceNo || '--'
    },
    {
      label: '发票抬头',
      value: props.invoiceData.invoiceTitle || '--'
    },
    {
      label: '发票类型',
      value: props.invoiceData.invoiceType || '--'
    },
    {
      label: '发票状态',
      value: props.invoiceData.invoiceStatus || '--'
    }
  ];
});

// 发票表格数据
const invoiceTableData = computed(() => {
  // 如果 invoiceData 为 null 或者 undefined，返回空数组
  if (!props.invoiceData) {
    return [];
  }
  
  return [
    {
      invoiceNo: props.invoiceData.invoiceNo || '--',
      invoiceTitle: props.invoiceData.invoiceTitle || '--',
      totalAmount: props.invoiceData.totalAmount || '--',
      invoiceTime: props.invoiceData.invoiceTime || '--',
      invoiceStatus: props.invoiceData.invoiceStatus || '--'
    }
  ];
});

// 发票状态转换函数
const getInvoiceStatusName = (status) => {
  switch (status) {
    case 'pending':
      return '待开票';
    case 'processing':
      return '开票中';
    case 'success':
      return '已开票';
    case 'failed':
      return '开票失败';
    default:
      return '--';
  }
};

// 发票状态颜色转换函数
const getInvoiceStatusColor = (status) => {
  switch (status) {
    case 'pending':
      return 'blue';
    case 'processing':
      return 'orange';
    case 'success':
      return 'green';
    case 'failed':
      return 'red';
    default:
      return 'gray';
  }
};

// 发票类型转换函数
const getInvoiceTypeName = (type) => {
  switch (type) {
    case 'vat':
      return '增值税发票';
    case 'non-vat':
      return '非增值税发票';
    default:
      return '--';
  }
};

// 发票类型颜色转换函数
const getInvoiceTypeColor = (type) => {
  switch (type) {
    case 'vat':
      return 'blue';
    case 'non-vat':
      return 'orange';
    default:
      return 'gray';
  }
};
</script>

<style scoped lang="less">
.invoice-detail-container {
  padding: 16px;
  
  .invoice-header {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 24px;
    
    .invoice-header-item {
      flex: 1;
      min-width: 200px;
      margin-bottom: 12px;
      
      .label {
        font-weight: 500;
        margin-right: 8px;
        color: #666;
      }
      
      .value {
        font-weight: 600;
        color: #333;
      }
    }
  }
  
  .invoice-table-container {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      padding-left: 8px;
      border-left: 4px solid #165dff;
    }
  }
  
  :deep(.arco-descriptions-title) {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-left: 8px;
    border-left: 4px solid #165dff;
  }
  
  :deep(.arco-descriptions) {
    margin-bottom: 24px;
  }
}

.footer-btns {
  display: flex;
  justify-content: flex-end;
}
</style>
