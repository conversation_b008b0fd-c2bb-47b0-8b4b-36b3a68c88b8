<template>
  <div class="invoice-apply-container">
    <!-- 顶部操作栏 -->
    <div class="page-header">
      <a-space>
        <!-- 返回按钮 -->
        <a-button @click="goBack">
          <template #icon>
            <icon-arrow-left />
          </template>
          返回列表
        </a-button>
        <div class="page-title">申请开票</div>
      </a-space>
    </div>

    <!-- 表单容器 -->
    <div class="form-container">
      <!-- 订单信息卡片 -->
      <a-card class="mb-4">
        <template #title>
          <div class="card-title">
            <icon-file-invoice class="mr-2" />订单信息
          </div>
        </template>
        <a-descriptions :data="orderInfo" :column="{ xs: 1, sm: 2, md: 3 }" />
      </a-card>

      <!-- 发票信息卡片 -->
      <a-card>
        <template #title>
          <div class="card-title">
            <icon-file-invoice class="mr-2" />发票信息
          </div>
        </template>

        <!-- 发票基础信息（类型、抬头等） -->
        <div class="invoice-base-info">
          <a-form
            ref="formRef"
            :model="formData"
            :label-width="formOptions.labelWidth"
            layout="horizontal"
            size="medium"
          >
            <!-- 发票类型 -->
            <a-form-item label="发票类型" :required="true" >
              <a-select
                v-model="formData.invoiceType"
                placeholder="请选择发票类型"
              >
                <a-option value="电子普票">电子普票</a-option>
                <a-option value="电子专票">电子专票</a-option>
                <a-option value="纸质普票">纸质普票</a-option>
                <a-option value="纸质专票">纸质专票</a-option>
              </a-select>
            </a-form-item>

            <!-- 发票抬头 -->
            <a-form-item label="发票抬头" :required="true" >
              <a-select
                v-model="formData.invoiceTitle"
                placeholder="请选择发票抬头"
              >
                <!-- 可通过接口动态加载 options，这里模拟静态数据 -->
                <a-option value="抬头1">抬头1</a-option>
                <a-option value="抬头2">抬头2</a-option>
              </a-select>
            </a-form-item>

            <!-- 内部备注 -->
            <a-form-item label="内部备注" >
              <a-textarea
                v-model="formData.remark"
                placeholder="请输入内容"
                :max-length="200"
                show-word-limit
                rows="4"
              />
            </a-form-item>
          </a-form>
        </div>

        <!-- 商品明细表格 -->
        <div class="goods-table-section">
          <div class="table-header">
            <p class="table-tip">
              我司将按照审核通过后的销项发票申请明细（包括税收分类、商品名称、规格型号、单位等）开具销项发票，供应商请严格按此明细开具进项发票，不一致的将退回申请！
            </p>
            <!-- <a-button type="primary" class="add-goods-btn" @click="addNewRow">
              <template #icon>
                <icon-plus />
              </template>
              添加商品
            </a-button> -->
          </div>
          <a-table
            :columns="tableColumns"
            :data="tableData"
            bordered
            :pagination="false"
            style="margin-top: 16px"
          >
            <!-- 操作列 - 删除按钮 -->
            <template #operation="{ rowIndex }">
              <a-button
                type="text"
                status="danger"
                @click="removeRow(rowIndex)"
              >
                <icon-delete />
              </a-button>
            </template>

            <!-- 可编辑单元格 -->
            <template #invoiceName="{ record }">
              <a-input v-model="record.invoiceName" placeholder="请输入开票名称" />
            </template>

            <template #quantity="{ record }">
              <a-input-number
                v-model="record.quantity"
                placeholder="数量"
                :min="1"
                @change="updateRowCalculations(record)"
              />
            </template>

            <template #untaxedPrice="{ record }">
              <a-input-number
                v-model="record.untaxedPrice"
                placeholder="未税单价"
                :precision="2"
                :min="0"
                @change="updateRowCalculations(record)"
              />
            </template>

            <template #taxRate="{ record }">
              <a-input-number
                v-model="record.taxRate"
                placeholder="税率"
                :precision="0"
                :min="0"
                :max="100"
                @change="updateRowCalculations(record)"
              />
            </template>
          </a-table>

          <!-- 总价统计行 -->
          <div class="total-row">
            <span>总价</span>
            <span>{{ totalCount }}.00</span>
            <span>{{ totalUntaxedAmount }}</span>
            <span>{{ totalTax }}</span>
            <span>{{ totalTaxedAmount }}</span>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <a-button @click="goBack">返回</a-button>
            <a-button type="primary" @click="handleConfirm">确认</a-button>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRouter, useRoute } from '#app';
// 定义页面路由元信息
definePageMeta({
  name: 'provider-finance-invoice-apply',
  title: '申请开票',
  icon: 'icon-file-invoice',
});

// 路由
const router = useRouter();
const route = useRoute();

// 订单ID（从路由参数获取）
const orderId = ref(route.query.orderId || 'test202505191747651036883'); // 模拟订单号，实际可从接口动态获取

// 订单信息（模拟接口返回，实际需对接真实接口）
const orderInfo = ref([
  { label: '订单号', value: orderId.value },
  { label: '订单来源', value: '君网HS' },
  { label: '订单状态', value: '待开票' }, // 可根据实际状态调整
  { label: '买家', value: '示例买家' },
  { label: '销售员', value: '示例销售员' },
  { label: '订单金额', value: '¥ 4618.15' }, // 与表格总价对齐
  { label: '已开票金额', value: '¥ 0.00' },
  { label: '可开票金额', value: '¥ 4618.15' },
]);

// 表单数据
const formData = ref({
  invoiceType: '电子普票', // 默认值与目标页对齐
  invoiceTitle: '',
  remark: '',
});

// 表单配置
const formOptions = reactive({
  labelWidth: 60,
  layout: 'horizontal',
  size: 'medium',
  rules: {
    invoiceType: [{ required: true, message: '请选择发票类型' }],
    invoiceTitle: [{ required: true, message: '请选择发票抬头' }],
  },
});

// 商品明细表格列定义
const tableColumns = reactive([
  { title: '操作', slotName: 'operation', width: 80 },
  { title: '税收分类编码', dataIndex: 'taxCode' },
  { title: '税收简称', dataIndex: 'taxName' },
  { title: '商品名称', dataIndex: 'goodsName' },
  { title: '开票名称', dataIndex: 'invoiceName', slotName: 'invoiceName' },
  { title: '规格型号', dataIndex: 'model' },
  { title: '单位', dataIndex: 'unit' },
  { title: '数量', dataIndex: 'quantity', align: 'right', slotName: 'quantity' },
  { title: '未税单价', dataIndex: 'untaxedPrice', align: 'right', slotName: 'untaxedPrice' },
  { title: '未税金额', dataIndex: 'untaxedAmount', align: 'right' },
  { title: '税率 (%)', dataIndex: 'taxRate', align: 'right', slotName: 'taxRate' },
  { title: '税额', dataIndex: 'tax', align: 'right' },
  { title: '含税金额', dataIndex: 'taxedAmount', align: 'right' },
]);

// 商品明细表格数据（模拟目标页内容，实际从接口获取）
const tableData = ref([
  {
    index: 1,
    taxCode: '4010100000000000000',
    taxName: '技术',
    goodsName: '普通电话机 中港...',
    invoiceName: '',
    model: 'C259',
    unit: '台',
    quantity: 15,
    untaxedPrice: 33.80,
    untaxedAmount: 506.95,
    taxRate: 13,
    tax: 65.90,
    taxedAmount: 572.85,
  },
  {
    index: 2,
    taxCode: '4010100000000000000',
    taxName: '技术',
    goodsName: '电线/电缆 扎带/扎...',
    invoiceName: '',
    model: 'LJ-DPH20',
    unit: '米',
    quantity: 12,
    untaxedPrice: 148.67,
    untaxedAmount: 1784.07,
    taxRate: 13,
    tax: 231.93,
    taxedAmount: 2016.00,
  },
  {
    index: 3,
    taxCode: '1060512020000000000',
    taxName: '日用杂品',
    goodsName: '刷子 江赛JC-SZZ...',
    invoiceName: '',
    model: 'JC-SZZC30B',
    unit: '把',
    quantity: 10,
    untaxedPrice: 148.67,
    untaxedAmount: 1486.73,
    taxRate: 13,
    tax: 193.27,
    taxedAmount: 1680.00,
  },
  {
    index: 4,
    taxCode: '4010200000000000000',
    taxName: '技术',
    goodsName: '网线/烽火FiberH...',
    invoiceName: '',
    model: 'CAT6A',
    unit: '袋',
    quantity: 7,
    untaxedPrice: 44.16,
    untaxedAmount: 309.12,
    taxRate: 13,
    tax: 40.18,
    taxedAmount: 349.30,
  },
]);

// 总价统计（计算属性动态汇总）
const totalCount = computed(() => tableData.value.reduce((sum, item) => sum + item.quantity, 0));
const totalUntaxedAmount = computed(() => tableData.value.reduce((sum, item) => sum + item.untaxedAmount, 0).toFixed(2));
const totalTax = computed(() => tableData.value.reduce((sum, item) => sum + item.tax, 0).toFixed(2));
const totalTaxedAmount = computed(() => tableData.value.reduce((sum, item) => sum + item.taxedAmount, 0).toFixed(2));

// 表单引用
const formRef = ref(null);

// 返回列表页
const goBack = () => {
  router.push('/provider/finance/invoice');
};

// 加载订单数据（实际需对接接口，这里模拟）
const loadOrderData = async () => {
  if (!orderId.value) {
    Message.error('订单ID不能为空');
    return;
  }
  // 模拟接口请求：const res = await api.getOrderDetail(orderId.value)
  // 若有真实接口，替换下方模拟逻辑，将 res 数据赋值给 orderInfo、tableData 等
  setTimeout(() => {
    console.log('订单数据加载完成');
  }, 500);
};

// 添加新行
const addNewRow = () => {
  // 创建新的空白行
  const newIndex = tableData.value.length > 0 ? Math.max(...tableData.value.map(item => item.index)) + 1 : 1;
  const newRow = {
    index: newIndex,
    taxCode: '',
    taxName: '',
    goodsName: '',
    invoiceName: '',
    model: '',
    unit: '',
    quantity: 1,
    untaxedPrice: 0,
    untaxedAmount: 0,
    taxRate: 13,
    tax: 0,
    taxedAmount: 0,
  };
  tableData.value.push(newRow);
  // 计算行的金额
  updateRowCalculations(newRow);
};

// 删除行
const removeRow = (rowIndex) => {
  tableData.value.splice(rowIndex, 1);
};

// 更新行计算
const updateRowCalculations = (record) => {
  // 计算未税金额 = 数量 * 未税单价
  record.untaxedAmount = (record.quantity * record.untaxedPrice).toFixed(2) * 1;
  // 计算税额 = 未税金额 * 税率%
  record.tax = (record.untaxedAmount * (record.taxRate / 100)).toFixed(2) * 1;
  // 计算含税金额 = 未税金额 + 税额
  record.taxedAmount = (record.untaxedAmount + record.tax).toFixed(2) * 1;
};

// 确认提交（可扩展真实接口提交逻辑）
const handleConfirm = () => {
  // 1. 校验表单
  formRef.value.validate().then(() => {
    // 2. 构造提交数据（示例）
    const submitData = {
      ...formData.value,
      orderId: orderId.value,
      goodsDetail: tableData.value, // 商品明细
      totalCount: totalCount.value,
      totalUntaxedAmount: totalUntaxedAmount.value,
      totalTax: totalTax.value,
      totalTaxedAmount: totalTaxedAmount.value,
    };
    // 3. 调用接口提交（示例）
    // api.submitInvoiceApply(submitData).then(() => {
    //   Message.success('发票申请提交成功');
    //   goBack();
    // }).catch((err) => {
    //   Message.error(err.message || '提交失败，请重试');
    // });

    // 模拟提交成功
    console.log('提交的表单数据:', submitData);
    Message.success('发票申请提交成功（模拟）');
    goBack();
  }).catch((err) => {
    Message.error(err[0].message || '表单校验失败');
  });
};

// 页面加载时初始化数据
onMounted(() => {
  loadOrderData();
});
</script>

<style scoped lang="less">
.invoice-apply-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  font-weight: 500;
  margin-left: 8px;
}

.form-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

/* 发票基础信息区样式 */
.invoice-base-info {
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 16px;
}

/* 商品明细表格区样式 */
.goods-table-section {
  & .table-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
  }
  & .table-tip {
    color: #999;
    font-size: 12px;
    margin: 0;
    padding: 8px 0;
    flex: 1;
  }
  & .add-goods-btn {
    flex-shrink: 0;
  }
  & .total-row {
    display: flex;
    justify-content: flex-end;
    gap: 40px;
    align-items: center;
    margin-top: 8px;
    font-weight: 500;
  }
  & .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 24px;
  }
}

:deep(.arco-form-item-label-col) {
  font-weight: 500;
}

:deep(.arco-form) {
  display: grid;
  grid-template-columns: repeat(2,1fr);
}

:deep(.arco-divider-text) {
  font-weight: 500;
  color: #1d2129;
}

.mb-4 {
  margin-bottom: 16px;
}

.mr-2 {
  margin-right: 8px;
}
</style>