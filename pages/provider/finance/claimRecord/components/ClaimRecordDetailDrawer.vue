<template>
  <a-drawer
    :visible="visible"
    :width="1200"
    :title="'认款申请详情'"
    :footer="false"
    @cancel="handleClose"
    unmountOnClose
  >
    <template #footer>
      <div class="drawer-footer">
        <a-button @click="handleClose">关闭</a-button>
      </div>
    </template>
    
    <div class="claim-detail-container">
      <!-- 认款申请基本信息 -->
      <div class="section-title">认款申请基本信息</div>
      <a-descriptions :data="basicInfoData" :column="2" bordered />
      
      <!-- 订单信息 -->
      <div class="section-title">订单信息</div>
      <a-table 
        :columns="orderColumns" 
        :data="orderData" 
        :pagination="false"
        bordered
        stripe
        size="small"
      />
      
      <!-- 收款信息 -->
      <div class="section-title">收款信息</div>
      <a-table 
        :columns="paymentColumns" 
        :data="paymentData" 
        :pagination="false"
        bordered
        stripe
        size="small"
      >
        <!-- 收款凭证列 -->
        <template #voucher="{ record }">
          <a-link @click="handleViewVoucher(record)">查看</a-link>
        </template>
      </a-table>
      
      <!-- 收款凭证图片预览 -->
      <a-image-preview-group>
        <a-modal
          v-model:visible="voucherVisible"
          :title="'收款凭证'"
          :footer="false"
          :mask-closable="true"
          @cancel="voucherVisible = false"
        >
          <div class="voucher-container">
            <a-image
              :src="currentVoucherUrl"
              :preview="false"
              alt="收款凭证"
              :width="400"
              fit="contain"
            />
          </div>
        </a-modal>
      </a-image-preview-group>
      
      <div class="section-title">合计</div>
      <div class="total-amount">{{ totalAmount }}</div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  recordData: {
    type: Object,
    default: () => ({})
  }
});

// 收款凭证预览相关
const voucherVisible = ref(false);
const currentVoucherUrl = ref('');

// 定义组件事件
const emit = defineEmits(['update:visible', 'close']);

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
  // 关闭凭证预览
  voucherVisible.value = false;
};

// 查看收款凭证
const handleViewVoucher = (record) => {
  // 实际项目中应该使用真实的图片URL
  // 这里使用一个示例图片URL
  currentVoucherUrl.value = '/assets/images/payment-voucher-example.png';
  voucherVisible.value = true;
};

// 认款申请基本信息
const basicInfoData = computed(() => {
  return [
    {
      label: '订单来源',
      value: props.recordData.orderSource || '--'
    },
    {
      label: '申请时间',
      value: props.recordData.applyTime || '--'
    },
    {
      label: '商户名称',
      value: props.recordData.merchantName || '--'
    },
    {
      label: '申请人',
      value: props.recordData.applicant || '--'
    },
    {
      label: '认款申请单号',
      value: props.recordData.claimNo || '--'
    },
    {
      label: '认款申请单状态',
      value: props.recordData.claimStatus || '--'
    }
  ];
});

// 订单信息表格列定义
const orderColumns = [
  {
    title: '订单编号',
    dataIndex: 'orderNo',
    align: 'center',
    width: 150
  },
  {
    title: '下单时间',
    dataIndex: 'orderTime',
    align: 'center',
    width: 150
  },
  {
    title: '收货人',
    dataIndex: 'receiver',
    align: 'center',
    width: 100
  },
  {
    title: '收货地址',
    dataIndex: 'address',
    align: 'center',
    width: 200
  },
  {
    title: '商品编码',
    dataIndex: 'productCode',
    align: 'center',
    width: 120
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    align: 'center',
    width: 200
  },
  {
    title: '成交单价',
    dataIndex: 'price',
    align: 'center',
    width: 100
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    align: 'center',
    width: 80
  }
];

// 收款信息表格列定义
const paymentColumns = [
  {
    title: '收款记录编号',
    dataIndex: 'paymentNo',
    align: 'center',
    width: 150
  },
  {
    title: '交易日期',
    dataIndex: 'paymentDate',
    align: 'center',
    width: 150
  },
  {
    title: '付款人姓名',
    dataIndex: 'payerName',
    align: 'center',
    width: 120
  },
  {
    title: '付款人账号',
    dataIndex: 'payerAccount',
    align: 'center',
    width: 150
  },
  {
    title: '付款人开户行',
    dataIndex: 'payerBank',
    align: 'center',
    width: 150
  },
  {
    title: '交易流水号',
    dataIndex: 'transactionNo',
    align: 'center',
    width: 150
  },
  {
    title: '收款金额',
    dataIndex: 'amount',
    align: 'center',
    width: 100
  },
  {
    title: '收款备注',
    dataIndex: 'remark',
    align: 'center',
    width: 150
  },
  {
    title: '收款凭证',
    dataIndex: 'voucher',
    align: 'center',
    width: 100,
    slotName: 'voucher'
  }
];

// 模拟订单数据
const orderData = computed(() => {
  // 这里应该根据实际情况从props.recordData中获取订单数据
  // 这里使用模拟数据
  return [
    {
      orderNo: '**********',
      orderTime: '2024-06-15 11:57:00',
      receiver: '李强',
      address: '福建宁德市蕉城区飞鸾大道天宏中央广场1下层4号店(5号7号99)',
      productCode: '**********',
      productName: '金测试 清透焕颜HIRUSIONⓇ DS-BCMⓇ-A6ZC 玻尿酸(0-300)乳 紫色',
      price: '200.00',
      quantity: 1
    },
    {
      orderNo: '2023110603',
      orderTime: '2024-06-15 11:57:00',
      receiver: '李强',
      address: '福建宁德市蕉城区飞鸾大道天宏中央广场1下层4号店(5号7号99)',
      productCode: '**********',
      productName: '金测试 清透焕颜HIRUSIONⓇ DS-BCMⓇ-A6ZC 玻尿酸(0-300)乳 紫色',
      price: '200.00',
      quantity: 2
    }
  ];
});

// 模拟收款数据
const paymentData = computed(() => {
  // 这里应该根据实际情况从props.recordData中获取收款数据
  // 这里使用模拟数据
  return [
    {
      paymentNo: '手动添加收款记录',
      paymentDate: '2024-06-15 11:57:00',
      payerName: '广州八杯科技发展有限公司',
      payerAccount: '3700020506081000314',
      payerBank: '中国银行',
      transactionNo: '****************',
      amount: '1000.00',
      remark: '',
      voucher: ''
    },
    {
      paymentNo: '****************',
      paymentDate: '2024-06-15 11:57:00',
      payerName: '广州八杯科技发展有限公司',
      payerAccount: '3700020506081000314',
      payerBank: '中国银行',
      transactionNo: '****************',
      amount: '200.00',
      remark: '',
      voucher: ''
    }
  ];
});

// 计算合计金额
const totalAmount = computed(() => {
  const total = paymentData.value.reduce((sum, item) => {
    return sum + parseFloat(item.amount || 0);
  }, 0);
  return `¥${total.toFixed(2)}`;
});
</script>

<style scoped lang="less">
.voucher-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.claim-detail-container {
  padding: 16px 0;
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin: 24px 0 16px;
    color: #1d2129;
    border-left: 4px solid #165DFF;
    padding-left: 10px;
  }
  
  :deep(.arco-descriptions-title) {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }
  
  :deep(.arco-descriptions-item-label) {
    font-weight: 500;
    color: #1d2129;
    background-color: #f2f3f5;
    padding: 8px 12px;
  }
  
  :deep(.arco-descriptions-item-value) {
    padding: 8px 12px;
  }
  
  :deep(.arco-table-th) {
    background-color: #f2f3f5;
    font-weight: 500;
    color: #1d2129;
  }
  
  :deep(.arco-table-td) {
    padding: 8px;
  }
  
  .total-amount {
    font-size: 16px;
    font-weight: 500;
    color: #f53f3f;
    text-align: right;
    padding: 12px 16px;
    border: 1px solid #e5e6eb;
    border-top: none;
    background-color: #f7f8fa;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
}
</style>
