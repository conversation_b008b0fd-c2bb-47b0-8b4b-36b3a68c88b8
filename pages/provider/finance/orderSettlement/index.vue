<template>
  <div class="claim-record-container">
    <div class="page-content">
      <!-- 详情抽屉 -->
      
      <div class="search-area">
        <!-- 状态标签列表 -->
        <div class="status-tabs-container">
          <a-tabs type="line" :active-key="activeStatus" @change="selectStatusTab">
            <a-tab-pane v-for="tab in statusTabs" :key="tab.key" :title="tab.name">
              <template #title>
                <span class="status-tab-title">{{ tab.name }}</span>
                <span class="status-tab-count">{{ tab.count || 0 }}</span>
              </template>
            </a-tab-pane>
          </a-tabs>
        </div>
        
        <!-- 搜索表单和表格 -->
        <ma-crud
          ref="crudRef"
          :options="crudOptions"
          :columns="columns"
          :search="search"
          v-model:search-params="searchParams"
          :data="tableData"
          :loading="loading"
        >
          <!-- 操作列 -->
          <template #operation="{ record }">
            <a-space>
              <a-link @click="handleViewAuditRecord(record)">审核记录</a-link>
              <a-link @click="handleView(record)">查看详情</a-link>
              <!-- 根据状态显示不同操作按钮 -->
              <template v-if="['待平台开票', '平台未回款', '待服务商开票'].includes(record.claimStatus)">
                <a-button size="mini" type="primary" @click="handleUploadInvoice(record)">进项票上传</a-button>
              </template>
              <template v-if="record.claimStatus === '待结算'">
                <a-button size="mini" type="primary" @click="handleSettlement(record)">结算</a-button>
              </template>
            </a-space>
          </template>
          
          <!-- 订单状态列 -->
          <template #claimStatus="{ record }">
            <a-tag :color="getClaimStatusColor(record.claimStatus)">
              {{ record.claimStatus }}
            </a-tag>
          </template>
        </ma-crud>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "#app";

// 定义页面路由元信息
definePageMeta({
  name: 'provider-finance-order-settlement',
  title: "订单结算",
  icon: "icon-money",
});

// 路由
const router = useRouter();

// 抽屉可见性
const drawerVisible = ref(false);
const currentRecord = ref({});

// 查看详情
const handleView = (record) => {
  currentRecord.value = record;
  drawerVisible.value = true;
};

// 关闭抽屉
const handleDrawerClose = () => {
  drawerVisible.value = false;
};

// 查看审核记录
const handleViewAuditRecord = (record) => {
  Message.info(`查看审核记录：${record.claimNo}`);
  // 这里可以实现查看审核记录的逻辑
};

// 进项票上传
const handleUploadInvoice = (record) => {
  Message.info(`上传进项票：${record.claimNo}`);
  // 这里可以实现进项票上传的逻辑
};

// 结算操作
const handleSettlement = (record) => {
  Message.info(`处理结算：${record.claimNo}`);
  // 这里可以实现结算的逻辑
};

// 选择状态标签
const selectStatusTab = (key) => {
  activeStatus.value = key;
  
  // 根据选中的标签设置搜索参数
  if (key === 'all') {
    searchParams.value.claimStatus = '';
  } else {
    const statusMap = {
      'auditing': '待平台开票',
      'rejected': '平台未回款',
      'approved': '待服务商开票',
      'abnormal1': '待结算',
      'abnormal2': '已结算',
    };
    searchParams.value.claimStatus = statusMap[key];
  }
  
  // 重置页码并重新加载数据
  currentPage.value = 1;
  loadData();
};

// 获取认款状态颜色
const getClaimStatusColor = (status) => {
  const colorMap = {
    '待平台开票': 'blue',
    '平台未回款': 'red',
    '待服务商开票': 'green',
    '待结算': 'orange',
    '已结算': 'gray',
  };
  return colorMap[status] || 'gray';
};

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);
// 当前页码
const currentPage = ref(1);
// 每页条数
const pageSize = ref(10);
// 总条数
const total = ref(0);

// 生成模拟数据
const getMockClaimRecordData = () => {
  const generateClaimRecordItem = (index) => {
    // 调整状态选项与标签匹配
    const statusOptions = ['待平台开票', '平台未回款', '待服务商开票', '待结算', '已结算'];
    const orderSourceOptions = ['君网-官网', '君网-小程序', '天猫-旗舰店', '天猫-专卖店', '京东-自营', '京东-POP'];
    const merchantNames = ['上海某某科技有限公司', '北京某某贸易有限公司', '广州某某电子商务有限公司', '深圳某某网络科技有限公司'];
    const applicants = ['张三', '李四', '王五', '赵六', '钱七'];
    
    const statusKey = statusOptions[Math.floor(Math.random() * statusOptions.length)];
    const orderSource = orderSourceOptions[Math.floor(Math.random() * orderSourceOptions.length)];
    const merchantName = merchantNames[Math.floor(Math.random() * merchantNames.length)];
    const applicant = applicants[Math.floor(Math.random() * applicants.length)];
    
    // 生成申请时间（过去30天内的随机时间）
    const applyDate = new Date();
    applyDate.setDate(applyDate.getDate() - Math.floor(Math.random() * 30));
    const applyTime = applyDate.toISOString().split('T')[0];
    
    // 生成审核时间（申请时间后1-3天）
    const auditDate = new Date(applyDate);
    auditDate.setDate(auditDate.getDate() + Math.floor(Math.random() * 3) + 1);
    const auditTime = auditDate.toISOString().split('T')[0];
    
    // 生成随机金额（1000-10000之间）
    const amount = (Math.random() * 9000 + 1000).toFixed(2);
    
    return {
      id: `CR${100000 + index}`,
      claimNo: `CL${new Date().getFullYear()}${String(Math.floor(Math.random() * 1000000)).padStart(6, '0')}`,
      claimStatus: statusKey,
      orderSource: orderSource,
      merchantName: merchantName,
      claimAmount: amount,
      applicant: applicant,
      applyTime: applyTime,
      auditTime: auditTime,
      remark: Math.random() > 0.5 ? `这是第${index+1}条认款申请记录的备注信息` : '',
      // 新增字段用于演示
      orderNo: `ORD-${Math.floor(Math.random() * 1000000)}`,
      orderStatus: statusKey,
      orderPayStatus: ['待平台开票', '平台未回款'].includes(statusKey) ? '未回款' : '已回款',
      settlementStatus: statusKey === '已结算' ? '已结算' : '未结算',
      orderInvoiceStatus: ['待服务商开票', '已结算'].includes(statusKey) ? '已开票' : '未开票',
      invoiceUploadStatus: ['待服务商开票', '已结算'].includes(statusKey) ? '已上传' : '未上传',
      orderAmount: (parseFloat(amount) * 1.2).toFixed(2), // 订单金额通常大于结算金额
      settlementPrice: amount,
      orderPayTime: ['待服务商开票', '已结算'].includes(statusKey) 
        ? new Date(auditDate.getTime() + Math.floor(Math.random() * 7) * 24 * 60 * 60 * 1000).toISOString().split('T')[0] 
        : '',
      settlementTime: statusKey === '已结算' 
        ? new Date(auditDate.getTime() + Math.floor(Math.random() * 14) * 24 * 60 * 60 * 1000).toISOString().split('T')[0] 
        : '',
    };
  };
  
  return Array.from({ length: 100 }, (_, index) => generateClaimRecordItem(index));
};

// 加载数据
const loadData = () => {
  console.log('加载数据');
  loading.value = true;
  
  // 生成模拟数据
  const allData = getMockClaimRecordData();
  
  // 根据搜索参数过滤
  let filteredData = allData;
  
  // 如果有状态筛选
  if (activeStatus.value !== 'all') {
    const statusMap = {
      'auditing': '待平台开票',
      'rejected': '平台未回款',
      'approved': '待服务商开票',
      'abnormal1': '待结算',
      'abnormal2': '已结算',
    };
    filteredData = filteredData.filter(item => item.claimStatus === statusMap[activeStatus.value]);
  }
  
  // 其他搜索条件过滤
  Object.keys(searchParams.value).forEach(key => {
    if (searchParams.value[key] && key !== 'claimStatus') {
      // 处理金额范围筛选
      if (key === 'minAmount') {
        filteredData = filteredData.filter(item => {
          const amount = parseFloat(item.claimAmount);
          return amount >= parseFloat(searchParams.value.minAmount);
        });
      } else if (key === 'maxAmount') {
        filteredData = filteredData.filter(item => {
          const amount = parseFloat(item.claimAmount);
          return amount <= parseFloat(searchParams.value.maxAmount);
        });
      } else if (key === 'orderPayTime' && searchParams.value[key].length === 2) {
        // 处理回款时间范围
        const [startDate, endDate] = searchParams.value[key];
        filteredData = filteredData.filter(item => {
          if (!item.orderPayTime) return false;
          const payTime = new Date(item.orderPayTime);
          return payTime >= new Date(startDate) && payTime <= new Date(endDate);
        });
      } else if (key === 'settlementTime' && searchParams.value[key].length === 2) {
        // 处理结算时间范围
        const [startDate, endDate] = searchParams.value[key];
        filteredData = filteredData.filter(item => {
          if (!item.settlementTime) return false;
          const settleTime = new Date(item.settlementTime);
          return settleTime >= new Date(startDate) && settleTime <= new Date(endDate);
        });
      } else {
        filteredData = filteredData.filter(item => {
          if (typeof item[key] === 'string') {
            return item[key].includes(searchParams.value[key]);
          }
          return item[key] === searchParams.value[key];
        });
      }
    }
  });
  
  // 更新状态标签数量
  statusTabs.forEach(tab => {
    if (tab.key === 'all') {
      tab.count = allData.length;
    } else {
      const statusMap = {
        'auditing': '待平台开票',
        'rejected': '平台未回款',
        'approved': '待服务商开票',
        'abnormal1': '待结算',
        'abnormal2': '已结算',
      };
      tab.count = allData.filter(item => item.claimStatus === statusMap[tab.key]).length;
    }
  });
  
  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  
  tableData.value = filteredData.slice(start, end);
  total.value = filteredData.length;
  crudOptions.pagination.total = filteredData.length;
  loading.value = false;
};

// 当前选中的状态标签
const activeStatus = ref('all');

// 状态标签列表 - 调整名称与数据状态匹配
const statusTabs = [
  { key: 'all', name: '全部', count: 0 },
  { key: 'auditing', name: '待平台开票', count: 0 },
  { key: 'rejected', name: '平台未回款', count: 0 },
  { key: 'approved', name: '待服务商开票', count: 0 },
  { key: 'abnormal1', name: '待结算', count: 0 },
  { key: 'abnormal2', name: '已结算', count: 0 }
];

// 表格配置
const crudOptions = reactive({
  rowKey: 'id',
  showIndex: false,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100],
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "订单编号",
    dataIndex: "orderNo",
    align: "center",
    width: 150,
    search: true,
    formType: "input",
    searchField: "orderNo",
    placeholder: "请输入订单编号",
  },
  {
    title: "订单状态",
    dataIndex: "orderStatus",
    align: "center",
    width: 100,
    search: true,
    formType: "select",
    searchField: "orderStatus",
    slotName: "claimStatus", // 使用相同的状态显示逻辑
    dict: {
      data: [
        { label: '待平台开票', value: '待平台开票' },
        { label: '平台未回款', value: '平台未回款' },
        { label: '待服务商开票', value: '待服务商开票' },
        { label: '待结算', value: '待结算' },
        { label: '已结算', value: '已结算' },
      ],
    },
  },
  {
    title: "回款状态",
    dataIndex: "orderPayStatus",
    align: "center",
    width: 100,
    search: true,
    formType: "select",
    searchField: "orderPayStatus",
    dict: {
      data: [
        { 
          value: '未回款', 
          label: '未回款',
        },
        { 
          value: '已回款', 
          label: '已回款',
        },
      ],
    },
  }, 
  {
    title: "结算状态",
    dataIndex: "settlementStatus",
    align: "center",
    width: 150,
    search: true,
    formType: "select",
    searchField: "settlementStatus",
    dict: {
      data: [
        { 
          value: '未结算', 
          label: '未结算',
        },
        { 
          value: '已结算', 
          label: '已结算',
        },
      ],
    },
  },
  {
    title: "订单开票状态",
    dataIndex: "orderInvoiceStatus",
    align: "center",
    width: 120,
    search: true,
    formType: "select",
    searchField: "orderInvoiceStatus",
    dict: {
      data: [
        { 
          value: '未开票', 
          label: '未开票',
        },
        { 
          value: '已开票', 
          label: '已开票',
        },
      ],
    },
  },
  {
    title: "进项票上传状态",
    dataIndex: "invoiceUploadStatus",
    align: "center",
    width: 100,
    search: true,
    formType: "select",
    searchField: "invoiceUploadStatus",
    dict: {
      data: [
        { 
          value: '未上传', 
          label: '未上传',
        },
        { 
          value: '已上传', 
          label: '已上传',
        },
      ],
    },
  },
  {
    title: "订单金额",
    dataIndex: "orderAmount",
    align: "center",
    width: 120,
    search: true,
    formType: "between",
    searchField: "orderAmount",
    placeholder: ["最小金额", "最大金额"],
  },
  {
    title: "结算价",
    dataIndex: "settlementPrice",
    align: "center",
    width: 120,
    search: true,
    formType: "between",
    searchField: "settlementPrice",
    placeholder: ["最小金额", "最大金额"],
  },
  {
    title: "回款时间",
    dataIndex: "orderPayTime",
    align: "center",
    width: 120,
    search: true,
    formType: "range",
    searchField: "orderPayTime",
    placeholder: ["开始日期", "结束日期"],
    component: "DatePicker",
    componentProps: {
      type: "date-range",
      placeholder: "请选择回款时间范围",
      style: { width: "100%" },
    },
  },
  {
    title: "结算时间",
    dataIndex: "settlementTime",
    align: "center",
    width: 120,
    search: true,
    formType: "range",
    searchField: "settlementTime",
    placeholder: ["开始日期", "结束日期"],
    component: "DatePicker",
    componentProps: {
      type: "date-range",
      placeholder: "请选择结算时间范围",
      style: { width: "100%" },
    },
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    width: 250, // 增加宽度以容纳更多按钮
    fixed: "right",
    slotName: "operation",
  },
]);

// 搜索表单配置
const search = reactive({
  labelWidth: 80,
  formProps: {
    layout: "inline",
  },
  schemas: [],
});

// 搜索参数
const searchParams = ref({
  auditTime: "",
  claimStatus: "", // 保留这个字段用于状态标签切换
  claimNo: "",
  merchantName: "",
  orderSource: "",
  claimAmount: "",
  applicant: "",
  minAmount: "",
  maxAmount: "",
  orderPayTime: [],
  settlementTime: [],
});

// 页面加载时获取数据
onMounted(() => {
  console.log('页面加载完成，开始获取数据');
  // 初始化表格数据
  loadData();
});

// 监听分页变化
watch([currentPage, pageSize], () => {
  loadData();
});

// 监听搜索参数变化
watch(() => searchParams.value, () => {
  currentPage.value = 1;
  loadData();
}, { deep: true });
</script>

<style scoped lang="less">
.claim-record-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  
  .page-header {
    margin-bottom: 16px;
    
    h1 {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
    }
  }
  
  .page-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.search-area {
  padding: 16px;
}

.status-tabs-container {
  margin-bottom: 16px;
  
  :deep(.arco-tabs-nav) {
    margin-bottom: 0;
  }
  
  :deep(.arco-tabs-nav-tab) {
    padding: 0;
  }
  
  .status-tab-title {
    margin-right: 4px;
  }
  
  .status-tab-count {
    display: inline-block;
    min-width: 16px;
    padding: 0 4px;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    background-color: #f2f3f5;
    border-radius: 8px;
  }
}
</style>  