<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => $emit('update:visible', val)"
    title="上传框架协议"
    :footer="false"
    width="500px"
    unmountOnClose
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      layout="horizontal"
      auto-label-width
    >
      <a-form-item field="expressCompany" label="快递公司">
        <a-input v-model="form.expressCompany" placeholder="请输入快递公司名称" />
      </a-form-item>
      
      <a-form-item field="trackingNumber" label="快递单号">
        <a-input v-model="form.trackingNumber" placeholder="请输入快递单号" />
      </a-form-item>
      
      <a-form-item field="file" label="协议文件">
        <a-upload
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          @change="handleFileChange"
         
          :custom-request="handleFileUpload"
        >
          <template #upload-button>
            <a-button type="primary">
              <template #icon><icon-upload /></template>
              选择文件
            </a-button>
          </template>
          <template #extra>
            <div class="upload-tip">
              支持 pdf、docx、doc 格式，文件大小不超过10MB
            </div>
          </template>
        </a-upload>
      </a-form-item>
      
      <div class="dialog-footer">
        <a-button @click="$emit('update:visible', false)" class="mr-2">取消</a-button>
        <a-button type="primary" :loading="uploading" @click="submitForm">确认上传</a-button>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import providerSystemApi from '@/api/provider/system'
import commonApi from '@/api/common'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'upload-success'])

// 表单引用
const formRef = ref(null)

// 文件列表
const fileList = ref([])

// 上传状态
const uploading = ref(false)

// 接受的文件类型
// const acceptFileTypes = '.pdf,.docx,.doc'

// 表单数据
const form = reactive({
  expressCompany: '',
  trackingNumber: '',
  file: null,
  fileUrl: '',
  fileName: '',
  fileId: ''
})

// 表单验证规则
const rules = {
  // expressCompany: [
  //   { required: true, message: '请输入快递公司名称' }
  // ],
  // trackingNumber: [
  //   { required: true, message: '请输入快递单号' }
  // ],
  file: [
    { required: true, message: '请上传协议文件' }
  ]
}

// 处理文件变更
async function handleFileChange(files) {
  if (files.length > 0) {
    const file = files[0]
    
    // 验证文件类型
    const validTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'image/jpeg', 'image/png', 'image/jpg']
    if (!validTypes.includes(file.file.type)) {
      Message.error('文件格式不支持，请上传pdf、docx、doc或图片格式的文件')
      fileList.value = []
      form.file = null
      return
    }
    
    // 验证文件大小（10MB）
    const maxSize = 10 * 1024 * 1024
    if (file.file.size > maxSize) {
      Message.error('文件大小不能超过10MB')
      fileList.value = []
      form.file = null
      return
    }
    
    // 设置上传中状态
    uploading.value = true
    
    try {
      // 构建FormData
      const formData = new FormData()
      formData.append('file', file.file)
      
      // 调用上传API
      const result = await commonApi.uploadImage(formData)
      
      if (result && result.code === 200 && result.data) {
        // 上传成功，保存文件信息
        form.fileUrl = result.data.fileUrl
        form.fileName = result.data.originalName
        form.fileId = result.data.id
        Message.success('文件上传成功')
      } else {
        // 上传失败
        Message.error(result?.message || '文件上传失败')
        fileList.value = []
        form.file = null
      }
    } catch (error) {
      console.error('文件上传错误:', error)
      Message.error('文件上传失败，请重试')
      fileList.value = []
      form.file = null
    } finally {
      // 重置上传状态
      uploading.value = false
    }
  } else {
    form.file = null
    form.fileUrl = ''
    form.fileName = ''
    form.fileId = ''
  }
}

// 自定义文件上传处理
function handleFileUpload(option) {
  // 这里不做实际上传，只是保存文件对象
  // 实际上传会在表单提交时进行
  option.onSuccess()
}

// 提交表单
async function submitForm() {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    // 检查文件URL是否存在
    if (!form.fileUrl) {
      Message.error('请先上传协议文件')
      return
    }
    
    // 从本地存储获取用户ID
    const userInfo = JSON.parse(localStorage.getItem('user_provider'))
    if (!userInfo || !userInfo.id) {
      Message.error('未找到用户信息，请重新登录')
      return
    }
    
    // 设置上传中状态
    uploading.value = true
    
    // 构建请求数据
    const requestData = {
      express_company: form.expressCompany,
      express_no: form.trackingNumber,
      file_url: form.fileUrl,
      file_name: form.fileName,
      // fileId: form.fileId,
      user_id: userInfo.id
    }
    
    // 调用创建API
    const result = await providerSystemApi.agreement.create(requestData)
    
    if (result && result.code === 200) {
      // 创建成功
      Message.success('框架协议保存成功')
      
      // 构建Agreement数据
      const agreementData = {
        id: result.data?.id || `agreement_${Date.now()}`,
        uploadTime: new Date().toLocaleString('zh-CN'),
        expressCompany: form.expressCompany,
        trackingNumber: form.trackingNumber,
        fileName: form.fileName,
        fileUrl: form.fileUrl,
        status: '有效'
      }
      
      // 触发成功事件
      emit('upload-success', agreementData)
      
      // 关闭对话框
      emit('update:visible', false)
      
      // 重置表单
      resetForm()
    } else {
      // 创建失败
      Message.error(result?.message || '框架协议保存失败')
    }
  } catch (error) {
    console.error('表单验证错误:', error)
  } finally {
    // 重置上传状态
    uploading.value = false
  }
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  fileList.value = []
  form.file = null
  form.fileUrl = ''
  form.fileName = ''
  form.fileId = ''
}
</script>

<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.upload-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.mr-2 {
  margin-right: 8px;
}
</style>
