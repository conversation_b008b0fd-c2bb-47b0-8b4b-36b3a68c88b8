<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => $emit('update:visible', val)"
    title="订单号报备"
    @cancel="handleCancel"
    @before-ok="handleOk"
    :mask-closable="false"
    :width="'35%'"
  >
    <a-form :model="formData" layout="horizontal" auto-label-width>
      <!-- 选择平台 -->
      <a-form-item field="platform_id" label="选择平台" required>
        <a-select
          v-model="formData.platform_id"
          placeholder="请选择平台"
          :loading="loading.platform"
          allow-clear
        >
          <a-option v-for="platform in platformList" :value="platform.id" :label="platform.name">
            {{ platform.name }}
          </a-option>
        </a-select>
      </a-form-item>

      <!-- 订单号 -->
      <a-form-item field="orderNumber" label="订单号" required>
        <a-input
          v-model="formData.orderNumber"
          placeholder="请输入订单号"
          allow-clear
        />
        <div class="w-full ml-2 text-sm mt-1">
          如填写的单据编号已经被报备则不可报备
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import orderApi from '@/api/provider/order';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 平台数据
const platformList = ref([]);

// 加载状态
const loading = reactive({
  platform: false
});

// 表单数据
const formData = reactive({
  platform_id: '',
  orderNumber: ''
});

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  resetForm();
};

// 处理确定
const handleOk = async (done) => {
  try {
    // 表单验证
    if (!formData.platform_id) {
      throw new Error('请选择平台');
    }
    if (!formData.orderNumber) {
      throw new Error('请输入订单号');
    }

    console.log('订单号报备提交数据:', formData);

    // 构建API请求数据
    const requestData = {
      third_party_order_sn: formData.orderNumber.trim(),
      platform_id: formData.platform_id
    };

    console.log('订单号报备API请求数据:', requestData);

    // 显示加载状态
    Message.loading('正在提交订单号报备...');

    // 调用API提交数据
    const response = await orderApi.addOrderByOrderSn(requestData);

    console.log('订单号报备API响应:', response);

    if (response && response.code === 200) {
      Message.success(`订单号报备成功！报备单号：${response.data?.report_sn || ''}`);
      emit('success', response.data);
      handleCancel();
      done();
    } else {
      const errorMessage = response?.message || '订单号报备失败';
      console.error('订单号报备失败:', errorMessage, response);
      Message.error(errorMessage);
      done(false);
    }
  } catch (error) {
    console.error('订单号报备失败:', error);
    Message.error(error.message || '订单号报备失败，请稍后再试');
    done(false);
  }
};

// 重置表单
const resetForm = () => {
  formData.platform_id = '';
  formData.orderNumber = '';
};

// 获取平台列表
const fetchPlatformList = async () => {
  try {
    loading.platform = true;
    const response = await orderApi.getChannelList({
      page: 1,
      pageSize: 100 // 获取足够多的平台数据
    });

    if (response && response.code === 200 && response.data) {
      // 根据实际返回的数据结构调整，可能是items数组或直接是data数组
      platformList.value = response.data.items || response.data || [];
      console.log('平台列表:', platformList.value);
    } else {
      Message.error(response?.message || '获取平台列表失败');
    }
  } catch (error) {
    console.error('获取平台列表失败:', error);
    Message.error('获取平台列表失败，请稍后再试');
  } finally {
    loading.platform = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  fetchPlatformList();
});
</script>
