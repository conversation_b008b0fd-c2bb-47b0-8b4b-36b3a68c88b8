<template>
  <a-modal :visible="visible" title="模糊报备" @cancel="handleCancel" @before-ok="handleSubmit" :ok-text="'提交'"
    :cancel-text="'取消'" :mask-closable="false" :width="'70%'">
    <a-form :model="reportForm" layout="horizontal" auto-label-width>
      <!-- 基本信息 -->
      <div class="text-base font-medium mb-2">基本信息</div>
      <div class="grid grid-cols-2 gap-4 mb-4">
        <a-form-item label="平台" required>
          <a-select v-model="reportForm.platform_id" placeholder="请选择平台" :loading="loading.platform">
            <a-option v-for="platform in platformList" :value="platform.id" :label="platform.name">

            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="报备类型" >
          <a-select v-model="reportForm.report_type" placeholder="请选择报备类型">
            <a-option :value="1">新客户报备</a-option>
            <a-option :value="2">老客户续单</a-option>
            <a-option :value="3">竞争客户转单</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="报备金额" required>
          <a-input-number v-model="reportForm.amount" placeholder="请输入报备金额" :min="0" :precision="2"
            style="width: 100%" />
        </a-form-item>
        <a-form-item label="预计下单时间" required>
          <a-date-picker v-model="reportForm.expected_order_date" style="width: 100%" />
        </a-form-item>
        <a-form-item label="关联订单号">
          <a-input v-model="reportForm.related_order_id" placeholder="请输入关联订单号" />
        </a-form-item>
        <a-form-item label="预计发货时间">
          <a-date-picker v-model="reportForm.expected_delivery_date" style="width: 100%" />
        </a-form-item>
      </div>

      <!-- 客户信息 -->
      <div class="text-base font-medium mb-2">客户信息</div>
      <div class="grid grid-cols-2 gap-4 mb-4">
        <a-form-item label="客户名称" required>
          <a-input v-model="reportForm.customer_name" placeholder="请输入客户名称" />
        </a-form-item>
        <a-form-item label="客户区域" required>
          <a-input v-model="reportForm.customer_region" placeholder="请输入客户区域" />
        </a-form-item>
      </div>

      <!-- 收货信息 -->
      <div class="text-base font-medium mb-2">收货信息</div>
      <div class="grid grid-cols-2 gap-4 mb-4">
        <a-form-item label="收货人" required>
          <a-input v-model="reportForm.receiver_name" placeholder="请输入收货人姓名" />
        </a-form-item>
        <a-form-item label="联系电话" required>
          <a-input v-model="reportForm.receiver_phone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item label="所在地区" class="col-span-2" required>
          <a-cascader v-model="reportForm.region_codes" :options="regionOptions" placeholder="请选择省/市/区" path-mode
            check-strictly @change="handleRegionChange" />
        </a-form-item>
        <a-form-item label="详细地址" class="col-span-2">
          <a-textarea v-model="reportForm.detail_address" placeholder="请输入详细收货地址" :rows="2" />
        </a-form-item>
      </div>

      <!-- 商品信息 -->
      <div class="text-base font-medium mb-2">商品信息</div>
      <div class="mb-2">
        <a-button type="primary" size="small" @click="openProductSelector">
          <template #icon><icon-plus /></template>
          添加商品
        </a-button>
      </div>

      <!-- 商品表格 -->
      <div class="mb-4">
        <a-table :data="reportForm.products" :bordered="true" :pagination="false">
          <template #columns>
            <a-table-column title="商品信息" data-index="product_name" :width="300">
              <template #cell="{ record }">
                <div class="flex items-center">
                  <a-image :src="record.product_image || '/assets/images/placeholder.png'" :width="40" :height="40"
                    class="mr-2" fit="cover" />
                  <div>
                    <div class="font-medium">{{ record.product_name }}</div>
                    <div class="text-xs text-gray-500" v-if="record.specification">
                      规格：{{ record.specification }}
                    </div>
                    <div class="text-xs text-gray-500" v-if="record.product_code">
                      编码：{{ record.product_code }}
                    </div>
                  </div>
                </div>
              </template>
            </a-table-column>
            <a-table-column title="参考价" data-index="unit_price" :width="100">
              <template #cell="{ record }">
                {{ formatAmount(record.unit_price) }}
              </template>
            </a-table-column>
            <a-table-column title="数量" data-index="quantity" :width="120">
              <template #cell="{ record, rowIndex }">
                <a-input-number v-model="record.quantity" placeholder="数量" :min="1" :precision="0"
                  @change="() => calculateSubtotal(rowIndex)" style="width: 100%" />
              </template>
            </a-table-column>
            <a-table-column title="报备价" data-index="report_price" :width="120">
              <template #cell="{ record, rowIndex }">
                <a-input-number v-model="record.report_price" placeholder="报备价" :min="0" :precision="2"
                  @change="() => calculateSubtotal(rowIndex)" style="width: 100%" />
              </template>
            </a-table-column>
            <a-table-column title="小计" data-index="subtotal" :width="120">
              <template #cell="{ record }">
                {{ formatAmount(record.subtotal) }}
              </template>
            </a-table-column>
            <a-table-column title="操作" align="center" :width="80">
              <template #cell="{ rowIndex }">
                <a-button type="text" status="danger" @click="removeProduct(rowIndex)">
                  <template #icon><icon-delete /></template>
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>

        <div class="flex justify-end mt-2" v-if="reportForm.products.length > 0">
          <div class="text-base">总金额：<span class="text-red-500 font-medium">{{ formatAmount(totalAmount) }}</span></div>
        </div>
      </div>

      <!-- 商品选择器组件 -->
      <ProductSelector v-model:visible="productSelectorVisible" @select-product="handleSelectProduct"
        @cancel="productSelectorVisible = false" />

      <!-- 备注信息 -->
      <a-form-item label="备注">
        <a-textarea v-model="reportForm.remark" placeholder="请输入备注信息" :rows="3" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineEmits, defineProps, onMounted, computed } from 'vue';
import { IconPlus, IconDelete } from "@arco-design/web-vue/es/icon";
import { Message } from '@arco-design/web-vue';
import ProductSelector from './components/ProductSelector.vue';
import orderApi from '@/api/provider/order';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:visible', 'submit', 'cancel']);

// 省市区级联选择器数据
const regionOptions = ref([]);

// 商品选择器状态
const productSelectorVisible = ref(false);

// 平台数据
const platformList = ref([]);

// 加载状态
const loading = reactive({
  platform: false
});

// 报备表单数据
const reportForm = reactive({
  platform_id: "",
  report_type: "",
  amount: 0,
  expected_order_date: null,
  related_order_id: "",
  expected_delivery_date: null,
  customer_name: "",
  customer_region: "",
  receiver_name: "",
  receiver_phone: "",
  region_codes: [],
  region_names: [],
  detail_address: "",
  remark: "",
  products: []
});

// 打开商品选择器
const openProductSelector = () => {
  productSelectorVisible.value = true;
};

// 处理选择商品
const handleSelectProduct = (product) => {
  // 检查是否已存在相同商品
  const existingIndex = reportForm.products.findIndex(item =>
    item.product_id === product.product_id &&
    item.specification === product.specification
  );

  if (existingIndex >= 0) {
    // 如果已存在，增加数量
    reportForm.products[existingIndex].quantity += 1;
    calculateSubtotal(existingIndex);
    Message.info('商品已存在，已增加数量');
  } else {
    // 如果不存在，添加新商品
    reportForm.products.push(product);
  }

  // 更新总金额
  calculateTotalAmount();
};

// 删除商品
const removeProduct = (index) => {
  reportForm.products.splice(index, 1);
};

// 计算小计
const calculateSubtotal = (index) => {
  const product = reportForm.products[index];
  product.subtotal = product.report_price * product.quantity;
  calculateTotalAmount();
};

// 计算总金额
const totalAmount = computed(() => {
  return reportForm.products.reduce((total, item) => {
    return total + (item.subtotal || 0);
  }, 0);
});

const calculateTotalAmount = () => {
  reportForm.amount = totalAmount.value;
};

// 取消
const handleCancel = () => {
  emits('update:visible', false);
  emits('cancel');
};

// 提交
const handleSubmit = async () => {
  console.log('handleSubmit 函数被调用');
  console.log('当前表单数据:', reportForm);

  // 表单验证
  if (!reportForm.platform_id) {
    Message.error("请选择平台");
    return false;
  }
  if (!reportForm.report_type) {
    Message.error("请选择报备类型");
    return false;
  }
  if (!reportForm.expected_order_date) {
    Message.error("请选择预计下单时间");
    return false;
  }
  if (!reportForm.customer_name) {
    Message.error("请输入客户名称");
    return false;
  }
  if (!reportForm.customer_region) {
    Message.error("请输入客户区域");
    return false;
  }
  if (!reportForm.receiver_name) {
    Message.error("请输入收货人姓名");
    return false;
  }
  if (!reportForm.receiver_phone) {
    Message.error("请输入联系电话");
    return false;
  }
  if (!reportForm.region_codes || reportForm.region_codes.length === 0) {
    Message.error("请选择所在地区");
    return false;
  }


  // 验证商品信息
  if (reportForm.products.length === 0) {
    Message.error('请添加至少一个商品');
    return false;
  }

  for (let i = 0; i < reportForm.products.length; i++) {
    const product = reportForm.products[i];
    if (!product.product_name) {
      Message.error(`第${i + 1}行商品名称不能为空`);
      return false;
    }
    if (!product.product_id) {
      Message.error(`第${i + 1}行商品ID不能为空`);
      return false;
    }
    if (!product.report_price && product.report_price !== 0) {
      Message.error(`第${i + 1}行商品报备价不能为空`);
      return false;
    }
    if (!product.quantity || product.quantity < 1) {
      Message.error(`第${i + 1}行商品数量必须大于0`);
      return false;
    }
    // 单价验证：如果单价为0或空，但有报备价，则可以通过
    const unitPrice = parseFloat(product.unit_price) || 0;
    const reportPrice = parseFloat(product.report_price) || 0;
    if (unitPrice <= 0 && reportPrice <= 0) {
      Message.error(`第${i + 1}行商品单价和报备价不能都为0`);
      return false;
    }
  }

  try {
    // 组装完整地址
    const fullAddress = reportForm.region_names.join('') + reportForm.detail_address;

    // 构建提交数据
    const submitData = {
      customer_name: reportForm.customer_name,
      customer_region: reportForm.customer_region,
      platform_id: reportForm.platform_id,
      report_type: parseInt(reportForm.report_type), // 确保是整数
      report_amount: parseFloat(reportForm.amount), // 确保是数字
      expected_order_time: reportForm.expected_order_date ? new Date(reportForm.expected_order_date).getTime() : null,
      related_order_number: reportForm.related_order_id || "",
      expected_shipping_time: reportForm.expected_delivery_date ? new Date(reportForm.expected_delivery_date).getTime() : null,
      recipient_name: reportForm.receiver_name,
      contact_phone: reportForm.receiver_phone,
      shipping_address: fullAddress,
      remark: reportForm.remark || "",
      items: reportForm.products.map(product => {
        const unitPrice = parseFloat(product.unit_price) || 0;
        const reportPrice = parseFloat(product.report_price) || 0;

        return {
          product_name: product.product_name,
          product_id: product.product_id.toString(), // 确保是字符串
          product_image: product.product_image || "",
          product_sku: product.product_code || "", // 添加product_sku字段
          specification: product.specification || "",
          quantity: parseInt(product.quantity), // 确保是整数
          unit_price: unitPrice,
          report_price: reportPrice, // 确保是数字
          subtotal: parseFloat(product.subtotal) || 0 // 确保是数字
        };
      })
    };

    console.log('提交报备数据:', submitData);
    console.log('商品数据详情:', submitData.items);

    // 调用API提交报备
    const response = await orderApi.addOrderReport(submitData);

    console.log('API响应:', response);

    if (response && response.code === 200) {
      Message.success(`报备提交成功！报备单号：${response.data?.report_id || ''}`);

      // 触发提交事件
      emits('submit', response.data);

      // 重置表单
      resetForm();

      // 关闭弹窗
      emits('update:visible', false);

      return true;
    } else {
      const errorMessage = response?.message || '报备提交失败';
      console.error('报备提交失败:', errorMessage, response);
      Message.error(errorMessage);
      return false;
    }
  } catch (error) {
    console.error('提交报备失败:', error);
    Message.error('提交失败，请稍后再试');
    return false;
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(reportForm, {
    platform_id: "",
    report_type: "",
    amount: 0,
    expected_order_date: null,
    related_order_id: "",
    expected_delivery_date: null,
    customer_name: "",
    customer_region: "",
    receiver_name: "",
    receiver_phone: "",
    region_codes: [],
    region_names: [],
    detail_address: "",
    remark: "",
    products: []
  });
};

// 获取平台列表
const fetchPlatformList = async () => {
  try {
    loading.platform = true;
    const response = await orderApi.getChannelList({
      page: 1,
      pageSize: 100 // 获取足够多的平台数据
    });

    if (response && response.code === 200 && response.data) {
      // 根据实际返回的数据结构调整，可能是items数组或直接是data数组
      platformList.value = response.data.items || response.data || [];
      console.log('平台列表:', platformList.value);
    } else {
      Message.error(response?.message || '获取平台列表失败');
    }
  } catch (error) {
    console.error('获取平台列表失败:', error);
    Message.error('获取平台列表失败，请稍后再试');
  } finally {
    loading.platform = false;
  }
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount && amount !== 0) return "-";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 获取省市区数据
const fetchRegionTree = async () => {
  try {
    // 获取token
    const tool = await import("@/utils/tool");
    const token = tool.default.local.get("token_provider");

    // 设置请求参数
    const params = {
      parentId: 0,
      excludeStreet: false // 允许获取到街道级别数据
    };

    // 显示加载中提示
    Message.loading("正在加载省市区数据...");

    // 直接使用axios调用接口，手动设置请求头
    const axios = (await import("axios")).default;
    const { baseUrl } = await import("@/api/config");
    const result = await axios({
      url: `${baseUrl}/api/v1/master/region/tree`,
      method: "get",
      params,
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    // 处理响应数据
    const response = result.data;

    if (response && response.code === 200 && response.data) {
      // 处理返回的数据，转换为级联选择器所需格式
      const formattedData = formatRegionData(response.data);
      regionOptions.value = formattedData;
      // Message.success("省市区数据加载成功");
    } else {
      Message.error(response?.message || "获取省市区数据失败");
    }
  } catch (error) {
    console.error("获取省市区数据失败:", error);
    Message.error("获取省市区数据失败，请稍后再试");
  }
};

// 格式化省市区数据为级联选择器所需格式
const formatRegionData = data => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    // 检查是否有子级数据
    const hasChildren =
      item.children && Array.isArray(item.children) && item.children.length > 0;

    return {
      value: item.code,
      label: item.name,
      // 只有子级数据存在时才递归处理
      children: hasChildren ? formatRegionData(item.children) : []
    };
  });
};

// 组件挂载时加载数据
onMounted(() => {
  fetchRegionTree();
  fetchPlatformList();
});

// 处理区域选择变化
const handleRegionChange = (value, selectedOptions) => {
  if (Array.isArray(value) && value.length > 0) {
    // 保存选中的区域名称
    if (Array.isArray(selectedOptions) && selectedOptions.length > 0) {
      reportForm.region_names = selectedOptions.map(option => option.label);
    }
  } else {
    // 清空值
    reportForm.region_names = [];
  }
};
</script>

<style scoped>
/* 组件样式 */
</style>
