<template>
  <div class="ma-content-block p-4">
    <!-- 状态标签页 -->
    <div class="mb-4">
      <a-tabs :active-key="activeTab" @change="handleTabChange">
        <a-tab-pane key="-1" title="全部"></a-tab-pane>
        <a-tab-pane key="1" title="待审核"></a-tab-pane>
        <a-tab-pane key="2" title="审核通过"></a-tab-pane>
        <a-tab-pane key="3" title="已驳回"></a-tab-pane>
      </a-tabs>
    </div>

    <!-- CRUD 组件 -->
    <ma-crud
      :options="crud"
      :columns="columns"
      ref="crudRef"
      :row-class="() => ''"
      :row-selection="{ type: 'none' }"
    >
      <!-- 自定义搜索按钮区域 -->
      <template #tableBeforeButtons>
        <a-button type="primary" @click="openAddReport">
          <template #icon><icon-plus /></template>
          模糊报备
        </a-button>
      </template>
       <!-- 自定义搜索按钮区域 -->
       <template #searchBeforeButtons>
        <a-button type="primary" @click="openOrderReport">
          订单号报备
        </a-button>
      </template>
      <!-- 状态列 -->
      <template #audit_status="{ record }">
        <a-tag :color="getStatusColor(record.audit_status)">{{ getStatusText(record.audit_status) }}</a-tag>
      </template>

      <!-- 操作列 -->
      <template #operationBeforeExtend="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button type="text" size="small" @click="openDetails(record)">详情</a-button>

          <!-- <template v-if="record.status === 1 || record.status === 2">
            <a-button type="text" size="small" @click="openAuditRecord(record)">审核记录</a-button>
          </template> -->
        </div>
      </template>
    </ma-crud>

    <!-- 报备详情抽屉 -->
    <a-drawer
      :width="'70%'"
      :visible="detailVisible"
      @update:visible="(val) => detailVisible = val"
      @cancel="closeDetail"
      unmountOnClose
      :footer="true"
    >
      <template #title>报备详情</template>

      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeDetail" class="mr-2">关闭</a-button>
          <!-- 待审核状态下显示审核按钮 -->
          <!-- <a-button
            v-if="detailRecord.audit_status === 1"
            type="primary"
            status="success"
            @click="handleAudit(detailRecord)"
          >审核</a-button> -->
        </div>
      </template>

      <div class="report-detail-container">
        <!-- 报备基本信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">基本信息</div>
          <div class="order-info-table mb-6">
            <div class="order-info-grid">
              <div class="info-cell">
                <div class="info-label">报备单号</div>
                <div class="info-value">{{ detailRecord.id || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">平台</div>
                <div class="info-value">{{ getPlatformName(detailRecord.platform_id) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">报备类型</div>
                <div class="info-value">{{ getReportTypeText(detailRecord.report_type) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">预计下单时间</div>
                <div class="info-value">{{ formatTimestamp(detailRecord.expected_order_time) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">客户名称</div>
                <div class="info-value">{{ detailRecord.customer_name || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">客户区域</div>
                <div class="info-value">{{ detailRecord.customer_region || '-' }}</div>
              </div>
              <div class="info-cell col-span-2">
                <div class="info-label">备注</div>
                <div class="info-value">{{ detailRecord.remark || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">商品信息</div>
          <a-table
            :data="detailRecord.items || []"
            :bordered="true"
            :pagination="false"
            class="product-detail-table mb-6"
          >
            <template #footer>
              <tr class="summary-row">
                <td>
                  <div class="summary-label">合计</div>
                </td>

                <td class="text-center" style="width: 142px;">
                  <div
                    class="summary-value"
                  >¥{{ formatAmount(calculateTotal(detailRecord.items)) }}</div>
                </td>
              </tr>
            </template>
            <template #columns>
              <a-table-column title="商品图片" data-index="product_image" width="120">
                <template #cell="{ record }">
                  <a-avatar
                    :size="60"
                    shape="square"
                    :image-url="record.product_image || '/assets/images/placeholder.png'"
                    class="product-detail-image"
                  ></a-avatar>
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="product_name" />
              <a-table-column title="规格与编码" data-index="specification">
                <template #cell="{ record }">
                  <div>规格：{{ record.specification || '-' }}</div>
                  <div class="text-gray-500 text-sm">编码：{{ record.product_code || '-' }}</div>
                </template>
              </a-table-column>
              <a-table-column title="单价" data-index="unit_price" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.unit_price) }}</template>
              </a-table-column>
              <a-table-column title="数量" data-index="quantity" align="center" />
              <a-table-column title="报备价" data-index="report_price" align="center">
                <template
                  #cell="{ record }"
                >¥{{ formatAmount(record.report_price || record.unit_price) }}</template>
              </a-table-column>
              <!-- <a-table-column title="小计" data-index="subtotal" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.subtotal) }}</template>
              </a-table-column>-->
            </template>
          </a-table>
        </div>

        <!-- 收货信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">收货信息</div>
          <div class="order-info-table mb-6">
            <div class="order-info-grid">
              <div class="info-cell">
                <div class="info-label">收货人</div>
                <div class="info-value">{{ detailRecord.recipient_name || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">联系电话</div>
                <div class="info-value">{{ detailRecord.contact_phone || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">预计交付时间</div>
                <div class="info-value">{{ formatTimestamp(detailRecord.expected_shipping_time) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">收货地址</div>
                <div class="info-value">{{ detailRecord.shipping_address || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 审核记录 -->
        <div class="mb-6" v-if="detailRecord.audit_status === 2 || detailRecord.audit_status === 3">
          <div class="text-lg font-bold mb-4">审核记录</div>
          <a-table
            :data="detailRecord.audit_records || []"
            :bordered="true"
            :pagination="false"
            class="audit-record-table"
          >
            <template #columns>
              <a-table-column title="审核时间" data-index="audit_time">
                <template #cell="{ record }">{{ formatDateTime(record.audit_time) }}</template>
              </a-table-column>
              <a-table-column title="审核人" data-index="auditor" />
              <a-table-column title="审核结果" data-index="result">
                <template #cell="{ record }">
                  <a-tag
                    :color="record.result === 2 ? 'green' : 'red'"
                  >{{ record.result === 2 ? '通过' : '驳回' }}</a-tag>
                </template>
              </a-table-column>
              <a-table-column title="备注" data-index="comment" />
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>

    <!-- 审核记录抽屉 -->
    <a-drawer
      :width="'70%'"
      :visible="auditRecordVisible"
      @update:visible="(val) => auditRecordVisible = val"
      @cancel="closeAuditRecord"
      unmountOnClose
    >
      <template #title>审核记录</template>

      <div class="audit-record-container">
        <a-table :data="currentAuditRecords || []" :bordered="true" :pagination="false">
          <template #columns>
            <a-table-column title="审核时间" data-index="audit_time">
              <template #cell="{ record }">{{ formatDateTime(record.audit_time) }}</template>
            </a-table-column>
            <a-table-column title="审核人" data-index="auditor" />
            <a-table-column title="审核结果" data-index="result">
              <template #cell="{ record }">
                <a-tag
                  :color="record.result === 2 ? 'green' : 'red'"
                >{{ record.result === 2 ? '通过' : '驳回' }}</a-tag>
              </template>
            </a-table-column>
            <a-table-column title="备注" data-index="comment" />
          </template>
        </a-table>
      </div>
    </a-drawer>

    <!-- 审核弹窗 -->
    <a-modal
      v-model:visible="auditVisible"
      title="报备审核"
      @cancel="closeAudit"
      @before-ok="handleAuditSubmit"
    >
      <a-form :model="auditForm" layout="vertical">
        <a-form-item label="审核结果" required>
          <a-radio-group v-model="auditForm.result">
            <a-radio :value="2">通过</a-radio>
            <a-radio :value="3">驳回</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审核意见" required>
          <a-textarea v-model="auditForm.comment" placeholder="请输入审核意见" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 使用添加报备组件 -->
    <add-report-dialog
      v-model:visible="addReportVisible"
      @submit="handleAddReportSubmit"
      @cancel="closeAddReport"
    />
    <!-- 订单号报备弹窗 -->
    <order-report-dialog
      v-model:visible="orderReportVisible"
      @success="handleOrderReportSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconPlus } from "@arco-design/web-vue/es/icon";
import OrderReportDialog from './OrderReportDialog.vue';
import AddReportDialog from './AddReportDialog.vue';
import orderApi from '@/api/provider/order';
import orderReportApi from '@/api/master/orderReport';


// 页面元数据
definePageMeta({
  name: "provider-ordermanagement-reportingmanagement",
  path: "/provider/ordermanagement/reportingmanagement"
});

// 状态标签页
const activeTab = ref("-1");
const crudRef = ref(null);

const handleTabChange = key => {
  activeTab.value = key;
  // 切换标签页时刷新表格数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 详情抽屉
const detailVisible = ref(false);
const detailRecord = reactive({});

const openDetails = async (record) => {
  try {
    console.log('打开详情，记录ID:', record.id);

    // 显示加载状态
    Message.loading('正在加载详情数据...');

    // 调用API获取详细数据
    const response = await orderApi.getOrderReportDetail(record.id);
    // const response = await orderReportApi.getReportDetail(record.id);

    console.log('详情API响应:', response);

    if (response && response.code === 200) {
      // 使用API返回的详细数据
      Object.assign(detailRecord, response.data);   
      detailRecord.audit_records = [{
        audit_time: detailRecord.audit_time,
        auditor: detailRecord.audit_username,
        result: detailRecord.audit_status,
        comment: detailRecord.audit_remark
      }];
   
      detailVisible.value = true;
      Message.success('详情数据加载成功');
    } else {
      Message.error(response?.message || '获取详情失败');
    }
  } catch (error) {
    console.error('获取报备详情失败:', error);
    Message.error('获取详情失败，请稍后再试');
  }
};

const closeDetail = () => {
  detailVisible.value = false;
};

// 审核弹窗
const auditVisible = ref(false);
const auditForm = reactive({
  result: 1,
  comment: ""
});

// 添加报备弹窗
const addReportVisible = ref(false);

// 订单号报备弹窗
const orderReportVisible = ref(false);

// 打开订单号报备弹窗
const openOrderReport = () => {
  orderReportVisible.value = true;
};

// 处理订单号报备成功
const handleOrderReportSuccess = () => {
  // 刷新表格数据
  crudRef.value?.refresh();
};

// 审核记录抽屉
const auditRecordVisible = ref(false);
const currentAuditRecords = ref([]);

const handleAudit = record => {
  Object.assign(detailRecord, record);
  auditVisible.value = true;
  auditForm.result = 2;
  auditForm.comment = "";
};

const closeAudit = () => {
  auditVisible.value = false;
};

const openAuditRecord = record => {
  // 模拟获取审核记录数据
  currentAuditRecords.value = record.audit_records || [];
  auditRecordVisible.value = true;
};

const closeAuditRecord = () => {
  auditRecordVisible.value = false;
};

const handleAuditSubmit = async () => {
  try {
    // 验证表单
    if (!auditForm.comment || auditForm.comment.trim() === '') {
      Message.error('请输入审核意见');
      return false;
    }

    console.log("提交审核结果", auditForm);

    // 构建审核数据
    const auditData = {
      id: detailRecord.id,
      audit_status: auditForm.result, // 1=通过, 3=驳回
      audit_remark: auditForm.comment.trim()
    };

    console.log('审核API请求数据:', auditData);

    // 显示加载状态
    Message.loading('正在提交审核结果...');

    // 调用审核API
    const response = await orderApi.auditOrderReport(auditData);

    console.log('审核API响应:', response);

    if (response && response.code === 200) {
      Message.success('审核提交成功！');

      // 更新本地数据 - 需要映射审核接口的状态值到详情接口的状态值
      // 审核接口: 1=通过, 3=驳回
      // 详情接口: 1=待审核, 2=审核通过, 3=已驳回
      detailRecord.audit_status = auditForm.result === 1 ? 2 : 3;

      // 添加审核记录
      if (!detailRecord.audit_records) {
        detailRecord.audit_records = [];
      }

      detailRecord.audit_records.push({
        audit_time: new Date().toISOString(),
        auditor: "当前用户",
        result: auditForm.result,
        comment: auditForm.comment
      });

      // 关闭审核弹窗
      auditVisible.value = false;

      // 刷新表格数据
      if (crudRef.value) {
        crudRef.value.refresh();
      }

      return true;
    } else {
      const errorMessage = response?.message || '审核提交失败';
      console.error('审核提交失败:', errorMessage, response);
      Message.error(errorMessage);
      return false;
    }
  } catch (error) {
    console.error('审核提交失败:', error);
    Message.error('审核提交失败，请稍后再试');
    return false;
  }
};

// 添加报备相关函数
const openAddReport = () => {
  addReportVisible.value = true;
};

const closeAddReport = () => {
  addReportVisible.value = false;
};

const handleAddReportSubmit = (formData) => {
  // 报备提交成功后的处理
  console.log('报备提交成功:', formData);

  // 关闭弹窗
  addReportVisible.value = false;

  // 刷新表格数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }

  Message.success('报备提交成功！');
};

// 状态处理函数
const getStatusColor = status => {
  switch (parseInt(status)) {
    case 1:
      return "orange";
    case 2:
      return "green";
    case 3:
      return "red";
    default:
      return "gray";
  }
};

const getStatusText = status => {
  switch (parseInt(status)) {
    case 1:
      return "待审核";
    case 2:
      return "审核通过";
    case 3:
      return "已驳回";
    default:
      return "未知状态";
  }
};

// 格式化时间戳
const formatTimestamp = timestamp => {
  if (!timestamp) return "-";
  // 如果是字符串格式的时间戳，转换为数字
  const time = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
  // 如果时间戳是秒级，转换为毫秒级
  const date = new Date(time * 1000);
  return date.toLocaleDateString("zh-CN");
};

// 格式化日期
const formatDate = date => {
  if (!date) return "-";
  return new Date(date).toLocaleDateString("zh-CN");
};

// 格式化日期时间
const formatDateTime = datetime => {
  if (!datetime) return "-";

  // 处理不同格式的时间戳
  let timestamp = datetime;

  // 如果是字符串，转换为数字
  if (typeof datetime === 'string') {
    timestamp = parseInt(datetime);
  }

  // 如果是秒级时间戳（小于10位数），转换为毫秒级
  if (timestamp < 10000000000) {
    timestamp = timestamp * 1000;
  }

  const date = new Date(timestamp);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return "-";
  }

  return `${date.toLocaleDateString("zh-CN")} ${date.toLocaleTimeString(
    "zh-CN",
    { hour: "2-digit", minute: "2-digit" }
  )}`;
};

// 获取平台名称
const getPlatformName = platformId => {
  const platformMap = {
    "185653478876647424": "天猫",
    "185653478876647425": "京东",
    "185653478876647426": "微信",
    "185653478876647427": "其他"
  };
  return platformMap[platformId] || "未知平台";
};

// 获取审核状态文本
const getAuditStatusText = status => {
  switch (parseInt(status)) {
    case 1:
      return "待审核";
    case 2:
      return "审核通过";
    case 3:
      return "已驳回";
    default:
      return "未知状态";
  }
};

// 获取报备类型文本
const getReportTypeText = type => {
  switch (parseInt(type)) {
    case 1:
      return "新客户报备";
    case 2:
      return "老客户续单";
    case 3:
      return "竞争客户转单";
    default:
      return "未知类型";
  }
};

// 格式化金额
const formatAmount = amount => {
  if (!amount && amount !== 0) return "-";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 计算商品总价
const calculateTotal = products => {
  if (!products || !products.length) return 0;
  return products.reduce((total, product) => {
    const price = product.report_price || product.unit_price || 0;
    const quantity = product.quantity || 0;
    return total + price * quantity;
  }, 0);
};

// 表格合计行
const productSummary = currentData => {
  // 计算总金额
  const totalAmount = calculateTotal(currentData);

  return {
    show: true,
    data: [
      {
        product_name: "合计",
        report_price: `¥${formatAmount(totalAmount)}`
      }
    ]
  };
};

// 获取合计行样式
const getSummaryStyle = column => {
  if (column.dataIndex === "report_price") {
    return {
      color: "#ff4d4f",
      fontWeight: "bold",
      fontSize: "14px",
      textAlign: "center"
    };
  }
  if (column.dataIndex === "product_name") {
    return {
      fontWeight: "bold",
      textAlign: "right",
      paddingRight: "20px"
    };
  }
  return {};
};

// 表格列定义
const columns = reactive([
  {
    title: "报备单号",
    dataIndex: "id",
    width: 150
  },
  {
    title: "报备人",
    dataIndex: "reporter_name",
    search: true,
    width: 100,
    searchSpan: 6
  },
  {
    title: "报备日期",
    dataIndex: "created_at",
    width: 120,
    search: true,
    searchSpan: 6,
    formType: "daterange",
    render: ({ record }) => formatTimestamp(record.created_at)
  },
  {
    title: "报备金额",
    dataIndex: "report_amount",
    width: 120,
    render: ({ record }) => `¥${formatAmount(record.report_amount)}`
  },
  {
    title: "平台",
    dataIndex: "platform_id",
    search: true,
    width: 100,
    searchSpan: 6,
    formType: "select",
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "天猫", value: "185653478876647424" },
        { label: "京东", value: "185653478876647425" },
        { label: "微信", value: "185653478876647426" },
        { label: "其他", value: "185653478876647427" }
      ]
    },
    render: ({ record }) => getPlatformName(record.platform_id)
  },
  {
    title: "审核状态",
    dataIndex: "audit_status",
    search: true,
    width: 100,
    formType: "select",
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "待审核", value: 1 },
        { label: "审核通过", value: 2 },
        { label: "已驳回", value: 3 }
      ]
    },
    render: ({ record }) => getAuditStatusText(record.audit_status)
  },
  {
    title: "关联订单号",
    dataIndex: "related_order_number",
    search: true,
    width: 120,
    searchSpan: 6
  },
  {
    title: "客户名称",
    dataIndex: "customer_name",
    search: true,
    width: 120,
    searchSpan: 6
  },
  {
    title: "客户区域",
    dataIndex: "customer_region",
    search: true,
    width: 100,
    searchSpan: 6
  },
  {
    title: "操作",
    dataIndex: "operationBeforeExtend",
    width: 100,
    align: "center",
    fixed: "right"
  }
]);

// 表格配置
const crud = reactive({
  title: "报备管理",
  api: getReportingList,
  searchLabelWidth: "100px",
  searchSpan: 6,
  pageSize: 10,
  menu: false
});

// 真实API函数
async function getReportingList(params) {
  try {
    const { page = 1, pageSize = 10, ...filters } = params || {};

    // 构建请求参数，包含分页参数
    const requestParams = {
      // 分页参数
      page,
      pageSize,

      // 查询过滤参数
      ...filters
    };

    // 根据标签页状态添加审核状态过滤
    if (activeTab.value !== "-1") {
      requestParams.audit_status = Number(activeTab.value);
    } else if (filters.audit_status) {
      requestParams.audit_status = Number(filters.audit_status);
    }

    // 清理undefined值
    Object.keys(requestParams).forEach(key => {
      if (requestParams[key] === undefined || requestParams[key] === '') {
        delete requestParams[key];
      }
    });

    console.log('请求参数(包含分页):', requestParams);
    console.log('分页信息:', { page, pageSize });

    // 调用API
    const response = await orderApi.getOrderReportList(requestParams);

    console.log('API响应:', response);

    if (response && response.code === 200) {
      return {
        success: true,
        message: response.message || "获取报备列表成功",
        code: 200,
        data: {
          items: response.data.items || [],
          pageInfo: {
            total: response.data.pageInfo?.total || 0,
            currentPage: response.data.pageInfo?.current || page,
            totalPage: Math.ceil((response.data.pageInfo?.total || 0) / pageSize)
          }
        }
      };
    } else {
      throw new Error(response?.message || '获取报备列表失败');
    }
  } catch (error) {
    console.error('获取报备列表失败:', error);
    Message.error(error.message || '获取报备列表失败，请稍后再试');
    return {
      success: false,
      message: error.message || '获取报备列表失败',
      code: 500,
      data: {
        items: [],
        pageInfo: {
          total: 0,
          currentPage: 1,
          totalPage: 0
        }
      }
    };
  }
}



// 初始化时调用API
onMounted(() => {
  // 页面加载时可以执行一些初始化操作
});
</script>

<style scoped>
.report-detail-container {
  padding: 0 20px;
}

.audit-record-container {
  padding: 20px;
}

/* 订单信息表格样式 */
.order-info-table {
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0;
}

.info-cell {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
}

.info-cell:nth-child(even) {
  border-right: none;
}

.info-cell:last-child,
.info-cell:nth-last-child(2):nth-child(odd) {
  border-bottom: none;
}

.info-cell.col-span-2 {
  grid-column: span 2;
  border-right: none;
}

.info-label {
  width: 120px;
  color: #86909c;
  flex-shrink: 0;
  font-weight: 500;
}

.info-value {
  flex: 1;
}

.amount-value {
  color: #ff4d4f;
  font-weight: 500;
}

/* 商品详情表格样式 */
.product-detail-table {
  width: 100%;
}

.product-detail-cell {
  display: flex;
  align-items: center;
}

.product-detail-image {
  margin-right: 12px;
}

.product-detail-name {
  text-align: left;
}

/* 审核记录表格样式 */
.audit-record-table {
  width: 100%;
}

/* 表格合计行样式 */
.summary-row {
  display: flex;
  padding: 12px 16px;

  justify-content: space-between;
  /* background-color: #fafafa; */
  /* border: 1px solid #f0f0f0; */
  border-top: none;
}

.summary-content {
  display: flex;
  align-items: center;
}

.summary-label {
  font-weight: 500;
  margin-right: 8px;
}

.summary-value {
  font-weight: 600;
  color: #ff4d4f;
  font-size: 16px;
}
</style>