<template>
  <div class="ma-content-block p-4">
    <!-- 状态标签页 -->
    <div class="mb-4">
      <a-tabs :active-key="activeTab" @change="handleTabChange">
        <a-tab-pane key="all" title="全部"></a-tab-pane>
        <a-tab-pane key="pending" title="待处理"></a-tab-pane>
        <a-tab-pane key="processing" title="处理中"></a-tab-pane>
        <a-tab-pane key="completed" title="已完成"></a-tab-pane>
      </a-tabs>
    </div>
    
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :data="tableData" :loading="loading" :row-selection="{ type: 'none' }" :row-class="() => ''" @page-change="handlePageChange" @search="handleSearch">
      <!-- 售后单号列 -->
      <template #after_no-cell="{ record }">
        <div class="after-no">
          <div class="text-blue-500 font-medium cursor-pointer" @click="viewAfterDetail(record)">
            {{ record.after_no }}
          </div>
        </div>
      </template>

      <!-- 来源订单列 -->
      <template #order_no-cell="{ record }">
        <div class="order-no">
          <div class="text-blue-500 cursor-pointer" @click="viewOrderDetail(record.order_id)">
            {{ record.order_no }}
          </div>
        </div>
      </template>

      <!-- 商品信息列 -->
      <template #product_info-cell="{ record }">
        <div v-if="typeof record.product === 'object'" class="product-info flex items-center">
          <a-image
            :src="record.product.image || '/assets/images/default-product.png'"
            width="60"
            height="60"
            class="mr-3 rounded-md border border-gray-200 object-cover flex-shrink-0"
            fit="cover"
          />
          <div class="flex flex-col justify-center flex-1 overflow-hidden">
            <div class="product-name text-gray-900 font-medium mb-1 line-clamp-2 text-sm">{{ record.product.name }}</div>
            <div class="product-meta flex flex-col space-y-0.5">
              <div class="product-code text-gray-500 text-xs">
                <span class="inline-block w-14 text-gray-400">商品编码</span>
                <span class="font-mono">{{ record.product.code }}</span>
              </div>
              <div class="product-spec text-gray-500 text-xs">
                <span class="inline-block w-14 text-gray-400">规格</span>
                <a-tag size="small" color="blue" >{{ record.product.spec }}</a-tag>
              </div>
              <div class="product-price text-red-500 text-xs mt-0.5">
                <span class="inline-block w-14 text-gray-400">销售价</span>
                <span class="text-red-500 font-medium">¥{{ record.product.price }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 售后类型列 -->
      <template #after_type_text="{ record }">
        <div class="after-type">
       
          <a-tag :color="getAfterTypeColor(record.after_type)" size="small" class="font-medium">
            {{ record.after_type_text || getAfterTypeText(record.after_type) }}
          </a-tag>
        </div>
      </template>

      <!-- 售后状态列 -->
      <template #status_text="{ record }">
        <div class="after-status">
          <a-tag :color="getStatusColor(record.status)" size="small" class="font-medium">
            {{ record.status_text || getStatusText(record.status) }}
          </a-tag>
        </div>
      </template>

      <!-- 操作列 -->
      <template #operations="{ record }">
        <div class="operations-btns">
          <!-- 详情按钮，所有状态都显示 -->
          <a-button type="text" @click="viewAfterDetail(record)" class="mr-2">
           
            详情
          </a-button>
          
          <!-- 待处理状态 -->
          <!-- <template v-if="record.status === 'pending'">
            <a-button type="text" @click="handleAfterSale(record, 'accept')" class="mr-2">
              <template #icon><icon-check-circle /></template>
              受理
            </a-button>
            <a-button type="text" status="danger" @click="handleAfterSale(record, 'reject')">
              <template #icon><icon-close-circle /></template>
              拒绝
            </a-button>
          </template> -->
          
          <!-- 处理中状态 -->
          <!-- <template v-else-if="record.status === 'processing'">
            <a-button type="text" @click="handleAfterSale(record, 'complete')">
              <template #icon><icon-check-circle /></template>
              完成处理
            </a-button>
          </template> -->
        </div>
      </template>
    </ma-crud>

    <!-- 售后详情抽屉 -->
    <a-drawer
      :visible="detailDrawerVisible"
      @update:visible="(val) => detailDrawerVisible = val"
      title="售后详情"
      width="70%"
      :footer="true"
      unmountOnClose
    >
      <template #title>
        售后详情
      </template>
      
      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="detailDrawerVisible = false" class="mr-2">关闭</a-button>
          
          <!-- 待处理状态 -->
          <template v-if="currentAfterSale && currentAfterSale.status === 'pending'">
            <a-button type="primary" status="success" @click="handleAfterSale(currentAfterSale, 'accept')" class="mr-2">
              受理
            </a-button>
            <a-button type="primary" status="warning" @click="handleAfterSale(currentAfterSale, 'reject')">
              拒绝
            </a-button>
          </template>
          
          <!-- 处理中状态 -->
          <template v-if="currentAfterSale && currentAfterSale.status === 'processing'">
            <a-button type="primary" status="success" @click="handleAfterSale(currentAfterSale, 'complete')">
              完成处理
            </a-button>
          </template>
        </div>
      </template>
      
      <div class="after-detail-container" v-if="currentAfterSale">
        <!-- 售后基本信息头部 -->
        <div class="after-header mb-4 pb-4 border-b border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="flex flex-col">
                <h3 class="text-lg font-medium">售后单号：{{ currentAfterSale.after_no }}</h3>
                <div class="text-gray-500 text-sm mt-1">申请时间：{{ currentAfterSale.created_at }}</div>
              </div>
            </div>
            <div>
              <a-tag :color="getStatusColor(currentAfterSale.status)" size="small" class="font-medium">
                {{ currentAfterSale.status_text || getStatusText(currentAfterSale.status) }}
              </a-tag>
            </div>
          </div>
        </div>
        
        <!-- 选项卡导航 -->
        <a-tabs :active-key="detailActiveTab" @update:active-key="(key) => detailActiveTab = key">
          <!-- 基础信息 -->
          <a-tab-pane key="1" title="基础信息">
            <div class="info-section">
              <a-card title="基本信息" class="mb-4">
                <a-descriptions :data="afterBaseInfo" layout="inline-horizontal" bordered />
              </a-card>
            </div>
          </a-tab-pane>
          
          <!-- 订单信息 -->
          <a-tab-pane key="2" title="订单信息">
            <div class="info-section">
              <a-card title="订单信息" class="mb-4">
                <a-descriptions :data="orderInfo" layout="inline-horizontal" bordered />
              </a-card>
            </div>
          </a-tab-pane>
          
          <!-- 商品信息 -->
          <a-tab-pane key="3" title="商品信息">
            <div class="info-section">
              <a-card title="商品信息" class="mb-4">
                <div class="product-detail p-4 border border-gray-200 rounded">
                  <a-table :columns="productColumns" :data="[currentAfterSale.product]" :pagination="false" border="#f0f0f0" stripe>
                    <template #name="{ record }">
                      <div class="flex items-center">
                        <a-image
                          :src="record.image || '/assets/images/default-product.png'"
                          width="60"
                          height="60"
                          class="mr-3 rounded-md border border-gray-200 object-cover flex-shrink-0"
                          fit="cover"
                        />
                        <div class="flex flex-col justify-center flex-1 overflow-hidden">
                          <div class="product-name text-gray-900 font-medium mb-1 line-clamp-2 text-sm">
                            {{ record.name }}
                          </div>
                          <div class="product-meta flex flex-col space-y-0.5">
                            <div class="product-code text-gray-500 text-xs">
                              <span class="inline-block w-14 text-gray-400">商品编码</span>
                              <span class="font-mono">{{ record.code }}</span>
                            </div>
                            <div class="product-spec text-gray-500 text-xs">
                              <span class="inline-block w-14 text-gray-400">规格</span>
                              <a-tag size="small"   color="blue" >{{ record.spec }}</a-tag>
                            </div>
                            <div class="product-price text-red-500 text-xs mt-0.5">
                              <span class="inline-block w-14 text-gray-400">销售价</span>
                              <span class="font-medium">¥{{ record.price }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template #price="{ record }">
                      <div class="product-price">
                        <!-- <span class="inline-block w-16 text-gray-400 text-sm">销售价</span> -->
                        <span class="text-red-500 font-medium">¥{{ record.price }}</span>
                      </div>
                    </template>
                    <template #total="{ record }">
                      <div class="product-total">
                        <!-- <span class="inline-block w-16 text-gray-400 text-sm">小计</span> -->
                        <span class="text-red-500 font-medium">¥{{ record.total }}</span>
                      </div>
                    </template>
                  </a-table>
                </div>
              </a-card>
            </div>
          </a-tab-pane>
          
          <!-- 售后信息 -->
          <a-tab-pane key="4" title="售后信息">
            <div class="info-section">
              <a-card title="售后原因" class="mb-4">
                <div class="p-4 border border-gray-200 rounded">
                  <div class="reason-type mb-2">
                    <span class="text-gray-500">售后类型：</span>
                    <a-tag :color="getAfterTypeColor(currentAfterSale.after_type)">
                      {{ getAfterTypeText(currentAfterSale.after_type) }}
                    </a-tag>
                  </div>
                  <div class="reason-content mb-2">
                    <div class="text-gray-500 mb-1">售后原因：</div>
                    <div class="p-2 bg-gray-50 rounded">{{ currentAfterSale.reason }}</div>
                  </div>
                  <div class="reason-images" v-if="currentAfterSale.images && currentAfterSale.images.length">
                    <div class="text-gray-500 mb-2">问题图片：</div>
                    <div class="flex flex-wrap gap-2">
                      <a-image
                        v-for="(img, index) in currentAfterSale.images"
                        :key="index"
                        :src="img"
                        width="80"
                        height="80"
                        class="rounded"
                        fit="cover"
                      />
                    </div>
                  </div>
                </div>
              </a-card>
              
              <a-card title="处理记录" v-if="currentAfterSale.logs && currentAfterSale.logs.length">
                <a-timeline>
                  <a-timeline-item
                    v-for="(log, index) in currentAfterSale.logs"
                    :key="index"
                    :dot-color="getLogColor(log.action)"
                  >
                    <div class="log-item">
                      <div class="log-action font-medium">{{ getLogActionText(log.action) }}</div>
                      <div class="log-time text-gray-500 text-sm">{{ log.time }}</div>
                      <div class="log-operator text-gray-500 text-sm">操作人：{{ log.operator }}</div>
                      <div class="log-remark mt-1" v-if="log.remark">
                        备注：{{ log.remark }}
                      </div>
                    </div>
                  </a-timeline-item>
                </a-timeline>
              </a-card>
            </div>
          </a-tab-pane>
          
          <!-- 收货信息 -->
          <a-tab-pane key="5" title="收货信息">
            <div class="info-section">
              <a-card title="收货信息" class="mb-4">
                <a-descriptions :data="receiverInfo" layout="inline-horizontal" bordered />
              </a-card>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-drawer>
  </div>
</template>

<script>
export default { name: "provider-ordermanagement-after" };
</script>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// Nuxt 2 不需要使用 definePageMeta

// 引用
const crudRef = ref(null);
const activeTab = ref('all'); // 默认选中全部标签页
const detailDrawerVisible = ref(false);
const currentAfterSale = ref(null);
const detailActiveTab = ref('1'); // 详情抽屉选项卡默认选中第一个

// 表格数据相关
const tableData = ref([]);
const loading = ref(false);
const searchParams = ref({});
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 50, 100]
});

// 售后类型映射
const afterTypeMap = {
  'refund': { text: '仅退款', color: 'orangered' },
  'return_refund': { text: '退货退款', color: 'red' },
  'logistics': { text: '物流问题', color: 'blue' },
  'product_issue': { text: '产品问题', color: 'purple' },
  'installation': { text: '产品安装', color: 'green' },
  'exchange': { text: '退换货', color: 'magenta' }
};

// 售后状态映射
const statusMap = {
  'pending': { text: '待处理', color: 'orange' },
  'processing': { text: '处理中', color: 'blue' },
  'completed': { text: '已完成', color: 'green' },
  'rejected': { text: '已拒绝', color: 'red' }
};

// 获取售后类型文本和颜色
const getAfterTypeText = (type) => {
  return (afterTypeMap[type] && afterTypeMap[type].text) || '未知类型';
};
const getAfterTypeColor = (type) => {
    console.log(type,'type');
    
  return (afterTypeMap[type] && afterTypeMap[type].color) || 'default';
};

// 获取状态文本和颜色
const getStatusText = (status) => {
  return (statusMap[status] && statusMap[status].text) || '未知状态';
};
const getStatusColor = (status) => {
  return (statusMap[status] && statusMap[status].color) || 'default';
};

// 获取日志操作文本和颜色
const getLogActionText = (action) => {
  const actionMap = {
    'create': '创建售后申请',
    'accept': '受理售后申请',
    'reject': '拒绝售后申请',
    'process': '处理中',
    'complete': '完成处理'
  };
  return actionMap[action] || action;
};

const getLogColor = (action) => {
  const colorMap = {
    'create': 'blue',
    'accept': 'green',
    'reject': 'red',
    'process': 'orange',
    'complete': 'green'
  };
  return colorMap[action] || 'gray';
};

// 商品表格列定义
const productColumns = [
  {
    title: '商品名称',
    dataIndex: 'name',
    slotName: 'name',
    width: 300
  },
  {
    title: '商品规格',
    dataIndex: 'spec',
    width: 150
  },
  {
    title: '商家',
    dataIndex: 'merchant',
    width: 150
  },
  {
    title: '单价',
    dataIndex: 'price',
    slotName: 'price',
    width: 100
  },
  {
    title: '购买数量',
    dataIndex: 'quantity',
    width: 100
  },
  {
    title: '小计',
    dataIndex: 'total',
    slotName: 'total',
    width: 100
  }
];

// 售后基本信息计算属性
const afterBaseInfo = computed(() => {
  if (!currentAfterSale.value) return [];
  
  return [
    {
      label: '售后单号',
      value: currentAfterSale.value.after_no
    },
    {
      label: '来源订单',
      value: currentAfterSale.value.order_no
    },
    {
      label: '申请时间',
      value: currentAfterSale.value.created_at
    },
    {
      label: '售后类型',
      value: () => {
        const afterType = currentAfterSale.value.after_type;
        const afterTypeText = currentAfterSale.value.after_type_text || getAfterTypeText(afterType);
        return h('div', {}, [
          h('a-tag', {
            color: getAfterTypeColor(afterType),
            size: 'small',
            class: 'font-medium'
          }, afterTypeText)
        ]);
      }
    },
    {
      label: '状态',
      value: () => {
        const status = currentAfterSale.value.status;
        const statusText = currentAfterSale.value.status_text || getStatusText(status);
        return h('div', {}, [
          h('a-tag', {
            color: getStatusColor(status),
            size: 'small',
            class: 'font-medium'
          }, statusText)
        ]);
      }
    },
    {
      label: '申请人',
      value: currentAfterSale.value.customer_name
    }
  ];
});

// 订单信息计算属性
const orderInfo = computed(() => {
  if (!currentAfterSale.value) return [];
  
  return [
    {
      label: '订单编号',
      value: currentAfterSale.value.order_no
    },
    {
      label: '下单时间',
      value: currentAfterSale.value.order_time || '2023-05-15 14:30:25'
    },
    {
      label: '订单来源',
      value: currentAfterSale.value.order_source || '官网商城'
    },
    {
      label: '支付方式',
      value: currentAfterSale.value.payment_method || '微信支付'
    },
    {
      label: '订单金额',
      value: `¥${currentAfterSale.value.order_amount || '0.00'}`
    },
    {
      label: '订单状态',
      value: currentAfterSale.value.order_status || '已完成'
    }
  ];
});

// 收货信息计算属性
const receiverInfo = computed(() => {
  if (!currentAfterSale.value || !currentAfterSale.value.receiver) return [];
  
  const receiver = currentAfterSale.value.receiver;
  
  return [
    {
      label: '收货人',
      value: receiver.name || currentAfterSale.value.customer_name
    },
    {
      label: '联系电话',
      value: receiver.phone || currentAfterSale.value.customer_phone || '13800138000'
    },
    {
      label: '收货地址',
      value: receiver.address || '北京市朝阳区建国路88号'
    },
    {
      label: '配送方式',
      value: receiver.delivery_method || '快递配送'
    },
    {
      label: '物流单号',
      value: currentAfterSale.value.tracking_no || '暂无数据'
    }
  ];
});

// 表格列定义
const columns = reactive([
  {
    title: '售后单号',
    dataIndex: 'after_no',
    width: 180,
    search: true,
    slotName: 'after_no-cell'
  },
  {
    title: '来源订单',
    dataIndex: 'order_no',
    width: 180,
    search: true,
    slotName: 'order_no-cell'
  },
  {
    title: '商品信息',
    dataIndex: 'product_info-cell',
    width: 300,
    // slotName: 'product_info-cell'
  },
  {
    title: '售后类型',
    dataIndex: 'after_type_text',
    width: 120,
    search: true,
    formType: 'select',
    dict: {
      data: [
        { label: '全部', value: '' },
        { label: '仅退款', value: '仅退款' },
        { label: '退货退款', value: '退货退款' },
        { label: '物流问题', value: '物流问题' },
        { label: '产品问题', value: '产品问题' },
        { label: '产品安装', value: '产品安装' },
        { label: '退换货', value: '退换货' }
      ]
    },
    slotName: 'after_type-cell'
  },
  {
    title: '售后原因',
    dataIndex: 'reason',
    width: 200,
    ellipsis: true
  },
  {
    title: '售后状态',
    dataIndex: 'status_text',
    width: 100,
    search: true,
    formType: 'select',
    dict: {
      data: [
        { label: '全部', value: '' },
        { label: '待处理', value: '待处理' },
        { label: '处理中', value: '处理中' },
        { label: '已完成', value: '已完成' },
        { label: '已拒绝', value: '已拒绝' }
      ]
    },
    slotName: 'status-cell'
  },
  {
    title: '客户名称',
    dataIndex: 'customer_name',
    width: 120,
    search: true
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    width: 180,
    search: true,
    formType: 'date-range',
    searchSpan: 8
  },
  {
    title: '操作',
    dataIndex: 'operations',
    width: 100,
    fixed: 'center',
    slotName: 'operations-cell'
  }
]);

// CRUD 配置
const crud = reactive({
  searchLabelWidth: '130px',
  // 添加按钮配置
  add: {
    show: false
  },
  // 导出按钮配置
  export: {
    show: true,
    text: '导出'
  },
  // 分页配置
  pagination: {
    pageSize: 10,
    pageSizes: [10, 20, 50, 100]
  },
  // 表格配置
  table: {
    border: true,
    stripe: true,
    rowKey: 'id'
  },
  // API 配置
  api: {
    // 将 fetchAfterSaleList 函数作为 api 的 list 方法
    list: fetchAfterSaleList
  }
});

// 处理标签页切换
const handleTabChange = (key) => {
  activeTab.value = key;
  // 根据标签页筛选数据
  if (key === 'all') {
    searchParams.value.status = '';
    searchParams.value.status_text = '';
  } else {
    // 获取对应的状态文本
    const statusObj = statusMap[key];
    searchParams.value.status = key;
    searchParams.value.status_text = statusObj ? statusObj.text : '';
  }
  fetchData();
};

// 处理分页变化
const handlePageChange = (page) => {
  pagination.page = page;
  fetchData();
};

// 处理搜索
const handleSearch = (params) => {
  searchParams.value = { ...params };
  pagination.page = 1;
  fetchData();
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const result = await fetchAfterSaleList({
      ...searchParams.value,
      page: pagination.page,
      pageSize: pagination.pageSize
    });
    tableData.value = result.list;
    pagination.total = result.total;
  } catch (error) {
    console.error('获取售后数据失败:', error);
    Message.error('获取售后数据失败');
  } finally {
    loading.value = false;
  }
};

// 查看售后详情
const viewAfterDetail = (record) => {
  currentAfterSale.value = record;
  detailDrawerVisible.value = true;
};

// 查看订单详情
const viewOrderDetail = (orderId) => {
  router.push(`/provider/ordermanagement/detail/${orderId}`);
};

// 处理售后申请
const handleAfterSale = (record, action) => {
  let title = '';
  let content = '';
  
  switch (action) {
    case 'accept':
      title = '受理售后申请';
      content = `确定要受理售后单号为 ${record.after_no} 的申请吗？`;
      break;
    case 'reject':
      title = '拒绝售后申请';
      content = `确定要拒绝售后单号为 ${record.after_no} 的申请吗？`;
      break;
    case 'complete':
      title = '完成售后处理';
      content = `确定要完成售后单号为 ${record.after_no} 的处理吗？`;
      break;
    default:
      return;
  }
  
  Modal.confirm({
    title,
    content,
    onOk: () => {
      // 模拟处理售后申请
      // 在实际项目中，这里应该调用API
      setTimeout(() => {
        // 更新本地数据状态
        const index = tableData.value.findIndex(item => item.id === record.id);
        if (index !== -1) {
          const updatedRecord = { ...tableData.value[index] };
          if (action === 'accept') {
            updatedRecord.status = 'processing';
            updatedRecord.logs = [...updatedRecord.logs, {
              action: 'accept',
              time: new Date().toLocaleString(),
              operator: '系统管理员',
              remark: '已受理售后申请'
            }];
          } else if (action === 'reject') {
            updatedRecord.status = 'rejected';
            updatedRecord.logs = [...updatedRecord.logs, {
              action: 'reject',
              time: new Date().toLocaleString(),
              operator: '系统管理员',
              remark: '拒绝售后申请，原因：不符合售后条件'
            }];
          } else if (action === 'complete') {
            updatedRecord.status = 'completed';
            updatedRecord.logs = [...updatedRecord.logs, {
              action: 'complete',
              time: new Date().toLocaleString(),
              operator: '系统管理员',
              remark: '售后处理完成'
            }];
          }
          tableData.value.splice(index, 1, updatedRecord);
          
          // 如果当前正在查看该售后单的详情，也更新详情数据
          if (currentAfterSale.value && currentAfterSale.value.id === record.id) {
            currentAfterSale.value = updatedRecord;
          }
        }
        
        Message.success(`已${getActionText(action)}售后申请`);
        
        // 如果在特定标签页下，可能需要重新加载数据
        if (activeTab.value !== 'all') {
          fetchData();
        }
      }, 500);
    }
  });
};

// 获取操作文本
const getActionText = (action) => {
  const actionTextMap = {
    'accept': '受理',
    'reject': '拒绝',
    'complete': '完成'
  };
  return actionTextMap[action] || action;
};

// 静态模拟数据
const mockDataList = [];

// 生成模拟数据
function generateMockData() {
  if (mockDataList.length > 0) return;
  
  const total = 35;
  
  for (let i = 0; i < total; i++) {
    // 生成状态 - 直接使用中文
    const statusTypes = [
      { code: 'pending', text: '待处理' },
      { code: 'processing', text: '处理中' },
      { code: 'completed', text: '已完成' },
      { code: 'rejected', text: '已拒绝' }
    ];
    const statusIndex = Math.floor(Math.random() * 4);
    let status = statusTypes[statusIndex].code;
    let statusText = statusTypes[statusIndex].text;
    
    // 生成售后类型 - 直接使用中文
    const afterTypes = [
      { code: 'refund', text: '仅退款' },
      { code: 'return_refund', text: '退货退款' },
      { code: 'logistics', text: '物流问题' },
      { code: 'product_issue', text: '产品问题' },
      { code: 'installation', text: '产品安装' },
      { code: 'exchange', text: '退换货' }
    ];
    const afterTypeIndex = Math.floor(Math.random() * afterTypes.length);
    const afterType = afterTypes[afterTypeIndex].code;
    const afterTypeText = afterTypes[afterTypeIndex].text;
    
    // 生成商品信息
    const productPrice = (Math.random() * 1000 + 100).toFixed(2);
    const quantity = Math.floor(Math.random() * 5) + 1;
    const productTotal = (parseFloat(productPrice) * quantity).toFixed(2);
    
    mockDataList.push({
      id: `AS${10000 + i}`,
      after_no: `AS${String(10000 + i).padStart(6, '0')}`,
      order_id: `ORD${10000 + i}`,
      order_no: `ORD${String(10000 + i).padStart(6, '0')}`,
      order_time: new Date(Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000).toLocaleString(),
      order_source: ['官网商城', '微信小程序', '手机APP', '第三方平台'][Math.floor(Math.random() * 4)],
      order_amount: (parseFloat(productTotal) + Math.random() * 50).toFixed(2),
      payment_method: ['微信支付', '支付宝', '银行卡', '货到付款'][Math.floor(Math.random() * 4)],
      order_status: ['已付款', '已发货', '已完成'][Math.floor(Math.random() * 3)],
      
      // 商品信息
      product: {
        name: [
          '高端智能家用空调',
          '全自动滚筒洗衣机',
          '超薄曲面智能电视',
          '多功能料理机',
          '智能扫地机器人',
          '家用净水器',
          '智能门锁',
          '无线蓝牙耳机',
          '智能手表',
          '便携式投影仪'
        ][i % 10] + ` ${i + 1}号`,
        code: `P${String(1000 + i).padStart(4, '0')}`,
        price: productPrice,
        // 使用占位图片URL
        image: `https://picsum.photos/200/200?random=${i}`,
        spec: [
          '标准版-白色',
          '豪华版-黑色',
          '至尊版-金色',
          '简约版-银色',
          '定制版-蓝色'
        ][i % 5],
        quantity: quantity,
        total: productTotal,
        merchant: `供应商${Math.floor(Math.random() * 10) + 1}`
      },
      
      after_type: afterType,
      after_type_text: afterTypeText,
      reason: (() => {
        const reasons = {
          'refund': [
            '商品未收到，申请退款',
            '下错订单，申请退款',
            '不想要了，申请退款',
            '商品降价，申请退差价',
            '商品缺货，申请退款'
          ],
          'return_refund': [
            '商品质量问题，申请退货退款',
            '商品与描述不符，申请退货退款',
            '商品损坏，申请退货退款',
            '收到假货，申请退货退款',
            '商品不喜欢，申请退货退款'
          ],
          'logistics': [
            '物流太慢，申请处理',
            '物流信息长时间未更新',
            '包装破损，申请处理',
            '快递员态度恶劣，申请投诉',
            '收到商品与订单不符，申请处理'
          ],
          'product_issue': [
            '商品使用过程中出现故障',
            '商品零部件缺失',
            '商品不符合安全标准',
            '商品存在设计缺陷',
            '商品功能与描述不符'
          ],
          'installation': [
            '安装师傅未按预约时间上门',
            '安装过程中损坏商品',
            '安装后商品无法正常使用',
            '需要重新安装',
            '安装师傅服务态度差'
          ],
          'exchange': [
            '商品尺寸不合适，申请换货',
            '商品颜色与图片不符，申请换货',
            '收到商品型号错误，申请换货',
            '商品有瑕疵，申请换货',
            '个人原因，申请换其他款式'
          ]
        };
        return reasons[afterType][i % 5] || `申请${getAfterTypeText(afterType)}`;
      })(),
      status,
      status_text: statusText,
      customer_name: `客户${i + 1}`,
      customer_phone: `1381234${String(1000 + i).padStart(4, '0')}`,
      created_at: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toLocaleString(),
      
      // 收货信息
      receiver: {
        name: `收货人${i + 1}`,
        phone: `1381234${String(1000 + i).padStart(4, '0')}`,
        address: `北京市朝阳区建国路${88 + i}号${i % 10 + 1}单元${i % 20 + 1}号`,
        delivery_method: ['快递配送', '上门自提', '同城配送'][Math.floor(Math.random() * 3)],
        tracking_number: i % 3 === 0 ? `SF${1000000 + i}` : ''
      },
      
      images: i % 3 === 0 ? [
        '/assets/images/product-1.jpg',
        '/assets/images/product-2.jpg'
      ] : [],
      
      logs: [
        {
          action: 'create',
          time: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toLocaleString(),
          operator: `客户${i + 1}`,
          remark: '创建售后申请'
        },
        ...(status !== 'pending' ? [{
          action: 'accept',
          time: new Date(Date.now() - Math.floor(Math.random() * 20) * 24 * 60 * 60 * 1000).toLocaleString(),
          operator: '系统管理员',
          remark: '已受理售后申请'
        }] : []),
        ...(status === 'processing' ? [{
          action: 'process',
          time: new Date(Date.now() - Math.floor(Math.random() * 10) * 24 * 60 * 60 * 1000).toLocaleString(),
          operator: '系统管理员',
          remark: '正在处理中'
        }] : []),
        ...(status === 'completed' ? [{
          action: 'complete',
          time: new Date(Date.now() - Math.floor(Math.random() * 5) * 24 * 60 * 60 * 1000).toLocaleString(),
          operator: '系统管理员',
          remark: '售后处理完成'
        }] : []),
        ...(status === 'rejected' ? [{
          action: 'reject',
          time: new Date(Date.now() - Math.floor(Math.random() * 15) * 24 * 60 * 60 * 1000).toLocaleString(),
          operator: '系统管理员',
          remark: '拒绝售后申请，原因：不符合售后条件'
        }] : [])
      ]
    });
  }
}

// 获取售后列表数据
async function fetchAfterSaleList(params) {
  // 模拟API调用
  console.log('查询参数:', params);
  
  // 确保模拟数据已生成
  generateMockData();
  
  // 模拟后端分页数据
  const pageSize = params.pageSize || 10;
  const currentPage = params.page || 1;
  
  // 过滤数据
  let filteredData = [...mockDataList];
  
  // 根据状态过滤
  if (params.status) {
    filteredData = filteredData.filter(item => item.status === params.status);
  }
  
  // 根据状态文字过滤
  if (params.status_text) {
    filteredData = filteredData.filter(item => item.status_text === params.status_text);
  }
  
  // 根据售后单号搜索
  if (params.after_no) {
    filteredData = filteredData.filter(item => item.after_no.includes(params.after_no));
  }
  
  // 根据订单号搜索
  if (params.order_no) {
    filteredData = filteredData.filter(item => item.order_no.includes(params.order_no));
  }
  
  // 根据售后类型搜索
  if (params.after_type) {
    filteredData = filteredData.filter(item => item.after_type === params.after_type);
  }
  
  // 根据售后类型文字搜索
  if (params.after_type_text) {
    filteredData = filteredData.filter(item => item.after_type_text === params.after_type_text);
  }
  
  // 根据客户名称搜索
  if (params.customer_name) {
    filteredData = filteredData.filter(item => item.customer_name.includes(params.customer_name));
  }
  
  // 模拟分页
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pagedData = filteredData.slice(startIndex, endIndex);
  
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return {
    list: pagedData,
    total: filteredData.length
  };
}

// 页面初始化
onMounted(() => {
  // 初始化数据
  fetchData();
});
</script>

<style scoped lang="less">
.after-detail {
  .product-detail {
    background-color: #fff;
  }
  
  .log-item {
    margin-bottom: 12px;
    
    .log-action {
      font-size: 14px;
    }
    
    .log-time, .log-operator {
      font-size: 12px;
      color: #999;
    }
    
    .log-remark {
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      margin-top: 4px;
      font-size: 12px;
    }
  }
}

:deep(.arco-table-tr:hover td) {
  background-color: var(--color-bg-2) !important;
}

:deep(.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr-selected td) {
  background-color: transparent !important;
}

:deep(.arco-table-tr:focus-within) {
  background-color: transparent !important;
  outline: none !important;
  border: none !important;
}

:deep(.arco-table-tr.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected td) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected:hover td) {
  background-color: var(--color-bg-2) !important;
}

:deep(.arco-table-td) {
  border-right: none !important;
}
</style>