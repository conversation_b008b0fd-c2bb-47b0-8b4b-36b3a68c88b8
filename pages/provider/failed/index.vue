<template>
  <div class="min-h-screen bg-gray-50 py-8 relative">
    <div
      class="absolute inset-0 z-0"
      :style="{ backgroundImage: `url(${bgImage})`, backgroundRepeat: 'no-repeat', backgroundPosition: 'center center', backgroundSize: 'cover' }"
    ></div>

    <!-- 审核不通过 -->
    <div class="relative z-10 register-gradient">
      <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8" style="width: 100%">
        <div class="register-success">
          <icon-close-circle class="error-icon text-6xl text-green-500 mb-4" />
          <h2 class="text-2xl font-semibold mb-2">审核不通过</h2>
          <p class="text-gray-600 mb-6">
            很遗憾，您提交的企业资料未能审核通过。请按照驳回原因修改并重新提交，感谢支持
          </p>
          <p>驳回原因：{{ rejectReason||'无' }}</p>
          <div class="flex justify-center">
            <a-button class="mr-4"  @click="goToRegister">重新填写</a-button>
            <a-button
              type="primary"
              @click="goToLogin"
              class=" bg-gradient-to-r from-red-500 to-orange-500 border-none font-semibold"
            >返回登录</a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script setup >
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
const bgImage = new URL(
  "@/assets/images/provider/base-map.jpg",
  import.meta.url
).href;
definePageMeta({
  layout: false,
  name: "provider-failed",
  path: "/provider/failed",
  title: "企业信息上传失败"
});

// 当前步骤
let currentStep = ref(1);
const router = useRouter()
const rejectReason = ref(router.currentRoute.value.query.remark)
// 表单引用
const basicFormRef = ref(null);
const companyFormRef = ref(null);

// 基础信息表单数据
const basicForm = reactive({
  username: "",
  nickname: "",
  password: "",
  confirmPassword: "",
  email: "",
  phone: "",
  verificationCode: ""
});

// 企业信息表单数据
const companyForm = reactive({
  // 基本信息
  companyName: "",

  // 营业执照
  businessLicense: "",
  businessLicenseList: [],

  // 法人身份证
  idCardFrontList: [],
  idCardBackList: [],

  // 纳税评级
  taxLevel: "",
  taxCertificateList: [],

  // 办公地址
  province: "",
  detailAddress: "",

  // 联系人信息
  contacts: [
    {
      position: "", // 职务
      name: "", // 姓名
      phone: "", // 电话
      email: "" // 邮箱
    }
  ],

  // 银行信息
  bankName: "", // 开户银行
  bankBranch: "", // 开户网点
  bankAccount: "", // 对公银行账号
  bankCertificateList: [] // 开户证明
});

// 处理营业执照上传
const handleBusinessLicenseChange = fileList => {
  companyForm.businessLicenseList = fileList;
  if (fileList && fileList.length > 0) {
    companyForm.businessLicense = fileList[0].url || "";
  } else {
    companyForm.businessLicense = "";
  }
};

// 处理法人身份证正面上传
const handleIdCardFrontChange = fileList => {
  companyForm.idCardFrontList = fileList;
};

// 处理法人身份证背面上传
const handleIdCardBackChange = fileList => {
  companyForm.idCardBackList = fileList;
};

// 处理纳税评级证明上传
const handleTaxCertificateChange = fileList => {
  companyForm.taxCertificateList = fileList;
};

// 处理银行开户证明上传
const handleBankCertificateChange = fileList => {
  companyForm.bankCertificateList = fileList;
};

// 添加联系人
const addContact = () => {
  companyForm.contacts.push({
    position: "",
    name: "",
    phone: "",
    email: ""
  });
};

// 删除联系人
const removeContact = index => {
  if (companyForm.contacts.length > 1) {
    companyForm.contacts.splice(index, 1);
  } else {
    Message.warning("至少需要保留一个联系人");
  }
};

// 短信验证码相关
const countdown = ref(0);
const timer = ref(null);

// 获取短信验证码
const getVerificationCode = async () => {
  if (countdown.value > 0) return;

  // 验证手机号
  if (!basicForm.phone || !/^1[3-9]\d{9}$/.test(basicForm.phone)) {
    Message.error("请输入正确的手机号");
    return;
  }

  try {
    // 这里应该调用发送短信验证码的API
    // 模拟发送成功
    countdown.value = 60;
    Message.success("验证码已发送到您的手机");

    // 倒计时
    timer.value = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer.value);
      }
    }, 1000);
  } catch (error) {
    console.error("发送验证码失败:", error);
    Message.error("发送验证码失败，请稍后重试");
  }
};

// 返回信息提交页面，并传递edit=true参数表示编辑模式
const goToRegister = () => {
  navigateTo({
    path: "/provider/information",
    query: { edit: "true" }
  });
};

// 提交表单
const nextStep = async () => {
  try {
    // 打印当前表单数据，用于调试
    console.log("当前表单数据:", companyForm);

    // 验证必填字段
    const requiredFields = {
      businessLicenseList: "营业执照",
      idCardFrontList: "法人身份证人像面",
      idCardBackList: "法人身份证国德面",
      //   taxLevel: '纳税评级等级',
      taxCertificateList: "纳税评级证明",
      province: "办公地址",
      detailAddress: "详细地址",
      contacts: "联系人",
      bankName: "开户银行",
      bankBranch: "开户网点",
      bankAccount: "对公银行账号",
      bankCertificateList: "开户证明"
    };

    // 检查必填字段是否填写
    const missingFields = [];
    for (const [field, label] of Object.entries(requiredFields)) {
      // 打印每个字段的值，用于调试
      console.log(`检查字段 ${field}:`, companyForm[field]);

      // 特殊处理文件上传列表
      if (field.endsWith("List")) {
        if (!companyForm[field] || companyForm[field].length === 0) {
          console.log(`文件列表 ${field} 为空`);
          missingFields.push(label);
        }
        continue;
      }
      // 特殊处理联系人数组
      if (field === "contacts") {
        if (!companyForm[field] || companyForm[field].length === 0) {
          console.log("联系人数组为空");
          missingFields.push(label);
        }
        continue;
      }
      // 其他普通字段
      if (!companyForm[field]) {
        console.log(`字段 ${field} 为空`);
        missingFields.push(label);
      }
    }

    // 打印缺失的字段，用于调试
    console.log("缺失的字段:", missingFields);

    // 如果有未填写的必填字段，显示错误信息
    if (missingFields.length > 0) {
      throw new Error(`请填写以下必填项：${missingFields.join("、")}`);
    }

    try {
      // 验证表单
      await companyFormRef.value.validate();
      console.log("表单验证通过");

      // TODO: 调用提交API
      // const response = await submitCompanyInfo(companyForm);

      // 显示成功提示
      Message.success("提交成功，请等待审核");
    } catch (validateError) {
      console.error("表单验证失败:", validateError);
      throw validateError;
    }

    // 切换到完成注册模块
    currentStep.value = 2;
  } catch (error) {
    console.error("表单验证失败:", error);
    // 如果是必填项的错误，直接显示错误信息
    if (error.message.includes("请填写以下必填项")) {
      Message.error(error.message);
    } else {
      // 如果是其他验证错误，显示默认错误信息
      Message.error("请填写完整的信息后再提交");
    }
  }
};

// 返回登录页
const goToLogin = () => {
  navigateTo("/provider/index");
};

// 返回上一步
const goBack = () => {
  navigateTo("/provider/index");
};
</script>
  
  <style scoped lang="less">
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  flex-direction: column;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
}
.register {
  position: absolute;
  font-size: 20px;
  top: 173px;
  left: 563px;
  font-weight: 400;
}

.Improve {
  margin-left: -675px;
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 15px;
}

.register-content {
  width: 100%;
  max-width: 800px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.steps-wrapper {
  margin-bottom: 40px;

  :deep(.arco-steps-item-title) {
    font-weight: 500;
  }
  ::v-deep .arco-steps-item-node {
    display: inline-block;
    // margin-right: 12px;
    font-weight: 500;
    font-size: 16px;
    // vertical-align: top;
  }
}
::v-deep .arco-form-item-content-flex {
  display: flex;
  align-items: flex-start;
  justify-items: flex-start;
  flex-direction: column;
}
.step-icon {
  background-color: #e8f3ff;
  color: #4080ff;

  &.active {
    background-color: #4080ff;
    color: #fff;
  }
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
}

.form-step {
  min-height: 400px;
  display: flex;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.verification-code {
  display: flex;
  gap: 10px;

  .get-code-btn {
    white-space: nowrap;
    min-width: 120px;
  }
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.id-card-upload {
  display: flex;
  gap: 20px;
}

.contacts-list {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  width: 100%;
  //   background-color: #fafafa;
}

.contact-item {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e5e6eb;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 500;
}

.contact-form {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px;
}

.add-contact {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 自定义上传组件样式 */
.custom-upload {
  :deep(.arco-upload-picture-card) {
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    background-color: #fafafa;
    transition: border-color 0.3s;

    &:hover {
      border-color: #4080ff;
    }
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    color: #999;
    border: 1px dashed;
  }

  .upload-icon {
    font-size: 24px;
    color: #c0c4cc;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 12px;
    color: #909399;
  }
}

.register-success {
  text-align: center;
  padding: 40px 0;

  .success-icon {
    font-size: 60px;
    color: #00b42a;
    margin-bottom: 16px;
  }

  h2 {
    font-size: 24px;
    margin-bottom: 16px;
    color: #1d2129;
  }

  p {
    font-size: 16px;
    color: #4e5969;
    margin-bottom: 30px;
  }
}

:deep(.arco-form-item-label-col) {
  label::before {
    margin-right: 4px;
  }
}
.register-gradient {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90vh;
}
.register-title {
  width: 800px;
  margin-left: 574px;
  margin-bottom: 15px;
  font-size: 20px;
  font-weight: 400;
  color: #ffffff;
}
</style>
  