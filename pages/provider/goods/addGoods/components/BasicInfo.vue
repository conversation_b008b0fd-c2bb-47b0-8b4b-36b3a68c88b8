<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div>
    <a-card class="mb-4">
      <ma-form
        ref="basicFormRef"
        v-model="form"
        :columns="basicFormColumns"
        layout="vertical"
        :options="{ showButtons: false }"
      >
      </ma-form>
    </a-card>

    <a-card title="商品标签" class="mb-4">
      <div class="flex items-center mb-2">
        <a-checkbox-group v-model="associations.tagIds">
          <a-checkbox v-for="item in labelData" :key="item.id" :value="item.id">
            {{ item.name }}
          </a-checkbox>
        </a-checkbox-group>
        <a-button type="text" @click="openLabelManage" class="ml-4">
          <template #icon><icon-plus /></template>
          标签管理
        </a-button>
      </div>
    </a-card>

    <a-card title="商品服务" class="mb-4">
      <div class="flex items-center mb-2">
        <a-checkbox-group v-model="associations.serviceIds">
          <a-checkbox
            v-for="item in serviceData"
            :key="item.id"
            :value="item.id"
          >
            {{ item.name }}
          </a-checkbox>
        </a-checkbox-group>
        <a-button type="text" @click="openServiceManage" class="ml-4">
          <template #icon><icon-plus /></template>
          商品服务
        </a-button>
      </div>
    </a-card>

    <!-- 物流设置 -->
    <a-card title="物流设置" class="mb-4">
      <div class="flex">
        <div style="width: 300px">
          <ma-form
            ref="logisticsFormRef"
            v-model="logisticsForm"
            :columns="logisticsFormColumns"
            layout="vertical"
            :options="{ showButtons: false }"
          />
        </div>
        <a-button
          v-if="parseInt(logisticsForm.isFreeShipping) === 2"
          type="text"
          @click="openFreightManage"
          style="margin-left: 15px; margin-top: 52px"
        >
          <template #icon><icon-plus /></template>
          运费模板管理
        </a-button>
      </div>
    </a-card>

    <!-- 配送信息 -->
    <a-card title="配送信息" class="mb-4">
      <ma-form
        ref="deliveryFormRef"
        v-model="deliveryForm"
        :columns="deliveryFormColumns"
        layout="vertical"
        :options="{ showButtons: false }"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconPlus } from "@arco-design/web-vue/es/icon";
import MaUpload from "~/components/base/ma-upload/index.vue";
import goodsApi from "@/api/master/goods";
const brandApi = goodsApi.brand;
const goodsCategoryApi = goodsApi.category;
const freightTemplateApi = goodsApi.freightTemplate;
const serviceApi = goodsApi.service;
const tagApi = goodsApi.tag;
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  associations: {
    type: Object,
    default: () => ({
      tagIds: [],
      serviceIds: [],
    }),
  },
  logisticsForm: {
    type: Object,
    default: () => ({
      isFreeShipping: 1,
      freightTemplateId: "",
    }),
  },
  deliveryForm: {
    type: Object,
    default: () => ({
      deliveryArea: "",
      deliveryTime: "",
    }),
  },
});

const emit = defineEmits([
  "update:modelValue",
  "update:associations",
  "update:logisticsForm",
  "update:deliveryForm",
  "open-label-manage",
  "open-service-manage",
  "change-category-id",
]);

// 基本信息表单 - 直接使用父组件传入的值
const form = reactive({ ...props.modelValue });

// 商品标签服务表单 - 直接使用父组件传入的值
const associations = reactive({ ...props.associations });

// 物流表单 - 直接使用父组件传入的值
const logisticsForm = reactive({ ...props.logisticsForm });

// 配送信息表单 - 直接使用父组件传入的值
const deliveryForm = reactive({ ...props.deliveryForm });

// 监听 props 中的 modelValue 变化，实现数据回显
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      Object.assign(form, newVal);
    }
  },
  { deep: true }
);

// 监听 props 中的 associations 变化，实现数据回显
watch(
  () => props.associations,
  (newVal) => {
    if (newVal) {
      Object.assign(associations, newVal);
    }
  },
  { deep: true }
);

// 监听 props 中的 logisticsForm 变化，实现数据回显
watch(
  () => props.logisticsForm,
  (newVal) => {
    if (newVal) {
      Object.assign(logisticsForm, newVal);
    }
  },
  { deep: true }
);

// 监听 props 中的 deliveryForm 变化，实现数据回显
watch(
  () => props.deliveryForm,
  (newVal) => {
    if (newVal) {
      Object.assign(deliveryForm, newVal);
    }
  },
  { deep: true }
);

// 商品分类数据
const categoryData = ref([]);

// 品牌数据
const brandData = ref([]);

// 标签数据
const labelData = ref([]);

// 服务数据
const serviceData = ref([]);

// 获取商品分类数据
const getCategoryData = async () => {
  const res = await goodsCategoryApi.getList();
  let data = res.data;
  return data;
};

// 获取品牌数据
const getBrandData = async () => {
  const res = await brandApi.getList();
  let data = res.data.items;
  return data;
};

// 获取标签数据
const getLabelData = async () => {
  const res = await tagApi.getList();
  let data = res.data.items;
  return data;
};

// 获取服务数据
const getServiceData = async () => {
  const res = await serviceApi.getList();
  let data = res.data.items;
  return data;
};

const router = useRouter();
// 基本信息表单配置
const basicFormColumns = reactive([
  {
    title: "商品名称",
    dataIndex: "name",
    formType: "input",
    placeholder: "请输入商品名称",
    rules: [{ required: true, message: "请输入商品名称" }],
    attrs: {
      maxLength: 100,
    },
  },
  {
    title: "商品分类",
    dataIndex: "categoryId",
    formType: "tree-select",
    placeholder: "请选择商品分类",
    rules: [{ required: true, message: "请选择商品分类" }],
    fieldNames: { key: "id", title: "name" },
    allowSearch: true,
    allowClear: true,
    onChange: (val) => {
      emit('change-category-id',val)
    },
  },
  {
    title: "商品品牌",
    dataIndex: "brandId",
    formType: "select",
    placeholder: "请选择商品品牌",
    rules: [{ required: true, message: "请选择商品品牌" }],
    allowSearch: true,
    allowClear: true,
    events: {
      change: (val) => {
        console.log("数据范围变化：", val);
      },
    },
  },
  {
    title: "副标题",
    dataIndex: "subtitle",
    formType: "textarea",
    placeholder: "请输入副标题",
    attrs: {
      maxLength: 200,
      showWordLimit: true,
      autoSize: { minRows: 3, maxRows: 5 },
    },
  },
  {
    title: "商品图片",
    dataIndex: "spuImages",
    formType: "upload",
    type: "image",
    multiple: true,
    limit: 5,
    accept: ".jpg,.jpeg,.png,.gif",
    tip: "最多上传5张图片，建议尺寸800x800px，大小不超过2MB",
    returnType: "url",
    rules: [{ required: true, message: "请上传至少一张商品图片" }],
  },
  {
    title: "商品视频",
    dataIndex: "video",
    formType: "upload",
    type: "file",
    multiple: false,
    limit: 1,
    accept: ".mp4,.mov",
    tip: "最多上传1个视频，大小不超过50MB",
    returnType: "url",
  },
]);

// 物流表单配置
const logisticsFormColumns = reactive([
  {
    title: "是否包邮",
    dataIndex: "isFreeShipping",
    formType: "radio",
    dict: {
      data: [
        { label: "包邮", value: 1 },
        { label: "不包邮", value: 2 },
      ],
      defaultValue: 1,
    },
    // 使用onControl方法控制运费模板字段的显示隐藏
    onControl: (value, maFormObject) => {
      if (!maFormObject) return;

      const service = maFormObject.getColumnService();
      if (!service) return;

      // 获取运费模板字段
      const field = service.get("freightTemplateId");
      if (field && typeof field.setAttr === "function") {
        // 当选择不包邮时显示运费模板，否则隐藏
        const isNotFreeShipping = parseInt(value) === 2;
        field.setAttr("display", isNotFreeShipping);

        // 调整必填规则
        const rules = isNotFreeShipping
          ? [{ required: true, message: "请选择运费模板" }]
          : [];
        field.setAttr("rules", rules);
      }
    },
  },
  {
    title: "运费模板",
    dataIndex: "freightTemplateId",
    formType: "select",
    placeholder: "请选择运费模板",
    allowSearch: true,
    allowClear: true,
    display: false, // 初始时隐藏，由onControl控制显示
    defaultValue: 1,
    dict: {
      data: async () => {
        const res = await freightTemplateApi.getList();
        const transform = (item) => {
          return {
            value: item.id,
            label: item.name,
            children: item.children ? item.children.map(transform) : undefined,
          };
        };
        return res.data.items.map(transform);
      },
    },
  },
]);

// 配送信息表单配置
const deliveryFormColumns = reactive([
  {
    title: "配送区域",
    dataIndex: "deliveryArea",
    formType: "city-linkage",
    placeholder: "请选择配送区域",
    rules: [{ required: true, message: "请选择配送区域" }],
    type: "cascader",
    mode: "code",
  },
  {
    title: "发货时间",
    dataIndex: "deliveryTime",
    formType: "date",
    placeholder: "请选择发货时间",
    rules: [{ required: true, message: "请选择发货时间" }],
    allowClear: true,
    format: "YYYY-MM-DD",
  },
]);

// 表单引用
const basicFormRef = ref(null);
const logisticsFormRef = ref(null);
const deliveryFormRef = ref(null);

// 打开标签管理
const openLabelManage = () => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, "/master/goods/labelManage");
  });
};

// 打开服务管理
const openServiceManage = () => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, "/master/goods/goodsService");
  });
};

// 打开运费模板管理
const openFreightManage = () => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, "/master/goods/freightTemplate");
  });
};

// 表单验证
const validate = async () => {
  try {
    // 主动同步最新的表单数据到父组件
    emit("update:modelValue", { ...form });
    emit("update:associations", { ...associations });
    emit("update:logisticsForm", { ...logisticsForm });
    emit("update:deliveryForm", { ...deliveryForm });

    // 分别验证三个表单
    const basicFormPromise = basicFormRef.value
      ? basicFormRef.value.validateForm()
      : Promise.resolve(null);
    const logisticsFormPromise = logisticsFormRef.value
      ? logisticsFormRef.value.validateForm()
      : Promise.resolve(null);
    const deliveryFormPromise = deliveryFormRef.value
      ? deliveryFormRef.value.validateForm()
      : Promise.resolve(null);

    // 使用allSettled而不是all，这样即使有一个验证失败也能获取所有结果
    const results = await Promise.allSettled([
      basicFormPromise,
      logisticsFormPromise,
      deliveryFormPromise,
    ]);

    console.log("验证结果详情:", results);

    // 检查是否有验证失败的
    const hasRejected = results.some((result) => result.status === "rejected");

    // 如果有验证没通过的表单，收集错误信息
    if (hasRejected) {
      const errors = results
        .filter((result) => result.status === "rejected")
        .map((result) => result.reason);
      console.error("表单验证失败:", errors);
      throw errors;
    }

    // 检查fulfilled值是否包含错误信息（有些验证框架在成功时返回空值/null/undefined）
    const formErrors = results
      .filter(
        (result) =>
          result.status === "fulfilled" &&
          result.value !== null &&
          result.value !== undefined
      )
      .map((result) => result.value);

    if (formErrors.length > 0) {
      console.error("表单字段验证失败:", formErrors);
      throw formErrors;
    }

    // 验证通过

    // 再次确保将最新数据同步到父组件
    emit("update:modelValue", { ...form });
    emit("update:associations", { ...associations });
    emit("update:logisticsForm", { ...logisticsForm });
    emit("update:deliveryForm", { ...deliveryForm });

    return true;
  } catch (error) {
    console.error("表单验证过程发生错误:", error);
    throw error;
  }
};

// 初始化数据
onMounted(async () => {
  // 获取商品分类数据
  categoryData.value = await getCategoryData();

  // 获取品牌数据
  brandData.value = await getBrandData();

  // 获取标签数据
  labelData.value = await getLabelData();

  // 获取服务数据
  serviceData.value = await getServiceData();

  // 更新表单配置中的数据源
  basicFormColumns.forEach((item) => {
    if (item.dataIndex === "categoryId") {
      item.data = categoryData.value;
    } else if (item.dataIndex === "brandId") {
      // 将品牌数据转换为select组件需要的格式
      item.data = brandData.value.map((brand) => ({
        label: brand.name,
        value: brand.id,
      }));
    }
  });
});

// 监听表单数据变化并同步到父组件
watch(
  () => form,
  (newVal) => {
    emit("update:modelValue", { ...newVal });
  },
  { deep: true, immediate: true }
);

watch(
  () => associations,
  (newVal) => {
    emit("update:associations", { ...newVal });
  },
  { deep: true, immediate: true }
);

watch(
  () => logisticsForm,
  (newVal) => {
    emit("update:logisticsForm", { ...newVal });
  },
  { deep: true, immediate: true }
);

watch(
  () => deliveryForm,
  (newVal) => {
    emit("update:deliveryForm", { ...newVal });
  },
  { deep: true, immediate: true }
);

// 暴露方法给父组件
defineExpose({
  validate,
  basicFormRef,
  logisticsFormRef,
  deliveryFormRef,
  form,
  logisticsForm,
  deliveryForm,
});
</script>
