<template>
  <div>
    <div class="product-line-container">
      <!-- 产品线详情抽屉 -->
      <ProductLineDetailDrawer
        v-model:visible="drawerVisible"
        :product-line-data="currentProductLine"
        @close="handleDrawerClose"
      />
      
      <div class="search-area">

        <!-- 搜索表单 -->
        <ma-crud
          ref="crudRef"
          :options="crudOptions"
          :columns="columns"
          :search="search"
          v-model:search-params="searchParams"
          :data="tableData"
          :loading="loading"
        >

          <!-- 操作列 -->
          <template #operation="{ record }">
            <a-space>
              <a-link @click="handleView(record)">查看详情</a-link>
            </a-space>
            <a-space>
              <a-link @click="handleReset(record)">重置</a-link>
            </a-space>
          </template>

          <!-- 状态列 -->
          <template #status="{ record }">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 是否自主品牌列 -->
          <template #isOwnBrand="{ record }">
            <a-tag :color="record.isOwnBrand ? 'green' : 'gray'">
              {{ record.isOwnBrand ? '是' : '否' }}
            </a-tag>
          </template>
        </ma-crud>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "#app";
import ProductLineDetailDrawer from "./components/ProductLineDetailDrawer.vue";

// 定义页面路由元信息
definePageMeta({
  name:'provider-productLine',
  title: "产品线管理",
  icon: "icon-apps",
});

// 路由
const router = useRouter();

// 抽屉可见性
const drawerVisible = ref(false);
// 当前选中的产品线
const currentProductLine = ref(null);

// 查看产品线详情和关闭抽屉的函数在下方定义

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);
// 当前页码
const currentPage = ref(1);
// 每页条数
const pageSize = ref(10);
// 总条数
const total = ref(0);

// 状态标签卡片已移除，不再需要相关变量和函数

// 表格列配置
const columns = reactive([
  {
    title: "一级分类",
    dataIndex: "categoryLevel1",
    align: "center",
    width: 120,
    search: false,
    searchField: "categoryLevel1",
    placeholder: "请选择一级分类",
  },
  {
    title: "二级分类",
    dataIndex: "categoryLevel2",
    align: "center",
    width: 120,
    search: false,
    searchField: "categoryLevel2",
    placeholder: "请选择二级分类",
  },
  {
    title: "三级分类",
    dataIndex: "categoryLevel3",
    align: "center",
    width: 120,
    search: false,
    searchField: "categoryLevel3",
    placeholder: "请选择三级分类",
  },
  {
    title: "关联品牌",
    dataIndex: "relatedBrand",
    align: "center",
    width: 120,
    search: false,
    searchField: "relatedBrand",
    placeholder: "请输入关联品牌",
  },
  {
    title: "品牌名称",
    dataIndex: "brandName",
    align: "center",
    width: 120,
    search: true,
    searchField: "brandName",
    placeholder: "请输入品牌名称",
  },
  {
    title: "产品线类型",
    dataIndex: "productLineType",
    align: "center",
    width: 120,
    search: false,
    searchField: "productLineType",
    placeholder: "请输入产品线类型",
  },
  {
    title: "品牌",
    dataIndex: "brand",
    align: "center",
    width: 120,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "君网BG", value: 'julingBG' },
        { label: "京东", value: 'jd' },
        { label: "商城", value: 'mall' }
      ]
    },
    searchField: "brand",
    placeholder: "请选择品牌",
  },
  {
    title: "是否自主品牌",
    dataIndex: "isOwnBrand",
    align: "center",
    width: 120,
    slotName: "isOwnBrand",
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "是", value: true },
        { label: "否", value: false },
      ]
    },
    searchField: "isOwnBrand",
    placeholder: "请选择是否自主品牌",
  },
  {
    title: "资质到期时间",
    dataIndex: "qualificationExpireDate",
    align: "center",
    width: 150,
    search: true,
    formType: "range",
    searchField: "qualificationExpireDate",
    placeholder: "请选择资质到期时间",
  },
  {
    title: "状态",
    dataIndex: "status",
    align: "center",
    width: 120,
    slotName: "status",
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "已生效", value: "valid" },
        { label: "无效", value: "invalid" },
        { label: "资质过期", value: "expired" },
      ]
    },
    searchField: "status",
    placeholder: "请选择状态",
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    width: 120,
    fixed: "right",
    slotName: "operation",
  },
]);

// 搜索表单配置
const search = reactive({
  labelWidth: 80,
  formProps: {
    layout: "inline",
  },
  schemas: [
    {
      field: "platform",
      label: "平台",
      component: "Select",
      componentProps: {
        placeholder: "请选择平台",
        options: [
          { label: "君网BG", value: "julingBG" },
          { label: "京东", value: "jd" },
          { label: "商城", value: "mall" },
        ],
      },
    },
    {
      field: "relatedBrand",
      label: "品牌名称",
      component: "Input",
      componentProps: {
        placeholder: "请输入品牌名称",
      },
    },
    {
      field: "category",
      label: "品牌分类",
      component: "Cascader",
      componentProps: {
        placeholder: "请选择品牌分类",
        options: [
          {
            value: "electronics",
            label: "电子产品",
            children: [
              {
                value: "phone",
                label: "手机",
                children: [
                  { value: "smartphone", label: "智能手机" },
                  { value: "featurephone", label: "功能手机" },
                ],
              },
              {
                value: "computer",
                label: "电脑",
                children: [
                  { value: "laptop", label: "笔记本电脑" },
                  { value: "desktop", label: "台式电脑" },
                ],
              },
            ],
          },
          {
            value: "clothing",
            label: "服装",
            children: [
              {
                value: "men",
                label: "男装",
                children: [
                  { value: "shirts", label: "衬衫" },
                  { value: "pants", label: "裤子" },
                ],
              },
              {
                value: "women",
                label: "女装",
                children: [
                  { value: "dresses", label: "连衣裙" },
                  { value: "skirts", label: "裙子" },
                ],
              },
            ],
          },
        ],
      },
    },
    {
      field: "status",
      label: "资质状态",
      component: "Select",
      componentProps: {
        placeholder: "请选择资质状态",
        options: [
          { label: "无效", value: "invalid" },
          { label: "已生效", value: "valid" },
          { label: "资质过期", value: "expired" },
        ],
      },
    },
    {
      field: "isOwnBrand",
      label: "是否自主品牌",
      component: "Select",
      componentProps: {
        placeholder: "请选择是否自主品牌",
        options: [
          { label: "是", value: true },
          { label: "否", value: false },
        ],
      },
    },
    {
      field: "qualificationExpireDate",
      label: "资质到期时间",
      component: "DatePicker",
      componentProps: {
        placeholder: "请选择资质到期时间",
        style: {
          width: "100%",
        },
      },
    },
  ],
});

// 搜索参数
const searchParams = ref({
  platform: "",
  relatedBrand: "",
  category: [],
  status: "",
  isOwnBrand: "",
  qualificationExpireDate: "",
  categoryLevel1: "",
  categoryLevel2: "",
  categoryLevel3: "",
  productLineType: "",
});

// 新增产品线
const handleAdd = () => {
  Message.info("新增产品线功能开发中...");
};

// 表格配置
const crudOptions = reactive({
  title: "产品线管理",
  border: true,
  stripe: true,
  rowKey: "id",
  pageLayout: 'fixed',
  exportable: false,
  searchColNumber: 3,
  add: {
    show: false,
    text: "新增产品线",
    action: handleAdd
  },
  pagination: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
    background: true,
    total: 0
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    return params;
  },
  // 分页事件
  onPageChange: (page) => {
    crudOptions.pagination.currentPage = page;
    filterProductLineData();
  },
  // 每页条数变化事件
  onPageSizeChange: (size) => {
    crudOptions.pagination.pageSize = size;
    filterProductLineData();
  },
  // 搜索事件
  onSearch: () => {
    crudOptions.pagination.currentPage = 1;
    filterProductLineData();
  }
});

// 这些事件处理函数已经移动到 crudOptions 中定义

// 查看产品线详情
const handleView = (record) => {
  currentProductLine.value = record;
  drawerVisible.value = true;
};

// 重置产品线
const handleReset = (record) => {
  Message.info("重置产品线功能开发中...");
};

// 关闭抽屉
const handleDrawerClose = () => {
  drawerVisible.value = false;
  currentProductLine.value = null;
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    valid: "已生效",
    invalid: "无效",
    expired: "资质过期",
  };
  return statusMap[status] || "未知状态";
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    valid: "green",
    invalid: "gray",
    expired: "red",
  };
  return colorMap[status] || "gray";
};

// 获取模拟数据
const getFilteredData = () => {
  // 生成模拟数据
  const allData = getMockProductLineData();
  console.log('原始数据数量:', allData.length);
  
  return allData.filter(item => {
    // 平台筛选
    if (searchParams.value.platform && item.platform !== searchParams.value.platform) {
      return false;
    }
    
    // 品牌名称筛选
    if (searchParams.value.relatedBrand && !item.relatedBrand.includes(searchParams.value.relatedBrand)) {
      return false;
    }
    
    // 品牌分类筛选
    if (searchParams.value.category && searchParams.value.category.length > 0) {
      const [level1, level2, level3] = searchParams.value.category;
      if (level1 && item.categoryLevel1 !== level1) return false;
      if (level2 && item.categoryLevel2 !== level2) return false;
      if (level3 && item.categoryLevel3 !== level3) return false;
    }
    
    // 一级分类筛选
    if (searchParams.value.categoryLevel1 && item.categoryLevel1 !== searchParams.value.categoryLevel1) {
      return false;
    }
    
    // 二级分类筛选
    if (searchParams.value.categoryLevel2 && item.categoryLevel2 !== searchParams.value.categoryLevel2) {
      return false;
    }
    
    // 三级分类筛选
    if (searchParams.value.categoryLevel3 && item.categoryLevel3 !== searchParams.value.categoryLevel3) {
      return false;
    }
    
    // 产品线类型筛选
    if (searchParams.value.productLineType && item.productLineType !== searchParams.value.productLineType) {
      return false;
    }
    
    // 资质状态筛选
    if (searchParams.value.status && item.status !== searchParams.value.status) {
      return false;
    }
    
    // 是否自主品牌筛选
    if (searchParams.value.isOwnBrand !== "" && searchParams.value.isOwnBrand !== undefined) {
      const isOwnBrandValue = searchParams.value.isOwnBrand === true || searchParams.value.isOwnBrand === 'true';
      if (item.isOwnBrand !== isOwnBrandValue) {
        return false;
      }
    }
    
    // 资质到期时间筛选
    if (searchParams.value.qualificationExpireDate) {
      const selectedDate = new Date(searchParams.value.qualificationExpireDate).setHours(0, 0, 0, 0);
      const itemDate = new Date(item.qualificationExpireDate).setHours(0, 0, 0, 0);
      if (selectedDate !== itemDate) {
        return false;
      }
    }
    
    return true;
  });
};

// 筛选产品线数据
const filterProductLineData = () => {
  loading.value = true;
  
  // 模拟 API 请求延迟
  setTimeout(() => {
    const filteredData = getFilteredData();
    console.log('筛选后数据数量:', filteredData.length);
    
    // 分页处理
    const start = (crudOptions.pagination.currentPage - 1) * crudOptions.pagination.pageSize;
    const end = start + crudOptions.pagination.pageSize;
    
    tableData.value = filteredData.slice(start, end);
    console.log('分页后数据数量:', tableData.value.length);
    total.value = filteredData.length;
    crudOptions.pagination.total = filteredData.length;
    loading.value = false;
  }, 300);
};

// 获取模拟产品线数据
const getMockProductLineData = () => {
  const categories = {
    level1: ["电子产品", "服装", "食品", "家居", "美妆"],
    level2: {
      "电子产品": ["手机", "电脑", "相机", "音响", "配件"],
      "服装": ["男装", "女装", "童装", "运动服", "内衣"],
      "食品": ["零食", "饮料", "生鲜", "调味品", "保健品"],
      "家居": ["家具", "灯具", "厨具", "卫浴", "装饰品"],
      "美妆": ["护肤", "彩妆", "香水", "美发", "美甲"],
    },
    level3: {
      "手机": ["智能手机", "功能手机", "老人手机", "游戏手机", "折叠手机"],
      "电脑": ["笔记本电脑", "台式电脑", "平板电脑", "一体机", "服务器"],
      "男装": ["衬衫", "裤子", "外套", "T恤", "西装"],
      "女装": ["连衣裙", "裙子", "上衣", "裤子", "外套"],
      "家具": ["沙发", "床", "桌子", "椅子", "柜子"],
    }
  };
  
  const brands = ["苹果", "三星", "华为", "小米", "OPPO", "vivo", "耐克", "阿迪达斯", "优衣库", "H&M", "无印良品"];
  const productLineTypes = ["标准产品线", "定制产品线", "高端产品线", "经济产品线", "特殊产品线"];
  const platforms = ["julingBG", "jd", "mall"];
  const statuses = ["valid", "invalid", "expired"];
  
  // 生成模拟数据
  const generateProductLineItem = (index) => {
    const level1 = categories.level1[index % categories.level1.length];
    const level2 = categories.level2[level1][index % categories.level2[level1].length];
    const level3 = categories.level3[level2] ? categories.level3[level2][index % categories.level3[level2].length] : `${level2}子类${index % 5 + 1}`;
    
    // 随机生成资质到期时间（今年到明年之间）
    const now = new Date();
    const expireDate = new Date(now.getFullYear() + (index % 2), (now.getMonth() + index) % 12, (now.getDate() + index) % 28 + 1);
    
    // 根据资质到期时间判断状态
    let status = statuses[index % statuses.length];
    if (status === "expired" || (expireDate < now && index % 3 === 0)) {
      status = "expired";
    }
    
    return {
      id: `pl-${index + 1}`,
      categoryLevel1: level1,
      categoryLevel2: level2,
      categoryLevel3: level3,
      relatedBrand: brands[index % brands.length],
      productLineType: productLineTypes[index % productLineTypes.length],
      isOwnBrand: index % 3 === 0, // 三分之一的概率是自主品牌
      qualificationExpireDate: expireDate.toISOString().split('T')[0],
      status: status,
      platform: platforms[index % platforms.length],
      createTime: new Date(now.getFullYear(), now.getMonth() - 1, now.getDate() - (index % 30)).toISOString().split('T')[0],
      updateTime: new Date(now.getFullYear(), now.getMonth(), now.getDate() - (index % 10)).toISOString().split('T')[0],
    };
  };
  
  return Array.from({ length: 100 }, (_, index) => generateProductLineItem(index));
};

// 页面初始化时的数据加载
const initData = () => {
  console.log('初始化数据');
  loading.value = true;
  
  // 生成模拟数据
  const allData = getMockProductLineData();
  
  // 分页处理
  const start = 0;
  const end = crudOptions.pagination.pageSize;
  
  tableData.value = allData.slice(start, end);
  total.value = allData.length;
  crudOptions.pagination.total = allData.length;
  loading.value = false;
};

// 页面加载时获取数据
onMounted(() => {
  console.log('页面加载完成，开始获取数据');
  // 初始化表格数据
  initData();
});
</script>

<style scoped lang="less">
.product-line-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.search-area {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-tabs-container {
  margin-bottom: 20px;
}

.status-tab-title {
  margin-right: 8px;
}

.status-tab-count {
  display: inline-block;
  padding: 0 8px;
  height: 20px;
  line-height: 20px;
  background-color: #f2f3f5;
  border-radius: 10px;
  font-size: 12px;
  color: #4e5969;
}
:deep(.arco-form-item-label){
 white-space: nowrap; 
}
</style>
