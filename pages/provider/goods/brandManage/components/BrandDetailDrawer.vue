<template>
  <a-drawer
    :visible="visible"
    :width="600"
    :title="'品牌详情 - ' + (brandData?.name || '')"
    @cancel="close"
    unmountOnClose
  >
    <template #footer>
      <div class="flex justify-end">
        <a-button @click="close">关闭</a-button>
      </div>
    </template>

    <div class="brand-detail-container">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="section-title">基本信息</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">品牌名称</div>
            <div class="info-value">{{ brandData?.name || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">品牌代码</div>
            <div class="info-value">{{ brandData?.code || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">商标状态</div>
            <div class="info-value">
              <a-tag :color="getTrademarkStatusColor(brandData?.trademarkStatus)">
                {{ getTrademarkStatusText(brandData?.trademarkStatus) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">经营状态</div>
            <div class="info-value">
              <a-tag :color="getBusinessStatusColor(brandData?.businessStatus)">
                {{ getBusinessStatusText(brandData?.businessStatus) }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 商标信息 -->
      <div class="detail-section">
        <div class="section-title">商标信息</div>
        <div class="logo-section">
          <div class="logo-title">商标Logo</div>
          <div class="logo-container">
            <a-image 
              :src="brandData?.logo" 
              width="120" 
              height="120" 
              fit="contain"
              :preview="false"
            />
          </div>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">商标号</div>
            <div class="info-value">{{ brandData?.trademarkNo || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">品牌所属地</div>
            <div class="info-value">{{ brandData?.location || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">商标注册类别</div>
            <div class="info-value">{{ brandData?.registrationCategory || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">商标小类</div>
            <div class="info-value">{{ brandData?.registrationSubCategory || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">注册日期</div>
            <div class="info-value">{{ brandData?.registrationDate || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">有效期</div>
            <div class="info-value">{{ brandData?.validityPeriod || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">商标持有人</div>
            <div class="info-value">{{ brandData?.owner || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 审核信息 -->
      <div class="detail-section">
        <div class="section-title">审核信息</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">审核状态</div>
            <div class="info-value">
              <a-tag :color="getAuditStatusColor(brandData?.trademarkStatus)">
                {{ getAuditStatusText(brandData?.trademarkStatus) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">提交时间</div>
            <div class="info-value">{{ getSubmitTime() }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">审核时间</div>
            <div class="info-value">{{ getAuditTime() }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">审核人</div>
            <div class="info-value">{{ getAuditor() }}</div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="detail-section">
        <div class="section-title">操作日志</div>
        <a-timeline>
          <a-timeline-item v-for="(log, index) in getOperationLogs()" :key="index">
            <div class="log-item">
              <div class="log-time">{{ log.time }}</div>
              <div class="log-content">{{ log.content }}</div>
              <div class="log-operator">操作人：{{ log.operator }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  brandData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'close']);

// 关闭抽屉
const close = () => {
  emit('update:visible', false);
  emit('close');
};

// 获取商标状态文本
const getTrademarkStatusText = (status) => {
  const statusMap = {
    valid: "有效",
    expired: "已过期",
    pending: "审核中",
    rejected: "已驳回"
  };
  return statusMap[status] || "未知状态";
};

// 获取商标状态颜色
const getTrademarkStatusColor = (status) => {
  const colorMap = {
    valid: "green",
    expired: "red",
    pending: "orange",
    rejected: "gray"
  };
  return colorMap[status] || "blue";
};

// 获取经营状态文本
const getBusinessStatusText = (status) => {
  const statusMap = {
    active: "经营中",
    inactive: "已停用",
    suspended: "已暂停"
  };
  return statusMap[status] || "未知状态";
};

// 获取经营状态颜色
const getBusinessStatusColor = (status) => {
  const colorMap = {
    active: "green",
    inactive: "red",
    suspended: "orange"
  };
  return colorMap[status] || "blue";
};

// 获取审核状态文本
const getAuditStatusText = (status) => {
  const statusMap = {
    valid: "已通过",
    expired: "已过期",
    pending: "审核中",
    rejected: "已驳回"
  };
  return statusMap[status] || "未知状态";
};

// 获取审核状态颜色
const getAuditStatusColor = (status) => {
  const colorMap = {
    valid: "green",
    expired: "orange",
    pending: "blue",
    rejected: "red"
  };
  return colorMap[status] || "gray";
};

// 获取提交时间（模拟数据）
const getSubmitTime = () => {
  // 根据品牌ID生成一个固定的日期
  const id = props.brandData?.id || '';
  const idNum = parseInt(id.replace(/\D/g, '') || '1');
  const day = (idNum % 28) + 1;
  const month = (idNum % 12) + 1;
  return `2023-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day} 10:30:00`;
};

// 获取审核时间（模拟数据）
const getAuditTime = () => {
  if (props.brandData?.trademarkStatus === 'pending') {
    return '-';
  }
  
  // 根据品牌ID生成一个固定的日期，比提交时间晚1-3天
  const id = props.brandData?.id || '';
  const idNum = parseInt(id.replace(/\D/g, '') || '1');
  const day = ((idNum % 28) + 1 + (idNum % 3) + 1);
  const month = (idNum % 12) + 1;
  return `2023-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day} 15:45:00`;
};

// 获取审核人（模拟数据）
const getAuditor = () => {
  if (props.brandData?.trademarkStatus === 'pending') {
    return '-';
  }
  
  const auditors = ['张审核', '李审核', '王审核', '赵审核', '钱审核'];
  const id = props.brandData?.id || '';
  const idNum = parseInt(id.replace(/\D/g, '') || '1');
  return auditors[idNum % auditors.length];
};

// 获取操作日志（模拟数据）
const getOperationLogs = () => {
  const logs = [];
  const id = props.brandData?.id || '';
  const idNum = parseInt(id.replace(/\D/g, '') || '1');
  const day = (idNum % 28) + 1;
  const month = (idNum % 12) + 1;
  
  // 提交申请日志
  logs.push({
    time: `2023-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day} 10:30:00`,
    content: '提交品牌审核申请',
    operator: '系统'
  });
  
  // 根据状态添加不同的日志
  if (props.brandData?.trademarkStatus === 'valid') {
    logs.push({
      time: `2023-${month < 10 ? '0' + month : month}-${(day + 2) < 10 ? '0' + (day + 2) : (day + 2)} 15:45:00`,
      content: '品牌审核通过',
      operator: getAuditor()
    });
  } else if (props.brandData?.trademarkStatus === 'rejected') {
    logs.push({
      time: `2023-${month < 10 ? '0' + month : month}-${(day + 1) < 10 ? '0' + (day + 1) : (day + 1)} 15:45:00`,
      content: '品牌审核不通过，原因：商标信息不完整',
      operator: getAuditor()
    });
  } else if (props.brandData?.trademarkStatus === 'expired') {
    logs.push({
      time: `2023-${month < 10 ? '0' + month : month}-${(day + 2) < 10 ? '0' + (day + 2) : (day + 2)} 15:45:00`,
      content: '品牌审核通过',
      operator: getAuditor()
    });
    logs.push({
      time: `2024-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day} 00:00:00`,
      content: '品牌有效期已过期',
      operator: '系统'
    });
  }
  
  return logs;
};
</script>

<style scoped lang="less">
.brand-detail-container {
  padding: 0 16px;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f2f3f5;
  
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #165dff;
    border-radius: 2px;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #1d2129;
}

.logo-section {
  margin-bottom: 16px;
}

.logo-title {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 8px;
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120px;
  height: 120px;
  background-color: #f7f8fa;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.log-item {
  margin-bottom: 8px;
}

.log-time {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.log-content {
  font-size: 14px;
  color: #1d2129;
  margin-bottom: 4px;
}

.log-operator {
  font-size: 12px;
  color: #86909c;
}
</style>
