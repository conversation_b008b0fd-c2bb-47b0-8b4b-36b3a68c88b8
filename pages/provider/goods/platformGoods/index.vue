<template>
  <div class="ma-content-block p-4">
    <!-- 平台选择器 -->


    <ma-crud
      :options="crudOptions"
      :columns="columns"
      :expandable="expandableConfig"
      ref="crudRef"
    >
      <!-- 商品信息列 -->
      <template #goodsInfo="{ record }">
        <div class="flex items-center py-2">
          <!-- 商品图片 -->
          <a-image
            :src="record.mainImage || '/placeholder.png'"
            width="60"
            height="60"
            fit="cover"
            class="rounded mr-3 flex-shrink-0"
          />
          <div class="flex flex-col min-w-0">
            <!-- 商品名称 -->
            <div class="font-medium text-gray-800 leading-tight truncate w-full">
              {{ record.name }}
            </div>
            <!-- SKU编码 -->
            <div class="text-gray-500 text-sm mt-1">
              {{ record.skuCode }}
            </div>
          </div>
        </div>
      </template>

      <!-- 商品服务 -->
      <template #service="{ record }">
        <template v-if="record.service && Array.isArray(record.service)">
          <a-tag
            v-for="srv in record.service"
            :key="srv"
            color="arcoblue"
            class="mr-1 mb-1"
          >{{ srv }}</a-tag>
        </template>
        <span v-else>{{ record.service }}</span>
      </template>

      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>

      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space>
          <a-link @click="handleView(record)">查看详情</a-link>
          <a-link @click="handleAddReportCar(record)">添加报备车</a-link>
          <a-link @click="handleEdit(record)">编辑</a-link>
          <a-dropdown>
            <a-button size="mini">
              更多
              <icon-down />
            </a-button>
            <template #content>
              <a-doption v-if="record.status === 'on_sale'" @click="handleChangeStatus(record, 'off_sale')">
                <icon-down class="mr-1" />下架
              </a-doption>
              <a-doption v-if="record.status === 'off_sale'" @click="handleChangeStatus(record, 'on_sale')">
                <icon-up class="mr-1" />上架
              </a-doption>
              <a-doption @click="handleImport(record)">
                <icon-import class="mr-1" />导入
              </a-doption>
            </template>
          </a-dropdown>
        </a-space>
      </template>

      <!-- 分类选择器 -->
      <template #search-categoryId="{ searchForm }">
        <a-cascader
          v-model="searchForm.categoryId"
          :options="categoryOptions"
          placeholder="请选择分类"
          allow-clear
        />
      </template>

      <!-- 平台选择器 -->
      <template #search-platform="{ searchForm }">
        <a-select
          v-model="searchForm.platform"
          placeholder="请选择平台"
          allow-clear
        >
          <a-option
            v-for="platform in platforms"
            :key="platform.id"
            :value="platform.id"
          >
            {{ platform.name }}
          </a-option>
        </a-select>
      </template>

      <!-- SKU 展开行 -->
      <template #expand-row="{ record }">
        <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded">
          <div class="max-w-6xl mx-auto">
            <a-table
              :data="record.skus"
              :pagination="false"
              size="small"
              :scroll="{ x: '100%' }"
            >
              <template #columns>
                <a-table-column
                  title="SKU图片"
                  data-index="imageUrl"
                  :width="80"
                  align="center"
                >
                  <template #cell="{ record: skuRecord }">
                    <a-image
                      :src="skuRecord.imageUrl"
                      width="40"
                      height="40"
                      fit="cover"
                    />
                  </template>
                </a-table-column>
                <a-table-column
                  title="SKU名称"
                  data-index="skuName"
                  :width="150"
                ></a-table-column>
                <a-table-column
                  title="销售价"
                  data-index="salesPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="市场价"
                  data-index="marketPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="成本价"
                  data-index="costPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="销量"
                  data-index="sales"
                  :width="80"
                  align="center"
                ></a-table-column>
                <a-table-column
                  title="库存"
                  data-index="stock"
                  :width="80"
                  align="center"
                ></a-table-column>
                <a-table-column
                  title="单位"
                  data-index="unit"
                  :width="80"
                ></a-table-column>
              </template>
            </a-table>
          </div>
        </div>
      </template>
    </ma-crud>

    <!-- 商品详情抽屉 -->
    <a-drawer
      :visible="detailDrawerVisible"
      @cancel="detailDrawerVisible = false"
      @ok="detailDrawerVisible = false"
      :width="700"
      :title="`商品详情 - ${currentDetail?.name || ''}`"
    >
      <template v-if="currentDetail">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div class="flex justify-center items-center">
            <a-image
              :src="currentDetail.mainImage || '/placeholder.png'"
              width="200"
              height="200"
              fit="cover"
              class="rounded"
            />
          </div>
          <div class="flex flex-col">
            <h3 class="text-lg font-bold mb-2">{{ currentDetail.name }}</h3>
            <div class="grid grid-cols-2 gap-2">
              <div class="text-gray-500">SKU编码:</div>
              <div>{{ currentDetail.skuCode }}</div>
              <div class="text-gray-500">品牌:</div>
              <div>{{ currentDetail.brandName }}</div>
              <div class="text-gray-500">状态:</div>
              <div>
                <a-tag :color="getStatusColor(currentDetail.status)">
                  {{ getStatusText(currentDetail.status) }}
                </a-tag>
              </div>
              <div class="text-gray-500">销量:</div>
              <div>{{ currentDetail.sales }}</div>
              <div class="text-gray-500">创建时间:</div>
              <div>{{ currentDetail.createdAt }}</div>
            </div>
          </div>
        </div>
        <a-divider />
        <div class="mb-4">
          <h4 class="text-base font-bold mb-2">商品服务</h4>
          <div>
            <template v-if="currentDetail.service && Array.isArray(currentDetail.service)">
              <a-tag
                v-for="srv in currentDetail.service"
                :key="srv"
                color="arcoblue"
                class="mr-1 mb-1"
              >{{ srv }}</a-tag>
            </template>
            <span v-else>{{ currentDetail.service || '无' }}</span>
          </div>
        </div>
        <a-divider />
        <div>
          <h4 class="text-base font-bold mb-2">商品描述</h4>
          <p class="text-gray-600">{{ currentDetail.description || '暂无商品描述' }}</p>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "#app";

// 在 setup 中定义 router 变量
const router = useRouter();

// 定义页面路由元信息
definePageMeta({
  name: "provider-platformGoods",
  path: "/provider/goods/platformGoods",
});

const crudRef = ref();
const currentPlatform = ref("0"); // 默认选择全部平台
const detailDrawerVisible = ref(false);
const currentDetail = ref(null);

// 展开行配置
const expandableConfig = reactive({
  expandRowByClick: true,
  showExpandRow: true,
});

// 平台列表
const platforms = reactive([
  { id: "0", name: "全部" },
  { id: "1", name: "自采" },
  { id: "2", name: "京东" },
  { id: "3", name: "商城" },
]);

// 状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "在售", value: "on_sale" },
  { label: "已下架", value: "off_sale" },
  { label: "售罄", value: "sold_out" },
];

// 模拟分类数据
const categoryOptions = reactive([
  {
    value: "1",
    label: "电子产品",
    children: [
      {
        value: "1-1",
        label: "手机",
      },
      {
        value: "1-2",
        label: "电脑",
      },
      {
        value: "1-3",
        label: "相机",
      },
    ],
  },
  {
    value: "2",
    label: "服装",
    children: [
      {
        value: "2-1",
        label: "男装",
      },
      {
        value: "2-2",
        label: "女装",
      },
    ],
  },
  {
    value: "3",
    label: "家居",
    children: [
      {
        value: "3-1",
        label: "家具",
      },
      {
        value: "3-2",
        label: "厨具",
      },
    ],
  },
]);

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case "on_sale":
      return "在售";
    case "off_sale":
      return "已下架";
    case "sold_out":
      return "售罄";
    default:
      return "未知";
  }
};

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case "on_sale":
      return "green";
    case "off_sale":
      return "gray";
    case "sold_out":
      return "red";
    default:
      return "blue";
  }
};

// 处理平台切换
const handlePlatformChange = (platformId) => {
  // 重新加载数据
  crudRef.value.refresh();
  Message.success(`已切换到${platforms.find(p => p.id === platformId).name}平台商品`);
};

// 查看商品详情
const handleView = (record) => {
  currentDetail.value = {
    ...record,
    description: `这是一个来自${platforms.find(p => p.id === currentPlatform.value).name}平台的商品，商品编码为${record.skuCode}。该商品由${record.brandName}品牌生产，目前状态为${getStatusText(record.status)}，累计销量${record.sales}件。`
  };
  detailDrawerVisible.value = true;
};

// 编辑商品
const handleEdit = (record) => {
  import("@/utils/common").then((module) => {
    module.navigateWithTag(router, `/provider/goods/addGoods/${record.id}`);
  });
};

// 更改商品状态（上架/下架）
const handleChangeStatus = (record, newStatus) => {
  const statusText = newStatus === 'on_sale' ? '上架' : '下架';
  Message.success(`已将商品 ${record.name} ${statusText}`);
  // 实际应用中这里会调用API更新状态，然后刷新列表
  setTimeout(() => {
    crudRef.value.refresh();
  }, 500);
};

// 导入商品
const handleImport = (record) => {
  Message.success(`已将商品 ${record.name} 导入到本地商品库`);
};

// 添加报备车
const handleAddReportCar = (record) => {
  Message.success(`已为商品 ${record.name} 添加报备车`);
};

// 生成SKU数据项
const generateSkuItem = (prefix, goodsId, index) => {
  return {
    id: `${goodsId}-${index + 1}`,
    skuName: `${prefix}规格${index + 1}`,
    imageUrl: `https://img12.360buyimg.com/imagetools/jfs/t1/158054/3/45410/37057/662b1030Fddb8470d/a20b6af2770d2632.png`,
    salesPrice: (Math.random() * 1000 + 500).toFixed(2),
    marketPrice: (Math.random() * 1500 + 800).toFixed(2),
    costPrice: (Math.random() * 800 + 300).toFixed(2),
    sales: Math.floor(Math.random() * 500),
    stock: Math.floor(Math.random() * 1000),
    unit: ['件', '个', '台', '套'][Math.floor(Math.random() * 4)]
  };
};

// 生成单个商品数据项
const generateGoodsItem = (prefix, index) => {
  const id = `${prefix}${10000 + index}`;
  
  // 生成1-3个SKU
  const skuCount = Math.floor(Math.random() * 3) + 1;
  const skus = Array.from({ length: skuCount }, (_, i) => generateSkuItem(prefix, id, i));
  
  return {
    id,
    name: `${prefix}商品-${index + 1}`,
    skuCode: `${prefix}SKU${100000 + index}`,
    mainImage: `https://img12.360buyimg.com/imagetools/jfs/t1/158054/3/45410/37057/662b1030Fddb8470d/a20b6af2770d2632.png`,
    brandId: Math.floor(Math.random() * 5) + 1,
    brandName: `品牌${Math.floor(Math.random() * 5) + 1}`,
    categoryId: `${Math.floor(Math.random() * 3) + 1}-${Math.floor(Math.random() * 2) + 1}`,
    categoryName: "电子产品/手机",
    status: ["on_sale", "off_sale", "sold_out"][Math.floor(Math.random() * 3)],
    sales: Math.floor(Math.random() * 1000),
    service: ["包邮", "7天无理由退换", "48小时发货"].slice(0, Math.floor(Math.random() * 3) + 1),
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    skus: skus
  };
};

// 模拟获取商品数据
const mockGetGoodsList = (params) => {
  console.log("查询参数:", params);
  console.log("当前平台:", currentPlatform.value);
  
  // 平台前缀映射
  const platformPrefixMap = {
    "0": ["JD", "TB", "PDD", "DY"], // 全部平台
    "1": ["JD"],  // 京东
    "2": ["TB"],  // 淘宝
    "3": ["PDD"], // 拼多多
    "4": ["DY"]   // 抖音
  };
  
  // 获取当前平台的前缀列表
  const platformPrefixes = platformPrefixMap[currentPlatform.value];
  
  // 生成模拟数据
  let items = [];
  
  // 如果是全部平台，则每个平台生成2-3条数据
  // 如果是特定平台，则生成10条该平台的数据
  if (currentPlatform.value === "0") {
    // 为每个平台生成2-3条数据
    platformPrefixes.forEach(prefix => {
      const count = Math.floor(Math.random() * 2) + 2; // 2-3条
      for (let i = 0; i < count; i++) {
        items.push(generateGoodsItem(prefix, i));
      }
    });
  } else {
    // 为特定平台生成10条数据
    const prefix = platformPrefixes[0];
    items = Array.from({ length: 10 }, (_, index) => generateGoodsItem(prefix, index));
  }
  
  return Promise.resolve({
    code: 0,
    data: {
      items,
      total: currentPlatform.value === "0" ? 100 : 30
    }
  });
};

// --- ma-crud 配置 ---
const crudOptions = reactive({
  api: mockGetGoodsList,
  searchColNumber: 3,
  showIndex: true,
  expandRowByClick: true,
  showExpandRow: true,
});

// 列定义
const columns = reactive([
  {
    title: "平台",
    dataIndex: "platform",
    width: 120,
    hide: true,
    search: true,
    formType: "select",
    slotName: "search-platform",
  },
  {
    title: "商品信息",
    dataIndex: "goodsInfo",
    width: 300,
    slotName: "goodsInfo",
    search: true,
    searchField: "name",
    placeholder: "请输入商品名称",
  },
  {
    title: "商品编码",
    dataIndex: "skuCode",
    width: 150,
    search: true,
    placeholder: "请输入商品编码",
  },
  {
    title: "商品品牌",
    dataIndex: "brandName",
    search: true,
    width: 120,
  },
  {
    title: "分类",
    dataIndex: "categoryId",
    width: 150,
    search: true,
    formType: "cascader",
    slotName: "search-categoryId",
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: statusOptions },
    slotName: "status",
  },
  {
    title: "销量",
    dataIndex: "sales",
    width: 100,
    align: "center",
  },
  {
    title: "商品服务",
    dataIndex: "service",
    width: 180,
    slotName: "service",
  },
  {
    title: "创建时间",
    dataIndex: "createdAt",
    width: 150,
    align: "center",
    search: true,
    formType: "daterange",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 220,
    align: "center",
    fixed: "right",
    slotName: "operation",
  },
]);

// --- 生命周期钩子 ---
onMounted(() => {
  // 初始加载数据
  crudRef.value?.refresh();
});
</script>

<style scoped>
/* 自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}

:deep(.arco-table-cell .arco-image-img) {
  object-fit: cover;
  /* 确保图片按比例填充 */
}

.bg-gray-100 {
  background-color: #f7f8fa;
}

.dark .bg-gray-700 {
  background-color: #2a2a2b;
  /* 适配暗黑模式的背景色 */
}
:deep(.arco-form-item-label){
 white-space: nowrap; 
}
</style>
