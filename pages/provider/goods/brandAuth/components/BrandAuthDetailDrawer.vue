<template>
  <a-drawer
    :visible="visible"
    :width="600"
    :title="'品牌授权详情 - ' + (brandAuthData?.brandName || '')"
    @cancel="close"
    unmountOnClose
  >
    <template #footer>
      <div class="flex justify-end">
        <!-- 复审通过的授权可以操作停用/启用 -->
        <a-button 
          v-if="brandAuthData?.auditStatus === 'finalPass'"
          type="primary" 
          status="warning" 
          class="mr-4"
          @click="handleToggleStatus"
        >
          {{ brandAuthData?.status === 'active' ? '停用' : '启用' }}
        </a-button>
        <a-button @click="close">关闭</a-button>
      </div>
    </template>

    <div class="brand-auth-detail-container">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="section-title">基本信息</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">授权编号</div>
            <div class="info-value">{{ brandAuthData?.authCode || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">授权品牌</div>
            <div class="info-value">{{ brandAuthData?.brandName || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">授权公司</div>
            <div class="info-value">{{ brandAuthData?.authCompany || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">被授权单位</div>
            <div class="info-value">{{ brandAuthData?.authorizedUnit || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">审核状态</div>
            <div class="info-value">
              <a-tag :color="getStatusColor(brandAuthData?.auditStatus)">
                {{ getStatusText(brandAuthData?.auditStatus) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item" v-if="brandAuthData?.auditStatus === 'finalPass'">
            <div class="info-label">使用状态</div>
            <div class="info-value">
              <a-tag :color="brandAuthData?.status === 'active' ? 'green' : 'gray'">
                {{ brandAuthData?.status === 'active' ? '启用中' : '已停用' }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 授权信息 -->
      <div class="detail-section">
        <div class="section-title">授权信息</div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">授权开始日期</div>
            <div class="info-value">{{ brandAuthData?.validStartDate || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">授权结束日期</div>
            <div class="info-value">{{ brandAuthData?.validEndDate || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">授权范围</div>
            <div class="info-value">{{ brandAuthData?.authScope || '全国范围' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">授权类型</div>
            <div class="info-value">{{ brandAuthData?.authType || '独家授权' }}</div>
          </div>
        </div>
      </div>

      <!-- 授权文件 -->
      <div class="detail-section">
        <div class="section-title">授权文件</div>
        <div class="auth-files">
          <div class="file-item" v-if="brandAuthData?.authFiles?.length">
            <div v-for="(file, index) in brandAuthData?.authFiles" :key="index" class="mb-2">
              <a-link>{{ file.name }}</a-link>
            </div>
          </div>
          <div v-else class="empty-files">
            暂无授权文件
          </div>
        </div>
      </div>

      <!-- 审核信息 -->
      <div class="detail-section" v-if="brandAuthData?.auditStatus !== 'pending'">
        <div class="section-title">审核信息</div>
        <div class="info-grid">
          <div class="info-item" v-if="brandAuthData?.firstAuditTime">
            <div class="info-label">初审时间</div>
            <div class="info-value">{{ brandAuthData?.firstAuditTime || '-' }}</div>
          </div>
          <div class="info-item" v-if="brandAuthData?.firstAuditor">
            <div class="info-label">初审人员</div>
            <div class="info-value">{{ brandAuthData?.firstAuditor || '-' }}</div>
          </div>
          <div class="info-item" v-if="brandAuthData?.finalAuditTime">
            <div class="info-label">复审时间</div>
            <div class="info-value">{{ brandAuthData?.finalAuditTime || '-' }}</div>
          </div>
          <div class="info-item" v-if="brandAuthData?.finalAuditor">
            <div class="info-label">复审人员</div>
            <div class="info-value">{{ brandAuthData?.finalAuditor || '-' }}</div>
          </div>
          <div class="info-item" v-if="brandAuthData?.auditStatus === 'firstReject' || brandAuthData?.auditStatus === 'finalReject'">
            <div class="info-label">驳回原因</div>
            <div class="info-value">{{ brandAuthData?.rejectReason || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="detail-section">
        <div class="section-title">操作日志</div>
        <a-timeline>
          <a-timeline-item v-for="(log, index) in getOperationLogs()" :key="index">
            <div class="log-item">
              <div class="log-time">{{ log.time }}</div>
              <div class="log-content">{{ log.content }}</div>
              <div class="log-operator">操作人：{{ log.operator }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  brandAuthData: {
    type: Object,
    default() {
      return null;
    }
  }
});

const emit = defineEmits(['update:visible', 'close', 'status-change']);

// 关闭抽屉
const close = () => {
  emit('update:visible', false);
  emit('close');
};

// 切换授权状态（启用/停用）
const handleToggleStatus = () => {
  if (!props.brandAuthData) return;
  
  const newStatus = props.brandAuthData.status === 'active' ? 'inactive' : 'active';
  const actionText = newStatus === 'active' ? '启用' : '停用';
  
  // 实际项目中应该调用API进行状态更新
  // 这里仅做模拟
  // 消息提示移动到主页面中处理
  
  // 更新本地数据状态（实际项目中应该通过API获取最新数据）
  if (props.brandAuthData) {
    props.brandAuthData.status = newStatus;
    
    // 触发状态变更事件，通知父组件更新数据
    emit('status-change', {
      id: props.brandAuthData.id,
      status: newStatus,
      actionText: actionText
    });
    
    // 添加新的操作日志
    const now = new Date();
    const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    // 如果有操作日志属性，则添加新的日志
    if (!props.brandAuthData._logs) {
      props.brandAuthData._logs = [];
    }
    
    props.brandAuthData._logs.unshift({
      time: timeString,
      content: `${actionText}品牌授权`,
      operator: '当前用户'
    });
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: "待审核",
    firstPass: "初审通过",
    finalPass: "复审通过",
    firstReject: "初审不通过",
    finalReject: "复审不通过",
  };
  return statusMap[status] || "未知状态";
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    pending: "orange",
    firstPass: "blue",
    finalPass: "green",
    firstReject: "red",
    finalReject: "red",
  };
  return colorMap[status] || "gray";
};

// 获取操作日志（模拟数据）
const getOperationLogs = () => {
  const logs = [];
  if (!props.brandAuthData) return logs;
  
  const id = props.brandAuthData.id || '';
  const idNum = parseInt(id.replace(/\D/g, '') || '1');
  
  // 创建日期
  const createDate = props.brandAuthData.createTime || '2023-01-01';
  
  // 更新日期
  const updateDate = props.brandAuthData.updateTime || '2023-01-02';
  
  // 创建日志
  logs.push({
    time: `${createDate} 10:30:00`,
    content: '提交品牌授权申请',
    operator: getOperator(idNum, 'creator')
  });
  
  // 根据状态添加不同的日志
  if (props.brandAuthData.auditStatus === 'firstPass') {
    logs.push({
      time: `${updateDate} 14:30:00`,
      content: '初审通过',
      operator: getOperator(idNum, 'firstAuditor')
    });
  } else if (props.brandAuthData.auditStatus === 'finalPass') {
    logs.push({
      time: `${updateDate} 14:30:00`,
      content: '初审通过',
      operator: getOperator(idNum, 'firstAuditor')
    });
    logs.push({
      time: `${updateDate} 16:45:00`,
      content: '复审通过',
      operator: getOperator(idNum + 1, 'finalAuditor')
    });
    
    // 如果有状态变更，添加相应日志
    if (props.brandAuthData.status === 'inactive') {
      logs.push({
        time: `${updateDate} 17:30:00`,
        content: '停用授权',
        operator: getOperator(idNum + 2, 'manager')
      });
    }
  } else if (props.brandAuthData.auditStatus === 'firstReject') {
    logs.push({
      time: `${updateDate} 14:30:00`,
      content: `初审不通过，原因：${props.brandAuthData.rejectReason || '资料不完整'}`,
      operator: getOperator(idNum, 'firstAuditor')
    });
  } else if (props.brandAuthData.auditStatus === 'finalReject') {
    logs.push({
      time: `${updateDate} 14:30:00`,
      content: '初审通过',
      operator: getOperator(idNum, 'firstAuditor')
    });
    logs.push({
      time: `${updateDate} 16:45:00`,
      content: `复审不通过，原因：${props.brandAuthData.rejectReason || '授权文件有效期与申请不符'}`,
      operator: getOperator(idNum + 1, 'finalAuditor')
    });
  }
  
  return logs;
};

// 获取操作人（模拟数据）
const getOperator = (idNum, role) => {
  const operators = {
    creator: ['张提交', '李提交', '王提交', '赵提交', '钱提交'],
    firstAuditor: ['张初审', '李初审', '王初审', '赵初审', '钱初审'],
    finalAuditor: ['张复审', '李复审', '王复审', '赵复审', '钱复审'],
    manager: ['张经理', '李经理', '王经理', '赵经理', '钱经理']
  };
  
  return operators[role][idNum % operators[role].length];
};
</script>

<style scoped lang="less">
.brand-auth-detail-container {
  padding: 0 16px;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f2f3f5;
  
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #165dff;
    border-radius: 2px;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #1d2129;
}

.auth-files {
  padding: 16px;
  background-color: #f7f8fa;
  border-radius: 4px;
}

.empty-files {
  color: #86909c;
  text-align: center;
  padding: 16px 0;
}

.log-item {
  margin-bottom: 8px;
}

.log-time {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.log-content {
  font-size: 14px;
  color: #1d2129;
  margin-bottom: 4px;
}

.log-operator {
  font-size: 12px;
  color: #86909c;
}
</style>
