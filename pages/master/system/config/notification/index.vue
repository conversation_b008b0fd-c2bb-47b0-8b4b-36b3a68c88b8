<template>
  <div class="p-5 bg-white rounded">
    <!-- 短信服务配置 -->
    <div class="flex gap-4">
      <!-- 左侧：服务提供商 -->
      <div class="w-1/4">
        <a-card bordered :body-style="{ padding: '16px' }">
          <div style="font-weight: 600;font-size: 23px; color: black;margin-bottom: 5px;">服务提供商</div>
          <div class="mb-3 text-sm text-gray-500">选择短信服务提供商，只能选择一种</div>
          <div class="storage-options">
            <div
              v-for="item in storageList"
              :key="item.value"
              :class="['storage-option-card', { active: formModel.storage_mode === item.value }]"
              @click="formModel.storage_mode = item.value"
            >
              <div class="storage-content">
                <a-radio :model-value="formModel.storage_mode === item.value"></a-radio>
                <component :is="item.icon" class="storage-icon" />
                <div class="storage-text">{{ item.label }}</div>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 右侧：短信配置 -->
      <div class="w-3/4">
        <a-card :body-style="{ padding: '16px' }">
          <div
            style="font-weight: 600;font-size: 23px; color: black;margin-bottom: 5px;"
          >{{ smsConfigTitleMap[formModel.storage_mode] }}</div>
          <div
            class="mb-3 text-sm text-gray-500"
          >请填写{{ smsConfigTitleMap[formModel.storage_mode] }}的相关配置信息</div>

          <a-form :model="formModel" layout="vertical">
            <!-- 动态生成表单字段 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 动态生成表单项 -->
              <template
                v-if="configData && configData[formModel.storage_mode] && Object.keys(configData[formModel.storage_mode]).length > 0"
              >
                <a-form-item
                  v-for="(value, key) in filteredConfigFields"
                  :key="key"
                  :field="key"
                  :label="key"
                >
                  <a-input
                    v-if="typeof value !== 'boolean'"
                    v-model="formModel[formModel.storage_mode][key]"
                    :placeholder="'请输入' + key"
                  />
                  <a-switch v-else v-model="formModel[formModel.storage_mode][key]" />
                </a-form-item>
              </template>
            </div>

            <!-- 短信模板配置 -->
            <div class="font-medium mb-3" style="font-size: 16px; color: black;">短信模板配置</div>
            <a-card>
              <div class="mt">
                <!-- 短信模板配置固定标签 -->
                <template v-if="formModel.storage_mode === 'aliyun'">
                  <a-form-item field="verification_template_id" label="验证码模板 ID">
                    <a-input
                      v-model="formModel.aliyun.templates.verification_template_id"
                      placeholder="请输入验证码短信模板 ID"
                    />
                  </a-form-item>

                  <a-form-item field="notification_template_id" label="通知模板 ID">
                    <a-input
                      v-model="formModel.aliyun.templates.notification_template_id"
                      placeholder="请输入通知短信模板 ID"
                    />
                  </a-form-item>

                  <a-form-item field="marketing_template_id" label="营销模板 ID">
                    <a-input
                      v-model="formModel.aliyun.templates.marketing_template_id"
                      placeholder="请输入营销短信模板 ID"
                    />
                  </a-form-item>
                </template>

                <template v-if="formModel.storage_mode === 'tencent'">
                  <a-form-item field="verification_template_id" label="验证码模板 ID">
                    <a-input
                      v-model="formModel.tencent.templates.verification_template_id"
                      placeholder="请输入验证码短信模板 ID"
                    />
                  </a-form-item>

                  <a-form-item field="notification_template_id" label="通知模板 ID">
                    <a-input
                      v-model="formModel.tencent.templates.notification_template_id"
                      placeholder="请输入通知短信模板 ID"
                    />
                  </a-form-item>

                  <a-form-item field="marketing_template_id" label="营销模板 ID">
                    <a-input
                      v-model="formModel.tencent.templates.marketing_template_id"
                      placeholder="请输入营销短信模板 ID"
                    />
                  </a-form-item>
                </template>
              </div>
            </a-card>
            <div class="font-medium mt-3" style="font-size: 16px; color: black;">测试发送</div>
            <!-- 测试发送 -->
            <a-card style="margin-top: 10px;">
              <div class="flex gap-2">
                <a-form-item field="verification_template_id" label="测试手机号">
                  <a-input v-model="testPhone" placeholder="请输入测试手机号" />
                  <a-button type="primary" @click="sendTestSms" style="margin-left: 20px;">发送测试短信</a-button>
                </a-form-item>
              </div>
            </a-card>
          </a-form>
          <div class="flex justify-end mt-4">
            <a-button type="primary" @click="submitForm">
              <template #icon>
                <icon-save />
              </template>
              保存设置
            </a-button>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import systemApi from "@/api/master/system";
import commonApi from "@/api/common";
definePageMeta({
  name: "system-config-notification",
  path: "/master/system/config/notification"
});

const configApi = systemApi.configuration;
// 服务商列表，将从接口动态获取
const storageList = ref([]);

const testPhone = ref("");

// 短信配置区块标题
const smsConfigTitleMap = reactive({});

// 获取表单字段标签 - 直接返回字段名称
const getFieldLabel = fieldName => {
  return fieldName;
};

// 根据value获取服务商的名称
const getStorageLabelByValue = value => {
  const storage = storageList.value.find(item => item.value === value);
  return storage ? storage.label : "";
};

// 获取当前服务商信息
const currentProvider = computed(() => {
  return (
    storageList.value.find(item => item.value === formModel.storage_mode) ||
    (storageList.value.length > 0 ? storageList.value[0] : null)
  );
});

// 过滤掉templates字段的配置项
const filteredConfigFields = computed(() => {
  if (!configData.value || !configData.value[formModel.storage_mode]) {
    return {};
  }

  const result = {};
  Object.entries(configData.value[formModel.storage_mode]).forEach(
    ([key, value]) => {
      if (key !== "templates") {
        result[key] = value;
      }
    }
  );

  return result;
});

// 不再需要动态生成模板配置字段

// 表单数据
const formModel = reactive({
  storage_mode: "",
  // 阿里云配置项
  aliyun: {
    accessKeyId: "",
    accessKeySecret: "",
    signName: "",
    templates: {
      verification_template_id: "",
      notification_template_id: "",
      marketing_template_id: ""
    }
  },
  // 腾讯云配置项
  tencent: {
    secretId: "",
    secretKey: "",
    sdkAppId: "",
    region: "",
    endpoint: "",
    signName: "",
    templates: {
      verification_template_id: "",
      notification_template_id: "",
      marketing_template_id: ""
    }
  }
});

// 监听服务商变化，加载对应配置
watch(
  () => formModel.storage_mode,
  newMode => {
    // 加载当前存储模式的配置
    loadStorageModeConfig(newMode);
  }
);

// 发送测试短信
const sendTestSms = async () => {
  if (!testPhone.value) {
    Message.warning("请输入测试手机号");
    return;
  }

  // 获取当前选中的服务商的验证码模板ID
  let templateId = "";
  if (formModel.storage_mode === "aliyun") {
    templateId = formModel.aliyun.templates.verification_template_id;
  } else if (formModel.storage_mode === "tencent") {
    templateId = formModel.tencent.templates.verification_template_id;
  }

  if (!templateId) {
    Message.warning("请先设置验证码模板ID");
    return;
  }

  try {
    // 调用发送短信API
    const res = await commonApi.sendSms({
      phoneNumber: testPhone.value, // 手机号
      templateParams: { code: "123456" }, // 写死的验证码
      templateCode: templateId // 验证码模板 ID
    });

    if (res.code === 200) {
      Message.success(res.message || "测试短信发送成功");
    } else {
      Message.error(res.message || "测试短信发送失败");
    }
  } catch (error) {
    console.error("发送测试短信失败:", error);
    Message.error("测试短信发送失败");
  }
};

// 全局配置数据缓存
const configData = ref(null);

// 获取设置
const fetchSettings = async () => {
  const res = await configApi.objectTypes("SMS");
  if (res.code === 200 && res.data) {
    // 缓存数据供切换存储模式时使用
    configData.value = res.data;

    // 加载当前存储模式的配置
    loadStorageModeConfig(formModel.storage_mode);
  }
};

// 获取默认设置和服务商列表
const configure = async () => {
  const res = await configApi.configure("SMS");
  if (res.code === 200 && res.data) {
    // 动态构建服务商列表
    storageList.value = res.data.map(item => ({
      value: item.config_key,
      label: item.name,
      icon: "icon-cloud",
      // 存储原始数据，方便后续使用
      rawData: item
    }));

    // 动态构建配置标题映射
    res.data.forEach(item => {
      smsConfigTitleMap[item.config_key] = `${item.name}`;
    });

    // 如果有默认配置，设置为当前选中的服务商
    const defaultProvider = res.data.find(item => item.is_default === 1);
    if (defaultProvider) {
      formModel.storage_mode = defaultProvider.config_key;
    } else if (res.data.length > 0) {
      formModel.storage_mode = res.data[0].config_key;
    }
  }

  // 获取表单字段配置
  await fetchSettings();
};

// 根据存储模式加载对应配置
const loadStorageModeConfig = mode => {
  if (!configData.value) return;

  // 根据选择的存储模式加载对应配置
  switch (mode) {
    case "aliyun":
      // 阿里云配置
      if (configData.value.aliyun) {
        // 准备模板配置
        const templates = {
          verification_template_id: "",
          notification_template_id: "",
          marketing_template_id: ""
        };

        // 如果有模板配置，则加载
        if (configData.value.aliyun.templates) {
          templates.verification_template_id =
            configData.value.aliyun.templates.verification_template_id || "";
          templates.notification_template_id =
            configData.value.aliyun.templates.notification_template_id || "";
          templates.marketing_template_id =
            configData.value.aliyun.templates.marketing_template_id || "";
        }

        // 设置阿里云配置
        formModel.aliyun = {
          accessKeyId: configData.value.aliyun.accessKeyId || "",
          accessKeySecret: configData.value.aliyun.accessKeySecret || "",
          signName: configData.value.aliyun.signName || "",
          templates: templates
        };
      }
      break;

    case "tencent":
      // 腾讯云配置
      if (configData.value.tencent) {
        // 准备模板配置
        const templates = {
          verification_template_id: "",
          notification_template_id: "",
          marketing_template_id: ""
        };

        // 如果有模板配置，则加载
        if (configData.value.tencent.templates) {
          templates.verification_template_id =
            configData.value.tencent.templates.verification_template_id || "";
          templates.notification_template_id =
            configData.value.tencent.templates.notification_template_id || "";
          templates.marketing_template_id =
            configData.value.tencent.templates.marketing_template_id || "";
        }

        // 设置腾讯云配置
        formModel.tencent = {
          secretId: configData.value.tencent.secretId || "",
          secretKey: configData.value.tencent.secretKey || "",
          region: configData.value.tencent.region || "",
          endpoint: configData.value.tencent.endpoint || "",
          sdkAppId: configData.value.tencent.sdkAppId || "",
          signName: configData.value.tencent.signName || "",
          templates: templates
        };
      }
      break;
  }
};

// 提交表单
const submitForm = async () => {
  try {
    // 直接使用接口返回的config_key作为存储模式
    const apiStorageMode = formModel.storage_mode;

    // 根据当前选择的存储方式，只提交对应的配置
    let storageConfig = {};

    switch (formModel.storage_mode) {
      case "aliyun":
        // 阿里云配置
        storageConfig = formModel.aliyun;
        break;

      case "tencent":
        // 腾讯云配置
        storageConfig = formModel.tencent;
        break;

      default:
        // 默认情况下使用空对象
        storageConfig = {};
    }

    // 获取当前选中存储方式的名称
    const storageName = getStorageLabelByValue(formModel.storage_mode) || "";

    // 组装最终要提交的数据
    const dataToSave = {
      storage_mode: apiStorageMode,
      configValue: storageConfig,
      isDefault: true, // 设置为默认存储方式
      name: storageName // 存储方式的名称
    };

    console.log("准备保存设置:", dataToSave);

    const data = { ...dataToSave };
    delete data.storage_mode;

    // 调用更新后的 API 接口保存配置
    const res = await configApi.update("SMS", apiStorageMode, data);

    if (res.code === 200) {
      fetchSettings();
      configure();
      Message.success(res.message || "保存成功");
    } else {
      Message.error(res.message || "保存失败");
    }
  } catch (error) {
    console.error("保存短信设置失败:", error);
    Message.error("保存失败，请稍后重试");
  }
};

onMounted(() => {
  configure();
});
</script>

<style lang="less" scoped>
// 保持原有样式
.storage-options {
  width: 100%;
}

.storage-option-card {
  width: 100%;
  height: 54px;
  display: flex;
  align-items: center;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 16px;
  margin-bottom: 15px;
  cursor: pointer;
  background: #fff;
  transition: border-color 0.2s;
  position: relative;
}

.storage-option-card.active {
  border-color: rgb(var(--primary-6));
  background: #f7f8fa;
}

.storage-option-card:hover {
  border-color: rgb(var(--primary-6));
}

.storage-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.storage-icon {
  font-size: 18px;
  margin: 0 8px;
  color: #86909c;
}

.storage-text {
  font-size: 14px;
  line-height: 1.2;
}

:deep(.arco-form-item-extra) {
  margin: 8px 0px !important;
  color: #86909c;
}

:deep(.arco-form-item-label-col) {
  margin-bottom: 10px !important;
}

:deep(.arco-card) {
  border-radius: 4px;
}

:deep(.arco-input-wrapper) {
  width: 100%;
}
:deep(.arco-form-item) {
  margin-bottom: 10px !important;
}
</style>