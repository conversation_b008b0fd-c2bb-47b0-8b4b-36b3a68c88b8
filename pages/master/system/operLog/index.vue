<template>
  <div class="ma-content-block lg:flex justify-between p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 平台 -->
      <template #client="{ record }">
        <div>{{ record.request_data?._client || '-' }}</div>
      </template>
      <!-- 自定义列 - 创建时间 -->
      <template #created_at="{ record }">
        <div v-time="record.created_at"></div>
      </template>

      <template #operationBeforeExtend="{ record }">
        <a-button type="text" size="small" @click="openDetails(record)"  v-auth="['system-detail']">
          <template #icon>
            <icon-list />
          </template>
          详情
        </a-button>
      </template>
    </ma-crud>

    <!-- 抽屉 -->
    <a-drawer v-model:visible="visible" width="600px" :footer="false">
      <template #title>操作日志详细信息</template>
      <div
        class="arco-descriptions arco-descriptions-layout-horizontal arco-descriptions-size-medium arco-descriptions-border"
      >
        <div class="arco-descriptions-body">
          <table class="arco-descriptions-table">
            <tbody>
              <tr class="arco-descriptions-row">
                <td class="arco-descriptions-item-label arco-descriptions-item-label-block">请求路由</td>
                <td
                  class="arco-descriptions-item-value arco-descriptions-item-value-block"
                  colspan="1"
                >{{ currentRow?.router }}</td>
              </tr>
              <tr class="arco-descriptions-row">
                <td class="arco-descriptions-item-label arco-descriptions-item-label-block">操作用户</td>
                <td
                  class="arco-descriptions-item-value arco-descriptions-item-value-block"
                  colspan="1"
                >{{ currentRow?.username }}</td>
              </tr>
              <tr class="arco-descriptions-row">
                <td class="arco-descriptions-item-label arco-descriptions-item-label-block">请求方法</td>
                <td
                  class="arco-descriptions-item-value arco-descriptions-item-value-block"
                  colspan="1"
                >{{ currentRow?.method }}</td>
              </tr>

              <tr class="arco-descriptions-row">
                <td class="arco-descriptions-item-label arco-descriptions-item-label-block">操作模块</td>
                <td
                  class="arco-descriptions-item-value arco-descriptions-item-value-block"
                  colspan="1"
                >{{ currentRow?.module }}</td>
              </tr>
              <tr class="arco-descriptions-row">
                <td class="arco-descriptions-item-label arco-descriptions-item-label-block">创建时间</td>
                <td
                  class="arco-descriptions-item-value arco-descriptions-item-value-block"
                  colspan="1"
                  v-if="currentRow && currentRow.created_at" v-time="currentRow.created_at"
                ></td>
              </tr>
              <tr class="arco-descriptions-row">
                <td class="arco-descriptions-item-label arco-descriptions-item-label-block">用户IP</td>
                <td
                  class="arco-descriptions-item-value arco-descriptions-item-value-block"
                  colspan="1"
                >{{ currentRow?.ip }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <a-collapse
        :default-active-key="['request', 'response']"
        expand-icon-position="right"
        class="mt-3"
      >
        <a-collapse-item header="请求数据" key="request">
          <ma-code-editor v-if="visible" :modelValue="currentRow.request_data || ''" @update:modelValue="val => currentRow.request_data = val" :height="150" readonly />
        </a-collapse-item>
        <a-collapse-item header="响应数据" key="response">
          <ma-code-editor v-if="visible" :modelValue="currentRow.response_data || ''" @update:modelValue="val => currentRow.response_data = val" :height="150" readonly />
        </a-collapse-item>
      </a-collapse>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { formatJson } from "@/utils/common";
import systemApi from "@/api/master/system";
const operLogApi = systemApi.operLog;

definePageMeta({
  name: "system-operLog",
  path: "/master/system/operLog"
});

const crudRef = ref();
const visible = ref(false);
const currentRow = ref({ request_data: undefined, response_data: undefined });

const openDetails = record => {
  currentRow.value = record;
  if (record.request_data && record.request_data.length > 0) {
    currentRow.value.request_data = !/\\/g.test(record.request_data)
      ? formatJson(currentRow.value.request_data)
      : currentRow.value.request_data.replace(/,/g, ",\n");
  }
  if (record.response_data && record.response_data.length > 0) {
    currentRow.value.response_data = !/\\/g.test(record.response_data)
      ? formatJson(currentRow.value.response_data)
      : currentRow.value.response_data.replace(/,/g, "\n");
  }
  visible.value = true;
};

const crud = reactive({
  api: operLogApi.getList,
  showIndex: true,
  pageLayout: "fixed",
  operationColumn: true,
  operationColumnWidth: 200,
  searchLabelWidth: "100px",
  requestParams: { orderBy: "created_at", orderType: "desc" },
  delete: { show: true, api: operLogApi.delete, auth: ["system-delete"] },
  beforeSearch: (params) => {
    console.log(params,'params')
    // 如果有创建时间参数，转换为时间戳
    if(params.created_at){
      params.startTime =new Date(params.created_at[0]).getTime()/1000;
      params.endTime =new Date(params.created_at[1]).getTime()/1000;
      delete params.created_at
    }else{
      delete params.startTime
      delete params.endTime
    }
    return params;
  },
});

const columns = reactive([
  { title: "客户端类型", dataIndex: "client",search: true, width: 100 },
  { title: "用户名", dataIndex: "username", search: true, width: 150 },
  { title: "操作模块", dataIndex: "module",search: true, width: 150 },
  { title: "登录IP", dataIndex: "ip", width: 150 },
  { title: "请求方式", dataIndex: "method", width: 50 },
  { title: "操作", dataIndex: "operation", width: 250 },
  {
    title: "创建时间",
    dataIndex: "created_at",
    width: 180,
    search: true,
    formType: "range"
  }
]);

</script>
  
<script>
export default { name: "system:operLog" };
</script>
  
<style scoped>
:deep(.arco-collapse-item-content) {
  padding-left: 10px;
  padding-right: 10px;
}
</style>