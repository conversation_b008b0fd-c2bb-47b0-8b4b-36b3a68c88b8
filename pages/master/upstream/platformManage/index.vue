<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="record.status == 1 ? 'green' : 'red'">
          {{ record.status == 1 ? '启用' : '禁用' }}
        </a-tag>
      </template>
      
      <!-- 爬虫配置列 -->
      <template #spiderConfig="{ record }">
        <a-button type="text" size="small" @click="openSpiderConfigModal(record)">
          <template #icon><icon-eye /></template>
          查看配置
        </a-button>
      </template>
      
      <!-- 自定义列 - 创建时间 -->
      <template #createdAt="{ record }">
        <div v-time="record.createdAt"></div>
      </template>
      
      <!-- 自定义表单项 - 爬虫配置 -->
      <!-- 爬虫配置已移除 -->
    </MaCrud>
    
    <!-- 爬虫配置弹出框 -->
    <a-modal
      v-model:visible="spiderConfigVisible"
      :title="`${currentPlatform?.name || ''} 爬虫配置`"
      :width="600"
      @before-ok="handleSaveSpiderConfig"
    >
      <div class="p-4">
        <a-form :model="spiderConfigForm" layout="vertical">
          <a-form-item field="url" label="爬虫地址">
            <a-input v-model="spiderConfigForm.url" placeholder="请输入爬虫地址" @change="updateJsonFromForm" />
          </a-form-item>
          <a-form-item field="token" label="访问Token">
            <a-input v-model="spiderConfigForm.token" placeholder="请输入访问Token" @change="updateJsonFromForm" />
          </a-form-item>
          <!-- 可以根据需要添加更多配置项 -->
          <a-form-item>
            <a-alert type="info">
              <template #icon><icon-info-circle /></template>
              <template #message>
                可以直接编辑JSON配置
              </template>
            </a-alert>
          </a-form-item>
          <a-form-item field="jsonConfig" label="JSON配置">
            <a-textarea
              v-model="jsonConfig"
              :disabled="true"
              :auto-size="{ minRows: 5, maxRows: 15 }"
              placeholder="请输入JSON格式的配置"
              @change="handleJsonChange"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import upstreamApi from '@/api/master/upstream';
import orderApi from '@/api/master/order';
import { IconEye, IconInfoCircle } from '@arco-design/web-vue/es/icon';

// 判断字符串是否为URL
const isUrl = (str) => {
  try {
    new URL(str);
    return true;
  } catch (e) {
    return false;
  }
};

// 在组件挂载时获取渠道列表
onMounted(() => {
  getChannels();
  console.log('组件挂载完成，开始获取渠道列表');
});

definePageMeta({
  name: 'master-upstream-platformManage',
  path: '/master/upstream/platformManage'
})

const crudRef = ref();

// 平台数据
const tableData = ref([]);

// 爬虫配置弹出框相关
const spiderConfigVisible = ref(false);
const currentPlatform = ref(null);
const spiderConfigForm = ref({
  url: '',
  token: ''
});
const jsonConfig = ref('');



// 打开爬虫配置弹出框
const openSpiderConfigModal = (record) => {
  currentPlatform.value = record;
  // 初始化表单数据
  if (record.spiderConfig) {
    spiderConfigForm.value = { ...record.spiderConfig };
    jsonConfig.value = JSON.stringify(record.spiderConfig, null, 2);
  } else {
    spiderConfigForm.value = { url: '', token: '' };
    jsonConfig.value = JSON.stringify({ url: '', token: '' }, null, 2);
  }
  spiderConfigVisible.value = true;
};

// 处理JSON配置变化
const handleJsonChange = () => {
  try {
    const parsedConfig = JSON.parse(jsonConfig.value);
    spiderConfigForm.value = { ...parsedConfig };
  } catch (error) {
    // JSON格式错误，不更新表单
  }
};

// 表单变化时更新JSON
const updateJsonFromForm = () => {
  try {
    // 将表单数据转换为JSON字符串
    jsonConfig.value = JSON.stringify(spiderConfigForm.value, null, 2);
  } catch (error) {
    // 如果发生错误，不做处理
  }
};

// 保存爬虫配置
const handleSaveSpiderConfig = async (done) => {
  try {
    // 验证JSON格式
    const parsedConfig = JSON.parse(jsonConfig.value);
    
    // 更新平台数据
    const updateData = {
      id: currentPlatform.value.id,
      spiderConfig: parsedConfig
    };
    
    await upstreamApi.platform.update(updateData);
    Message.success('爬虫配置更新成功');
    
    // 刷新数据
    crudRef.value.refresh();
    done(true); // 关闭弹出框
  } catch (error) {
    Message.error(`配置保存失败: ${error.message || 'JSON格式错误'}`);
    done(false); // 不关闭弹出框
  }
};

// 表格列定义
// 渠道列表
const channelOptions = ref([]);

// 获取渠道列表
const getChannels = async () => {
  try {
    // 使用orderApi.getChannelList获取渠道列表
    const res = await orderApi.getChannelList({
      // 可以根据需要添加参数
      // name: '',
      // isBuiltIn: 1,
      page: 1,
      pageSize: 100 // 获取足够多的渠道
    });
    
    // 处理返回的数据格式
    if (res && res.data && res.data.items && Array.isArray(res.data.items)) {
      channelOptions.value = res.data.items.map(item => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (error) {
    Message.error('获取渠道列表失败');
    console.error('获取渠道列表失败:', error);
  }
};



const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    hide: true,
    align: 'center',
    editDisplay: false,
  },
  {
    title: '平台名称',
    dataIndex: 'name',
    align: 'center',
    search: true,
    commonRules: [{ required: true, message: '平台名称必填' }],
  },
  {
    title: '平台代码',
    dataIndex: 'code',
    align: 'center',
    search: true,
    commonRules: [{ required: true, message: '平台代码必填' }],
  },
  {
    title: '登录网址',
    dataIndex: 'loginUrl',
    align: 'center',
    search: false,
    commonRules: [{ required: true, message: '登录网址必填' }],
  },
  {
    title: '渠道',
    dataIndex: 'channelId',
    align: 'center',
    search: false,
    hide: true,
    formType: 'select',
    commonRules: [{ required: true, message: '渠道必选' }],
    dict: {
      data: channelOptions
    },
    form: {
      component: 'select',
      componentProps: {
        options: channelOptions,
        placeholder: '请选择渠道'
      }
    }
  },
  {
    title: '渠道名称',
    dataIndex: 'channelName',
    align: 'center',
    editDisplay: false,
    addDisplay: false,
  },
  {
    title: '爬虫配置',
    dataIndex: 'spiderConfig',
    align: 'center',
    width: 100,
    showOverflowTooltip: true,
    editDisplay: false,
    addDisplay: false,
    
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'center',
    formType: 'radio',
    addDefaultValue: 1,
    search: true,
    dict: {
      data: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    formType: "range",
    search: true,
    addDisplay: false,
    editDisplay: false,
    form: false
  },
];

// CRUD 配置
const crud = reactive({
  // 使用真实API获取数据
  api: upstreamApi.platform.getList,
  showIndex: true,
  pageLayout: 'fixed',
  operationColumn: true,
  operationColumnWidth: 200,
  searchLabelWidth: '100px',
      // 表单打开前处理
      beforeOpen: (form, action) => {
      console.log('表单打开前处理:', action, form);
      
      // 加载渠道列表
      if (channelOptions.value.length === 0) {
        getChannels();
      }
    },
    // 添加表单提交前处理
    beforeAdd: (form) => {
      // 表单提交前处理
      try {
        console.log('新增表单提交前处理:', JSON.stringify(form));
        // 设置爬虫配置为null，因为不需要在表单中填写
        form.spiderConfig = null;
      } catch (error) {
        console.error('表单提交前处理失败:', error);
      }
      console.log('表单最终提交数据:', JSON.stringify(form));
      return true; // 返回true允许提交
    },
    beforeEdit: (form) => {
      // 表单提交前处理
      try {
        console.log('编辑表单提交前处理:', JSON.stringify(form));
        // 保持原有的spiderConfig不变，因为不需要在表单中编辑
      } catch (error) {
        console.error('表单提交前处理失败:', error);
      }
      console.log('表单最终提交数据:', JSON.stringify(form));
      return true; // 返回true允许提交
    },
  add: {
    show: true,
    api: upstreamApi.platform.create, // 使用自定义新增按钮

  },
  edit: {
    show: true,
    api: upstreamApi.platform.update, // 使用自定义编辑按钮
  },
  delete: {
    show: true,
    api: upstreamApi.platform.delete // 使用自定义删除按钮
  },
  beforeSearch: (params) => {
    // 如果有创建时间参数，转换为时间戳
    if(params.createdAt){
      params.startTime = new Date(params.createdAt[0]).getTime();
      params.endTime = new Date(params.createdAt[1]).getTime();
      delete params.createdAt
    }else{
      delete params.startTime
      delete params.endTime
    }
    
    // 确保状态参数是整数类型
    if(params.status !== undefined && params.status !== ''){
      params.status = parseInt(params.status)
    }
    
    // 添加分页参数
    params.page = params.page || 1
    params.pageSize = params.pageSize || 10
    
    return params;
  }
});
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 图标选择器样式 */
.icon-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-item:hover {
  background-color: #f2f3f5;
}

.icon-item.active {
  border-color: #1278FF;
  background-color: #e8f3ff;
}

.icon-item .iconfont {
  font-size: 24px;
  margin-bottom: 5px;
}

.icon-name {
  font-size: 12px;
  color: #4e5969;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
</style>
