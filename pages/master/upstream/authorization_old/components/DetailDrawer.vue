<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <!-- 授权详情抽屉 -->
  <a-drawer 
    :width="650" 
    :visible="visible" 
    @cancel="closeDrawer" 
    :unmount-on-close="true"
  >
    <template #title>
      授权详情
    </template>
    
    <div class="auth-detail-container">
      <!-- 授权基本信息 -->
      <div class="auth-header mb-4 pb-4 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <a-avatar 
              :size="64" 
              :image-url="record.platform_icon || '/assets/images/platforms/' + record.platform_id + '.png'" 
              class="mr-4"
            >
              <template #icon>
                <icon-user />
              </template>
            </a-avatar>
            <div>
              <h3 class="text-lg font-medium">{{ record.store_name || '未命名店铺' }}</h3>
              <p class="text-gray-500">店铺编码：{{ record.store_code || '-' }}</p>
            </div>
          </div>
          <div>
            <a-button type="primary" @click="handleEdit">编辑</a-button>
          </div>
        </div>
      </div>
      
      <!-- 授权信息 -->
      <div class="auth-info-section mb-4 pb-4 border-b border-gray-200">
        <h4 class="text-base font-medium mb-2 text-blue-500">基础信息</h4>
        <div class="grid grid-cols-2 gap-4">
          <div class="info-item">
            <div class="text-gray-500">店铺名称：</div>
            <div>{{ record.store_name || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">店铺代码：</div>
            <div>{{ record.store_code || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">站点：</div>
            <div>{{ record.site || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">账号名称：</div>
            <div>{{ record.account_name || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">店铺状态：</div>
            <div>
              <a-tag :color="record.status == 1 ? 'green' : 'red'">
                {{ record.status == 1 ? '启用' : '禁用' }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">授权状态：</div>
            <div>
              <a-tag :color="record.auth_status == 1 ? 'green' : 'red'">
                {{ record.auth_status == 1 ? '授权成功' : '授权失败' }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">授权时间：</div>
            <div>{{ formatDate(record.auth_time) || '-' }}</div>
          </div>
        </div>
      </div>
      
      <!-- 渠道绑定 -->
      <div class="channel-binding-section mb-4 pb-4 border-b border-gray-200">
        <h4 class="text-base font-medium mb-2 text-blue-500">渠道绑定</h4>
        <div class="bg-gray-50 p-4 rounded">
          <div class="flex items-center">
            <a-avatar 
              :size="48" 
              :image-url="'/assets/images/channels/' + (record.channel_id || 'default') + '.png'" 
              class="mr-4"
            >
              <template #icon>
                <icon-user />
              </template>
            </a-avatar>
            <div>
              <h5 class="text-base font-medium">{{ record.channel_name || '暂无绑定渠道' }}</h5>
              <p class="text-gray-500 text-sm">{{ record.channel_code || '暂无渠道代码' }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 信息同步 -->
      <div class="sync-section mb-4 pb-4 border-b border-gray-200">
        <h4 class="text-base font-medium mb-2 text-blue-500">信息同步</h4>
        <div class="flex flex-wrap gap-3">
          <a-button type="primary" @click="handleSync('order')">订单同步</a-button>
          <a-button type="primary" @click="handleSync('product')">商品同步</a-button>
          <a-button type="primary" @click="handleSync('invoice')">发票同步</a-button>
          <a-button type="primary" @click="handleSync('report')">报备同步</a-button>
        </div>
      </div>
      
      <!-- 同步日志 -->
      <div class="sync-log-section">
        <h4 class="text-base font-medium mb-2 text-blue-500">同步日志</h4>
        <a-table :data="syncLogs" :bordered="false" :pagination="false" size="small">
          <template #columns>
            <a-table-column title="类型" data-index="type">
              <template #cell="{ record }">
                <a-tag :color="getSyncTypeColor(record.type)">
                  {{ getSyncTypeName(record.type) }}
                </a-tag>
              </template>
            </a-table-column>
            <a-table-column title="数量" data-index="count" />
            <a-table-column title="同步时间" data-index="sync_time">
              <template #cell="{ record }">
                {{ formatDate(record.sync_time) }}
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconUser } from '@arco-design/web-vue/es/icon';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'edit']);

// 同步日志数据
const syncLogs = ref([
  {
    id: '1',
    type: 'order',
    count: 156,
    sync_time: '2025-06-08 14:30:00'
  },
  {
    id: '2',
    type: 'product',
    count: 78,
    sync_time: '2025-06-08 13:15:00'
  },
  {
    id: '3',
    type: 'invoice',
    count: 42,
    sync_time: '2025-06-07 16:45:00'
  },
  {
    id: '4',
    type: 'report',
    count: 23,
    sync_time: '2025-06-07 11:20:00'
  }
]);

// 关闭抽屉
function closeDrawer() {
  emit('update:visible', false);
}

// 编辑按钮处理函数
function handleEdit() {
  emit('edit', props.record);
}

// 同步按钮处理函数
function handleSync(type) {
  let actionName = '';
  switch(type) {
    case 'order':
      actionName = '订单';
      break;
    case 'product':
      actionName = '商品';
      break;
    case 'invoice':
      actionName = '发票';
      break;
    case 'report':
      actionName = '报备';
      break;
  }
  
  Message.success(`开始${actionName}同步，店铺：${props.record.store_name}`);
  
  // 模拟添加新的同步日志
  const newLog = {
    id: Date.now().toString(),
    type: type,
    count: Math.floor(Math.random() * 100) + 1, // 随机生成同步数量
    sync_time: new Date().toISOString().replace('T', ' ').substring(0, 19)
  };
  
  // 将新的日志添加到日志数组中
  syncLogs.value.unshift(newLog);
  
  // 保持日志数组不超过一定数量
  if (syncLogs.value.length > 10) {
    syncLogs.value = syncLogs.value.slice(0, 10);
  }
}

// 获取同步类型名称
function getSyncTypeName(type) {
  switch(type) {
    case 'order': return '订单';
    case 'product': return '商品';
    case 'invoice': return '发票';
    case 'report': return '报备';
    default: return '未知';
  }
}

// 获取同步类型颜色
function getSyncTypeColor(type) {
  switch(type) {
    case 'order': return 'blue';
    case 'product': return 'green';
    case 'invoice': return 'orange';
    case 'report': return 'purple';
    default: return 'gray';
  }
}

// 日期格式化函数
function formatDate(dateString) {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (e) {
    return dateString;
  }
}
</script>

<style scoped>
/* 详情抽屉样式 */
.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .text-gray-500 {
  width: 100px;
  flex-shrink: 0;
}
</style>
