<!--
 - 客户详情抽屉组件
 - 
 - <AUTHOR>
-->
<template>
  <div>
    <a-drawer
      :visible="visible"
      @update:visible="handleVisibleChange"
      :title="title"
      width="50%"
      :footer="true"
      @cancel="handleClose"
    >
      <template #footer>
        <div class="flex justify-end space-x-2">
          <a-button @click="handleClose">取消</a-button>
          <a-button type="primary" @click="handleConfirm">确定</a-button>
        </div>
      </template>
      <template v-if="customer">
        <div class="p-4">
          <!-- 客户基本信息头部 -->
          <div class="customer-header mb-4 pb-4 border-b border-gray-200">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <a-avatar 
                  :size="64" 
                  :style="{ backgroundColor: '#165DFF' }"
                  class="mr-4"
                >
                  {{ customer.name ? customer.name.substring(0, 1) : 'C' }}
                </a-avatar>
                <div class="flex flex-col ml-2">
                  <h3 class="text-lg font-medium">{{ customer.name || '未命名客户' }}</h3>
                  <div class="flex items-center text-gray-600 text-sm mt-1">
                    <a-tag :color="getStatusColor(customer.status)" size="small" class="mr-2">
                      {{ getStatusText(customer.status) || '未知状态' }}
                    </a-tag>
                    <a-tag :color="getSourceColor(customer.source)" size="small">
                      {{ getSourceText(customer.source) || '未知来源' }}
                    </a-tag>
                  </div>
                </div>
              </div>
              <div>
              </div>
            </div>
            
            <!-- 客户关键信息 -->
            <div class="grid grid-cols-3 gap-4 mt-4 pt-3  border-gray-100">
              <div class="flex flex-col">
                <span class="text-gray-500 text-sm">首跟进人：{{ customer.firstContactUser || 'A' }}</span>
        
              </div>
              <div class="flex flex-col">
                <span class="text-gray-500 text-sm">首联系人：{{ customer.firstContact || 'B' }}</span>
                
              </div>
              <div class="flex flex-col">
                <span class="text-gray-500 text-sm">最后跟进时间：{{ customer.lastContactTime || '无记录' }}</span>
             
              </div>
            </div>
          </div>

        <!-- 标签页内容 -->
        <a-tabs default-active-key="info">
          <a-tab-pane key="info" title="基本信息">
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-2 gap-4">
                <!-- 联系人 -->
                <div class="info-item">
                  <div class="text-gray-500">客户编号：</div>
                  <div>{{ customer.contact || '-' }}</div>
                </div>
                
                <!-- 联系电话 -->
                <div class="info-item">
                  <div class="text-gray-500">客户名称：</div>
                  <div>{{ customer.phone || '-' }}</div>
                </div>
                
                <!-- 客户来源 -->
                <div class="info-item">
                  <div class="text-gray-500">客户来源：</div>
                  <div>
                    <a-tag :color="getSourceColor(customer.source)">
                      {{ getSourceText(customer.source) || '未知来源' }}
                    </a-tag>
                  </div>
                </div>
                
                <!-- 客户星级 -->
                <div class="info-item">
                  <div class="text-gray-500">客户星级：</div>
                  <div>
                    <a-rate :model-value="customer.star || 0" readonly :count="5" size="small" />
                  </div>
                </div>
                
                <!-- 所属行业 -->
                <div class="info-item">
                  <div class="text-gray-500">所属行业：</div>
                  <div>{{ customer.industryName || '-' }}</div>
                </div>
                
                <!-- 企业规模 -->
                <div class="info-item">
                  <div class="text-gray-500">企业规模：</div>
                  <div>{{ getScaleText(customer.scale) || '-' }}</div>
                </div>
                
                <!-- 客户状态 -->
                <div class="info-item">
                  <div class="text-gray-500">客户状态：</div>
                  <div>
                    <a-tag :color="getStatusColor(customer.status)">
                      {{ getStatusText(customer.status) || '未知状态' }}
                    </a-tag>
                  </div>
                </div>
                
                <!-- 跟进人 -->
                <div class="info-item">
                  <div class="text-gray-500">跟进人：</div>
                  <div>{{ customer.salesName || '未分配' }}</div>
                </div>
                
                <!-- 创建时间 -->
                <div class="info-item">
                  <div class="text-gray-500">创建时间：</div>
                  <div>{{ customer.createTime || '-' }}</div>
                </div>
                
                <!-- 首跟进人 -->
                <div class="info-item">
                  <div class="text-gray-500">最近跟进人：</div>
                  <div>{{ customer.firstContactUser || '-' }}</div>
                </div>

                <!-- 电子邮箱 -->
                <div class="info-item">
                  <div class="text-gray-500">电子邮箱：</div>
                  <div>{{ customer.email || '-' }}</div>
                </div>
                
                <!-- 详细地址 -->
                <div class="info-item col-span-2">
                  <div class="text-gray-500">详细地址：</div>
                  <div>{{ customer.address || '-' }}</div>
                </div>
                
                <!-- 备注 -->
                <div class="info-item col-span-2">
                  <div class="text-gray-500">备注：</div>
                  <div>{{ customer.remark || '-' }}</div>
                </div>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="contacts" title="联系人">
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm text-gray-500">共 {{ contacts && contacts.length || 0 }} 个联系人</div>
              <a-button type="primary" size="small" @click="addContact">
                <template #icon><icon-plus /></template>
                新增联系人
              </a-button>
            </div>
            <a-table :data="contacts || []" :pagination="false">
              <template #columns>
                <a-table-column title="姓名" data-index="name"></a-table-column>
                <a-table-column title="职位" data-index="position"></a-table-column>
                <a-table-column title="电话" data-index="phone"></a-table-column>
                <a-table-column title="邮箱" data-index="email"></a-table-column>
                <a-table-column title="操作" width="150">
                  <template #cell="{ record }">
                    <a-space>
                      <a-button type="text" size="small" @click="editContact(record)">
                        <template #icon><icon-edit /></template>
                        编辑
                      </a-button>
                      <a-button type="text" size="small" status="danger" @click="deleteContact(record)">
                        <template #icon><icon-delete /></template>
                        删除
                      </a-button>
                    </a-space>
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="followRecords" title="跟进记录">
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm text-gray-500">共 {{ followRecords && followRecords.length || 0 }} 条跟进记录</div>
              <a-button type="primary" size="small" @click="addFollowRecord">
                <template #icon><icon-plus /></template>
                跟进
              </a-button>
            </div>
            <a-table :data="followRecords || []" :pagination="false">
              <template #columns>
                <a-table-column title="跟进时间" data-index="time"></a-table-column>
                <a-table-column title="跟进人员" data-index="user"></a-table-column>
                <a-table-column title="跟进内容" data-index="content"></a-table-column>
                <a-table-column title="客户状态" data-index="status">
                  <template #cell="{ record }">
                    <a-tag :color="record.status ? getStatusColor(record.status) : 'gray'">
                      {{ record.status ? getStatusText(record.status) : '未设置' }}
                    </a-tag>
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="communications" title="沟通记录">
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm text-gray-500">共 {{ communications && communications.length || 0 }} 条沟通记录</div>
              <a-button type="primary" size="small" @click="openCommunicationModal">
                <template #icon><icon-plus /></template>
                新增沟通记录
              </a-button>
            </div>
            <a-table :data="communications || []" :pagination="false">
              <template #columns>
                <a-table-column title="沟通时间" data-index="time"></a-table-column>
                <a-table-column title="沟通方式" data-index="method"></a-table-column>
                <a-table-column title="沟通内容" data-index="content" :width="400"></a-table-column>
                <a-table-column title="沟通人员" data-index="user"></a-table-column>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="opportunities" title="商机记录">
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm text-gray-500">共 {{ opportunities && opportunities.length || 0 }} 条商机记录</div>
              <a-button type="primary" size="small" @click="openOpportunityModal">
                <template #icon><icon-plus /></template>
                新增商机
              </a-button>
            </div>
            <a-table :data="opportunities || []" :pagination="false">
              <template #columns>
                <a-table-column title="商机名称" data-index="name"></a-table-column>
                <a-table-column title="预计金额" data-index="amount"></a-table-column>
                <a-table-column title="商机阶段" data-index="stage"></a-table-column>
                <a-table-column title="负责人" data-index="owner"></a-table-column>
                <a-table-column title="创建时间" data-index="createTime"></a-table-column>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="orders" title="订单记录">
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm text-gray-500">共 {{ orders && orders.length || 0 }} 条订单记录</div>
            </div>
            <a-table :data="orders || []" :pagination="false">
              <template #columns>
                <a-table-column title="订单编号" data-index="orderNo"></a-table-column>
                <a-table-column title="订单金额" data-index="amount"></a-table-column>
                <a-table-column title="订单状态" data-index="status"></a-table-column>
                <a-table-column title="创建时间" data-index="createTime"></a-table-column>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="logs" title="操作日志">
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm text-gray-500">共 {{ logs && logs.length || 0 }} 条操作日志</div>
            </div>
            <a-table :data="logs || []" :pagination="false">
              <template #columns>
                <a-table-column title="操作时间" data-index="time"></a-table-column>
                <a-table-column title="操作人员" data-index="user"></a-table-column>
                <a-table-column title="操作类型" data-index="type"></a-table-column>
                <a-table-column title="操作内容" data-index="content"></a-table-column>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
        </div>
      </template>
    </a-drawer>

    <!-- 跟进记录弹窗 -->
    <FollowRecordForm
      :visible="followModal.visible"
      @update:visible="followModal.visible = $event"
      :title="followModal.title"
      :customer="customer"
      :status-options="statusOptions"
      @save="handleSaveFollowRecord"
      @close="followModal.visible = false"
    />
    
    <!-- 商机弹窗 -->
    <OpportunityForm
      :visible="opportunityModal.visible"
      @update:visible="opportunityModal.visible = $event"
      :title="opportunityModal.title"
      :customer="customer"
      :opportunity="currentOpportunity"
      @save="handleOpportunitySaved"
      @close="opportunityModal.visible = false"
    />
    
    <!-- 分配销售弹窗已移动到客户列表页面 -->
    
    <!-- 新增联系人弹窗 -->
    <ContactForm
      :visible="contactModal.visible"
      @update:visible="contactModal.visible = $event"
      :title="contactModal.title"
      :customer="customer"
      :contact="contactForm"
      @save="handleContactSaved"
      @close="contactModal.visible = false"
      @refresh="emit('refresh')"
    />
    
    <!-- 新增沟通记录弹窗 -->
    <CommunicationForm
      :visible="communicationModal.visible"
      @update:visible="communicationModal.visible = $event"
      :title="communicationModal.title"
      :customer="customer"
      :contacts="contacts"
      @save="handleCommunicationSaved"
      @close="communicationModal.visible = false"
      @add-contact="addContactFromCommunication"
      @refresh="emit('refresh')"
    />
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import dayjs from 'dayjs';

// 导入各种表单组件
import ContactForm from './ContactForm.vue';
import CommunicationForm from './CommunicationForm.vue';
import OpportunityForm from './OpportunityForm.vue';
import FollowRecordForm from './FollowRecordForm.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '客户详情'
  },
  customer: {
    type: Object,
    default: () => ({})
  },
  contacts: {
    type: Array,
    default: () => []
  },
  followRecords: {
    type: Array,
    default: () => []
  },
  communications: {
    type: Array,
    default: () => []
  },
  opportunities: {
    type: Array,
    default: () => []
  },
  orders: {
    type: Array,
    default: () => []
  },
  logs: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits([
  'update:visible', 
  'edit',
  'assign',
  'close',
  'confirm',
  'refresh',
  'action',
  'delete-contact'
]);

// 定义弹窗状态
const followModal = reactive({
  visible: false,
  title: '添加跟进记录'
});

const opportunityModal = reactive({
  visible: false,
  title: '新增商机'
});

// 当前操作的商机数据
const currentOpportunity = reactive({
  id: undefined,
  customerId: undefined,
  name: '',
  amount: '',
  stage: '',
  owner: '',
  createTime: '',
  remark: ''
});

// 分配销售人员功能已移动到客户列表页面

const contactModal = reactive({
  visible: false,
  title: '新增联系人'
});

const communicationModal = reactive({
  visible: false,
  title: '新增沟通记录'
});

// 联系人表单数据
const contactForm = reactive({
  id: undefined,
  customerId: undefined,
  name: '',
  position: '',
  phone: '',
  wechat: '',
  email: '',
  importance: 3,
  remark: '',
  fromCommunication: false
});

// 客户状态选项
const statusOptions = ref([
  { label: '了解产品', value: 1 },
  { label: '正在跟进', value: 2 },
  { label: '准备购买', value: 3 },
  { label: '复购', value: 4 },
]);

// 处理可见性变化
const handleVisibleChange = (val) => {
  emit('update:visible', val);
  if (!val) {
    // 当抖屉关闭时触发close事件
    emit('close');
  }
};

// 处理关闭按钮点击
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 处理确认按钮点击
const handleConfirm = () => {
  emit('confirm');
  emit('update:visible', false);
};

// 分配销售人员功能已移动到客户列表页面

// 打开联系人模态框
const openContactModal = (contact = null) => {
  // 重置当前联系人数据
  Object.keys(contactForm).forEach(key => {
    contactForm[key] = contact && contact[key] !== undefined ? contact[key] : '';
  });
  
  if (contact) {
    contactForm.id = contact.id;
    contactModal.title = '编辑联系人';
  } else {
    contactForm.id = undefined;
    contactModal.title = '新增联系人';
  }
  
  contactModal.visible = true;
};

// 添加联系人 - 快捷方式
const addContact = () => {
  openContactModal();
};

// 编辑联系人 - 快捷方式
const editContact = (contact) => {
  openContactModal(contact);
};

// 处理联系人保存成功
const handleContactSaved = (contactData) => {
  if (!contactData) return;
  
  try {
    if (contactData.id) {
      // 编辑模式
      const index = props.contacts.findIndex(item => item.id === contactData.id);
      if (index !== -1) {
        // 更新现有联系人
        const updatedContacts = [...props.contacts];
        updatedContacts[index] = { ...contactData };
        emit('update:contacts', updatedContacts);
      }
    } else {
      // 新增模式
      const updatedContacts = [contactData, ...props.contacts];
      emit('update:contacts', updatedContacts);
    }
    
    // 刷新数据
    emit('refresh');
  } catch (error) {
    console.error('处理联系人数据失败', error);
  }
};

// 删除联系人
const deleteContact = (contact) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除联系人 ${contact.name} 吗？`,
    onOk: () => {
      // 删除联系人
      const updatedContacts = props.contacts.filter(item => item.id !== contact.id);
      emit('update:contacts', updatedContacts);
      Message.success('删除联系人成功');
      emit('refresh');
      emit('action', 'contactDeleted', {
        customerId: props.customer?.id,
        contactId: contact.id
      });
    }
  });
};

// 添加跟进记录
const addFollowRecord = () => {
  followModal.title = '添加跟进记录';
  followModal.visible = true;
};

// 保存跟进记录
const handleSaveFollowRecord = (followData, done) => {
  if (!followData) {
    done(false);
    return;
  }
  
  try {
    // 模拟保存操作
    setTimeout(() => {
      // 更新跟进记录列表
      if (followData.id) {
        // 编辑模式
        const index = followRecords.findIndex(item => item.id === followData.id);
        if (index !== -1) {
          followRecords[index] = { ...followData };
        }
      } else {
        // 新增模式
        const newRecord = {
          id: Date.now(),
          customerId: props.customer?.id,
          ...followData,
          createdAt: new Date().toISOString()
        };
        followRecords.unshift(newRecord);
      }
      
      // 更新客户状态
      if (followData.status && props.customer) {
        props.customer.status = followData.status;
        props.customer.statusName = getStatusText(followData.status);
        
        // 通知主页面更新状态
        emit('action', 'updateStatus', {
          customerId: props.customer.id,
          status: followData.status
        });
      }
      
      followModal.visible = false;
      Message.success('跟进记录保存成功');
      done();
    }, 500);
  } catch (error) {
    console.error('保存跟进记录失败', error);
    Message.error('保存跟进记录失败');
    done(false);
  }
};

// 打开沟通记录弹窗
const openCommunicationModal = () => {
  communicationModal.title = '新增沟通记录';
  communicationModal.visible = true;
};

// 从沟通记录弹窗中新增联系人
const addContactFromCommunication = () => {
  // 先隐藏沟通记录弹窗
  communicationModal.visible = false;
  
  // 然后打开新增联系人弹窗
  contactForm.fromCommunication = true;
  addContact();
};

// 处理沟通记录保存成功
const handleCommunicationSaved = (communicationData) => {
  if (!communicationData) return;
  
  // 添加到沟通记录列表
  const updatedCommunications = [communicationData, ...props.communications];
  emit('update:communications', updatedCommunications);
  
  // 更新客户最后联系时间
  const updatedCustomer = { ...props.customer };
  updatedCustomer.lastContactTime = communicationData.time;
  emit('update:customer', updatedCustomer);
  
  // 刷新数据
  emit('refresh');
};

// 打开商机模态框
const openOpportunityModal = (opportunity = null) => {
  // 重置当前商机数据
  Object.keys(currentOpportunity).forEach(key => {
    currentOpportunity[key] = opportunity && opportunity[key] !== undefined ? opportunity[key] : '';
  });
  
  // 设置模态框标题
  opportunityModal.title = opportunity && opportunity.id ? '编辑商机' : '新增商机';
  opportunityModal.visible = true;
};

// 处理商机保存成功
const handleOpportunitySaved = (opportunityData, done) => {
  if (!opportunityData) {
    done(false);
    return;
  }
  
  try {
    // 更新商机列表
    if (opportunityData.id) {
      // 编辑模式
      const index = props.opportunities.findIndex(item => item.id === opportunityData.id);
      if (index !== -1) {
        const updatedOpportunities = [...props.opportunities];
        updatedOpportunities[index] = { ...opportunityData };
        emit('update:opportunities', updatedOpportunities);
      }
    } else {
      // 新增模式
      const updatedOpportunities = [opportunityData, ...props.opportunities];
      emit('update:opportunities', updatedOpportunities);
    }
    
    // 通知主页面更新数据
    emit('action', 'opportunityAdded', {
      customerId: props.customer?.id
    });
    
    // 关闭模态框
    opportunityModal.visible = false;
    
    // 刷新数据
    emit('refresh');
    
    done(true);
  } catch (error) {
    console.error('保存商机失败', error);
    done(false);
  }
};

// 客户来源文本
const getSourceText = (source) => {
  switch (source) {
    case 1: return '自主开发';
    case 2: return '推荐介绍';
    case 3: return '复购';
    default: return '未知';
  }
};

// 客户来源颜色
const getSourceColor = (source) => {
  switch (source) {
    case 1: return 'blue';
    case 2: return 'green';
    case 3: return 'purple';
    default: return 'gray';
  }
};

// 客户状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1: return '了解产品';
    case 2: return '正在跟进';
    case 3: return '准备购买';
    case 4: return '复购';
    default: return '未知';
  }
};

// 客户状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1: return 'blue';
    case 2: return 'orange';
    case 3: return 'green';
    case 4: return 'purple';
    default: return 'gray';
  }
};

// 企业规模文本
const getScaleText = (scale) => {
  switch (scale) {
    case 1: return '初创企业';
    case 2: return '中小企业';
    case 3: return '中型企业';
    case 4: return '大型企业';
    case 5: return '集团企业';
    default: return '未知';
  }
};
</script>

<style scoped>
.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item .text-gray-500 {
  width: 80px;
  flex-shrink: 0;
}

.customer-header {
  position: relative;
}
</style>
