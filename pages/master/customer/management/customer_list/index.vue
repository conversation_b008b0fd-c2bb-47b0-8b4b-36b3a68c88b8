<!--
 - 客户列表页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      
      <!-- 自定义客户名称列 -->
      <template #name="{ record }">
        <a-link @click="viewDetail(record)">{{ record.name }}</a-link>
      </template>
      
      <!-- 自定义客户星级列 -->
      <template #star="{ record }">
        <a-rate :model-value="record.star || 0" readonly :count="5" size="small" />
      </template>
      
      <!-- 自定义客户来源列 -->
      <template #source="{ record }">
        <a-tag :color="getSourceColor(record.source)">
          {{ getSourceText(record.source) }}
        </a-tag>
      </template>
      
      <!-- 自定义销售人员列 -->
      <template #salesName="{ record }">
        <span>
          {{ record.salesName || '未分配' }}
        </span>
      </template>
      
      <!-- 自定义状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>
      <!-- 自定义操作列 -->
      <template #operation="{ record }">
        <a-button type="text" size="small" @click="viewDetail(record)">
          <template #icon><icon-eye /></template>查看</a-button>
        <a-button type="text" size="small" @click="editCustomer(record)">
            <template #icon><icon-edit /></template>编辑</a-button>
        <a-button type="text" size="small" @click="handleAssignSales(record)">
          <template #icon><icon-user /></template>分配</a-button>
      </template>

      <template #tableBeforeButtons>
        <a-button
                type="primary" status="primary"
                class="w-full lg:w-auto mt-2 lg:mt-0"
                @click="handleAddCustomer"
              >
                <template #icon><icon-plus /></template>
                新增
              </a-button>
      </template>      
    </ma-crud>

    <!-- 客户详情抽屉 -->
    <CustomerDetail
      v-model:visible="detailDrawer.visible"
      :title="detailDrawer.title"
      :customer="detailDrawer.customer"
      :contacts="detailDrawer.contacts"
      :follow-records="detailDrawer.followRecords"
      :communications="detailDrawer.communications"
      :opportunities="detailDrawer.opportunities"
      :orders="detailDrawer.orders"
      :logs="detailDrawer.logs"
      @close="closeDetailDrawer"
      @confirm="closeDetailDrawer"
      @edit="editCustomer"
      @action="handleDetailAction"
    />
    
    <!-- 客户表单组件 -->
    <CustomerForm ref="customerFormRef" @save="handleCustomerSaved" />

    <!-- 分配销售人员弹窗 -->
    <AssignSalesForm
      :visible="assignModal.visible"
      @update:visible="assignModal.visible = $event"
      :title="assignModal.title"
      :customer="assignModal.customer"
      @submit="handleAssignSalesSubmit"
      @close="assignModal.visible = false"
    />

    <!-- 弹窗已移动到CustomerDetail组件中 -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import CustomerForm from './components/CustomerForm.vue';
import CustomerDetail from './components/CustomerDetail.vue';
import AssignSalesForm from './components/AssignSalesForm.vue';
import dayjs from 'dayjs';
import { Message } from '@arco-design/web-vue';


definePageMeta({
  name: "master-customer_list",
  path: "/master/customer/management/customer_list",
})


// 表格列定义
const columns = [
  {
    title: '客户名称',
    dataIndex: 'name',
    search: true,
    slotName: 'name',
  },
  {
    title: '客户星级',
    dataIndex: 'star',
    search: true,
    width: 200,
    slotName: 'star',
  },
  {
    title: '客户来源',
    dataIndex: 'source',
    slotName: 'source',
  },
  {
    title: '联系人',
    search: true,
    dataIndex: 'contact',
  },
  {
    title: '联系电话',
    search: true,
    dataIndex: 'phone',
  },
  {
    title: '所属行业',
    search: true,
    dataIndex: 'industryName',
  },
  {
    title: '跟进人',
    dataIndex: 'salesName',
    slotName: 'salesName',
  },
  {
    title: '最后跟进时间',
    search: true,
    dataIndex: 'lastContactTime',
  },
  {
    title: '状态',
    dataIndex: 'status', 
    search: true,
    slotName: 'status',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 280,
    fixed: 'right',
  },
];

// CRUD配置
const crud = reactive({
  // API配置 - 这里需要替换为实际的API
  api: async (params) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据
    const data = {
      items: [
        {
          id: 1,
          name: '阿里巴巴',
          contact: '马云',
          phone: '13800138000',
          email: '<EMAIL>',
          industryName: '互联网',
          industry: 1,
          salesName: '张三, 李四, 王五',
          salesId: '1,2,3',
          createTime: '2025-01-01 12:00:00',
          lastContactTime: '2025-05-20 15:30:00',
          status: 3,
          star: 5,
          source: 1,
          address: '杭州市余杭区文一西路969号',
          remark: '重点客户 - 多人负责'
        },
        {
          id: 2,
          name: '腾讯科技',
          contact: '马化腾',
          phone: '13900139000',
          email: '<EMAIL>',
          industryName: '互联网',
          industry: 1,
          salesName: '李四',
          salesId: 2,
          createTime: '2025-01-02 12:00:00',
          lastContactTime: '2025-05-22 10:15:00',
          status: 2,
          star: 4,
          source: 1,
          address: '深圳市南山区高新科技园',
          remark: '重点客户'
        },
        {
          id: 3,
          name: '百度科技',
          contact: '李彦宏',
          phone: '13700137000',
          email: '<EMAIL>',
          industryName: '互联网',
          industry: 1,
          salesName: '王五',
          salesId: 3,
          createTime: '2025-01-03 12:00:00',
          lastContactTime: '2025-05-15 16:45:00',
          status: 1,
          star: 3,
          source: 2,
          address: '北京市海淀区西北旺东路10号',
          remark: '重点客户'
        },
        {
          id: 4,
          name: '量子基金',
          contact: '乔治·索罗斯',
          phone: '13700137000',
          email: '<EMAIL>',
          industryName: '金融',
          industry: 2,
          salesName: '王五',
          salesId: 3,
          createTime: '2025-01-03 12:00:00',
          lastContactTime: '2025-05-25 09:30:00',
          status: 4,
          star: 5,
          source: 3,
          address: '美国纽约州贝德福特镇',
          remark: '重'
        }
      ],
      total: 3,
    };
    
    return {
      code: 200,
      message: '操作成功',
      data: {
        items: data.items,
        total: data.total,
      }
    };
  },
  
  // 表格配置
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  searchLabelWidth: '120px',
});

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1: return '了解产品';
    case 2: return '正在跟进';
    case 3: return '准备购买';
    case 4: return '复购';
    default: return '未知状态';
  }
};

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1: return 'blue';
    case 2: return 'orange';
    case 3: return 'green';
    case 4: return 'purple';
    default: return 'gray';
  }
};

// 获取企业规模文本
const getScaleText = (scale) => {
  switch (scale) {
    case 1: return '初创企业';
    case 2: return '中小企业';
    case 3: return '中型企业';
    case 4: return '大型企业';
    case 5: return '集团企业';
    default: return '未知规模';
  }
};

// 获取来源文本
const getSourceText = (source) => {
  switch (source) {
    case 1: return '自主开发';
    case 2: return '推荐介绍';
    case 3: return '复购';
    default: return '未知来源';
  }
};

// 获取来源颜色
const getSourceColor = (source) => {
  switch (source) {
    case 1: return 'blue';
    case 2: return 'green';
    case 3: return 'purple';
    default: return 'gray';
  }
};

// 导入客户
const handleImport = () => {
  Message.info('导入客户功能开发中');
};

// 导出客户
const handleExport = () => {
  Message.info('导出客户功能开发中');
};
const loading = ref(false);
const customerList = ref([]);

// ma-crud组件引用
const crudRef = ref(null);

// 客户表单组件引用
const customerFormRef = ref(null);

// 客户详情抽屉
const detailDrawer = reactive({
  visible: false,
  title: '客户详情',
  customer: {
    id: undefined,
    name: '',
    typeName: '',
    industryName: '',
    sourceName: '',
    statusName: '',
    salesName: '',
    contactName: '',
    contactPhone: '',
    email: '',
    address: '',
    createTime: '',
    lastContactTime: '',
    remark: '',
    star: 0,
    source: undefined,
    scale: undefined,
    status: undefined
  },
  contacts: [],
  communications: [],
  followRecords: [],
  opportunities: [],
  orders: [],
  logs: []
});

// 分配销售人员弹窗
const assignModal = reactive({
  visible: false,
  title: '分配销售人员',
  customer: {}
});

// 搜索表单
const searchForm = reactive({
  name: '',
  contact: '',
  phone: '',
  industry: undefined,
  star: undefined,
  lastContactTime: null,
  status: undefined
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
});


// 行业选项
const industryOptions = ref([
  { label: '互联网', value: 1 },
  { label: '金融', value: 2 },
  { label: '教育', value: 3 },
  { label: '医疗', value: 4 },
  { label: '制造业', value: 5 },
  { label: '零售', value: 6 },
  { label: '其他', value: 7 },
]);

// 客户星级选项
const starOptions = ref([
  { label: '一星', value: 1 },
  { label: '二星', value: 2 },
  { label: '三星', value: 3 },
  { label: '四星', value: 4 },
  { label: '五星', value: 5 },
]);

// 客户来源选项
const sourceOptions = ref([
  { label: '自主开发', value: 1 },
  { label: '推荐介绍', value: 2 },
  { label: '复购', value: 3 },
]);

// 客户状态选项
const statusOptions = ref([
  { label: '了解产品', value: 1 },
  { label: '正在跟进', value: 2 },
  { label: '准备购买', value: 3 },
  { label: '复购', value: 4 },
]);

// 企业规模选项
const scaleOptions = ref([
  { label: '初创企业', value: 1 },
  { label: '中小企业', value: 2 },
  { label: '中型企业', value: 3 },
  { label: '大型企业', value: 4 },
  { label: '集团企业', value: 5 },
]);

// 销售人员选项
const salesOptions = ref([]);
const salesLoading = ref(false);


// 分配销售弹窗已在前面声明





// 新增客户
const handleAddCustomer = () => {
  customerFormRef.value.open();
};

// 编辑客户
const editCustomer = (record) => {
  customerFormRef.value.open(record);
};

// 处理客户保存后的回调
const handleCustomerSaved = (customerData) => {
  // 刷新客户列表
  // 显示成功消息
  Message.success(`${customerData.id ? '编辑' : '新增'}客户成功`);
};

// 查看客户详情
const viewDetail = (record, activeTab = 'basic') => {
  if (!record) return;
  
  detailDrawer.title = `客户详情 - ${record.name}`;
  detailDrawer.data = record;
  detailDrawer.visible = true;
  // activeTab参数可以在需要时使用，但目前detailDrawer中没有tabs属性
  setTimeout(() => {
    // 基本信息
    detailDrawer.customer = {
      id: record.id,
      name: record.name,
      contact: record.contact,
      phone: record.phone,
      email: record.email,
      industryName: record.industryName,
      industry: record.industry,
      salesName: record.salesName,
      salesId: record.salesId,
      source: record.source,
      sourceName: getSourceText(record.source),
      status: record.status,
      statusName: getStatusText(record.status),
      scale: record.scale || 2,
      scaleName: getScaleText(record.scale || 2),
      star: record.star || 3,
      region: record.region || ['440000', '440300', '440305'],
      regionText: record.regionText || '广东省深圳市南山区',
      address: record.address || '',
      website: record.website || 'https://www.example.com',
      createTime: record.createTime,
      lastContactTime: record.lastContactTime,
      // 新增字段
      firstContactUser: record.firstContactUser || 'A', // 首跟进人
      firstContact: record.firstContact || 'B', // 首联系人
      firstContactTime: record.firstContactTime || '2025-05-01 10:00:00', // 首次跟进时间
      remark: record.remark || ''
    };
    
    // 联系人
    detailDrawer.contacts = [
      {
        id: 101,
        customerId: record.id,
        name: '张经理',
        position: '采购经理',
        phone: '13812345678',
        wechat: 'zhangmgr',
        email: '<EMAIL>',
        importance: 4,
        remark: '主要决策人',
        createdAt: '2023-09-01 08:30:00'
      },
      {
        id: 102,
        customerId: record.id,
        name: '王主管',
        position: '技术主管',
        phone: '13987654321',
        wechat: 'wangtech',
        email: '<EMAIL>',
        importance: 3,
        remark: '技术对接人',
        createdAt: '2023-09-05 14:20:00'
      }
    ];
    
    // 跟进记录
    detailDrawer.followRecords = [
      {
        id: 1,
        customerId: record.id,
        time: '2023-09-15 14:30:00',
        user: '张三',
        content: '展示了产品功能，客户表示有兴趣，但还需要考虑预算',
        status: 2,
        createdAt: '2023-09-15 14:30:00'
      },
      {
        id: 2,
        customerId: record.id,
        time: '2023-09-10 10:15:00',
        user: '李四',
        content: '初次联系客户，介绍了公司和产品线',
        status: 1,
        createdAt: '2023-09-10 10:15:00'
      }
    ];
    
    // 商机
    detailDrawer.opportunities = [
      {
        id: 1,
        customerId: record.id,
        name: 'CRM系统项目',
        amount: '50000',
        stage: '需求收集',
        owner: '张三',
        createTime: '2023-09-12 09:00:00',
        remark: '客户正在评估多家公司的方案',
        createdAt: '2023-09-12 09:00:00'
      }
    ];
    
    // 沟通记录
    detailDrawer.communications = [
      {
        id: 1,
        customerId: record.id,
        contactId: 101,
        contactName: '张经理',
        contactPosition: '采购经理',
        time: '2023-09-15 14:30:00',
        method: '电话',
        content: '讨论了产品功能和价格，客户对A功能非常感兴趣，但对B功能有疑虑。需要准备更详细的方案分析。',
        user: '张三',
        attachments: []
      }
    ];
    
    // 操作日志
    detailDrawer.logs = [
      {
        id: 1,
        customerId: record.id,
        time: '2023-09-15 14:30:00',
        user: '张三',
        type: '跟进',
        content: '添加了跟进记录'
      },
      {
        id: 2,
        customerId: record.id,
        time: '2023-09-12 09:00:00',
        user: '张三',
        type: '商机',
        content: '创建了新商机'
      },
    ];
  }, 500);
};

// 关闭详情抽屉
const closeDetailDrawer = () => {
  detailDrawer.visible = false;
};

// assignModal变量已经在前面声明过了

// 处理分配销售人员
const handleAssignSales = (record) => {
  assignModal.customer = record;
  assignModal.visible = true;
};

// 处理分配销售人员提交
const handleAssignSalesSubmit = (data, callback) => {
  // 模拟API调用
  setTimeout(() => {
    try {
      // 更新客户列表中的销售人员信息
      const index = customerList.value.findIndex(item => item.id === data.customerId);
      if (index !== -1) {
        customerList.value[index].salesId = data.salesIdStr;
        customerList.value[index].salesName = data.salesNameStr;
      }
      
      // 显示成功消息
      Message.success('销售人员分配成功');
      callback(true);
    } catch (error) {
      console.error('保存分配销售人员失败', error);
      Message.error('保存分配销售人员失败');
      callback(false);
    }
  }, 500);
};
// 查看客户详情时的回调函数，用于通知CustomerDetail组件执行相应操作
const handleDetailAction = (action, data) => {
  // 根据action执行不同操作
  
  // 更新列表数据（如果需要）
  if (action === 'updateStatus' && data) {
    const customerIndex = customerList.value.findIndex(item => item.id === data.customerId);
    if (customerIndex !== -1 && data.status) {
      customerList.value[customerIndex].status = data.status;
    }
  }
  
  // 刷新客户列表（如果需要）
  if (action === 'refresh') {

  }
};

// 删除未使用的addCommunication函数

</script>

<style scoped>
.arco-form {
  margin-bottom: 0;
}

/* 只对沟通记录弹窗中的表单项应用宽度设置 */
.communicationModal .arco-form-item {
  width: 100%;
}

.rich-text-preview img {
  max-width: 100%;
  margin: 8px 0;
}

.communicationModal .arco-textarea-wrapper {
  width: 100%;
}

.ma-content-block {
  min-height: calc(100vh - 60px);
  width: 100%;
}

.customer-header {
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.info-section {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item .text-gray-500 {
  width: 120px;
  flex-shrink: 0;
}
</style>
