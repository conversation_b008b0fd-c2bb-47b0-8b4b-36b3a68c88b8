<template>
  <div class="product-detail-page website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/one/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/one/product">产品中心</a>
            </li>
            <li class="breadcrumb-item">产品详情</li>
          </ol>
        </nav>
      </div>
    </div>

    <!-- 产品详情主体 -->
    <div class="product-detail-container">
      <div class="container">
        <!-- 产品基本信息 -->
        <div class="product-basic-info">
          <div class="product-image">
            <a-image :src="detailProduct.image" :alt="detailProduct.name" :preview="false"/>
          </div>
          <div class="product-info">
            <h1 class="product-name">{{ detailProduct.name }}</h1>
            <div class="product-desc"> {{detailProduct.description}}</div>
            <a-divider />
            <div class="product-desc">
                {{ detailProduct.specification }}
            </div>
          </div>
        </div>
        <a-divider style="margin: 3rem 0;"/>
        <div class="product-detail">
          <div v-html="detailProduct.detail_images"></div>
            <!-- <a-image :src="detailProduct.detail_images" :preview="false" /> -->
        </div>
      </div>
      
    </div>


    <!-- 页脚 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>

<script setup>
import procuctApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted, onBeforeUnmount } from "vue";
import NavBar from "@/components/website/one/NavBar.vue";
import Footer from "@/components/website/one/Footer.vue";

// 定义页面元数据
definePageMeta({
  layout: false,
  path: '/website/one/productdetail/:id'
});

// 获取路由参数
const route = useRoute();

// 使用useRuntimeConfig来获取基础URL
const baseUrl = "/website/one/assets";

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = path => {
  return getAssetUrl("images", path);
};

// 激活的标签页
const activeTab = ref("specs");

// 产品数据
const productData = ref({
  id: 1,
  name: "小米电视S Mini LED 2025",
  description:
    "新锐Mini LED光控技术 超高清画质 高亮度高对比度小米电视全新旗舰产品，全新一代Mini LED 超薄设计，2025年全新发布",
  price: 4999,
  image: "product_detail.png",
  specs: [
    { name: "屏幕尺寸", value: "75英寸" },
    { name: "分辨率", value: "4K超高清 (3840 x 2160)" },
    { name: "屏幕类型", value: "Mini LED" },
    { name: "刷新率", value: "120Hz" },
    { name: "HDR", value: "Dolby Vision, HDR10+" },
    { name: "内存", value: "4GB" },
    { name: "存储空间", value: "64GB" },
    { name: "操作系统", value: "小米电视系统 MIUI TV" },
    { name: "连接方式", value: "WiFi 6, 蓝牙 5.2, HDMI 2.1 x 3, USB 3.0 x 2" },
    { name: "音频", value: "Dolby Atmos, 立体声环绕音效" },
    { name: "尺寸(长*宽*高)", value: "1675 x 967 x 33 mm" },
    { name: "重量", value: "24.5kg" }
  ],
  features: [
    {
      title: "新一代Mini LED背光技术",
      description:
        "采用高精密Mini LED背光技术，配合全新光学算法，实现更精准的光线控制和更高的对比度。",
      image: "feature1.png"
    },
    {
      title: "全新超薄机身设计",
      description:
        "机身厚度仅为33mm，采用全金属一体化机身，简约现代的设计风格，完美融入家居环境。",
      image: "feature2.png"
    },
    {
      title: "全新升级的MIUI TV系统",
      description:
        "搭载最新的MIUI TV系统，支持语音控制、智能推荐、多屏互动等功能，带来更便捷的操作体验。",
      image: "feature3.png"
    }
  ],
  reviews: [
    {
      user: "用户123456",
      date: "2025-05-20",
      rating: 5,
      content:
        "画质非常好，Mini LED技术确实比普通LED电视有很大提升，对比度和色彩表现都很出色。"
    },
    {
      user: "小米粉丝",
      date: "2025-05-18",
      rating: 4,
      content:
        "电视外观设计非常漂亮，超薄机身挂在墙上很有艺术感。系统流畅度还可以，偏低一星是因为听说会有广告。"
    },
    {
      user: "科技追求者",
      date: "2025-05-15",
      rating: 5,
      content:
        "画质和音质都很棒，特别是看电影时的沉浸感很强。Dolby Vision和Dolby Atmos的加持让观影体验提升了一个档次。"
    }
  ]
});

// 相关产品
const relatedProducts = ref([
  { id: 2, name: "小米电视S 65英寸", price: 3999, image: "related1.png" },
  { id: 3, name: "小米电视EA 55英寸", price: 2999, image: "related2.png" },
  { id: 4, name: "小米电视A Pro 65英寸", price: 4599, image: "related3.png" },
  { id: 5, name: "红米智能电视X65", price: 3499, image: "related4.png" }
]);

const detailProduct = ref({});

const productId = computed(() => route.params.id);
const getdetailProduct = async () => {
  const { data } = await procuctApi.productManagement.list.detail(productId.value);
  detailProduct.value = data;
};

// 页面加载完成后执行的逻辑
onMounted(() => {
  getdetailProduct();
  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  loadCSS(`${baseUrl}/css/product-detail.css`, "product-detail-css");
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
  loadCSS(
    "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",
    "font-awesome-css"
  );
  loadCSS(`${baseUrl}/vendor/iconfont/iconfont.css`, "iconfont-css");

  // 加载脚本
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = reject;
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap脚本
  setTimeout(() => {
    loadScript(
      `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
      "bootstrap-script"
    )
      .then(() => {
        console.log("Bootstrap脚本加载成功");
      })
      .catch(error => {
        console.error("Bootstrap脚本加载失败:", error);
      });
  }, 500);
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 清除所有脚本
  ["bootstrap-script"].forEach(id => {
    const script = document.getElementById(id);
    if (script) {
      document.body.removeChild(script);
    }
  });

  // 清除所有样式
  [
    "style-css",
    "product-detail-css",
    "bootstrap-css",
    "font-awesome-css",
    "iconfont-css"
  ].forEach(id => {
    const link = document.getElementById(id);
    if (link) {
      document.head.removeChild(link);
    }
  });
});
</script>

<style scoped>
.product-detail {
 width: 100%;
 text-align: center;   
}
/* 面包屑导航样式 */
.breadcrumb-container {
  background-color: #f5f5f5;
  padding: 15px 0;
  border-bottom: 1px solid #e1e5eb;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  color: #0052d9;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #666;
}
.product-detail-page {
  padding-top: 70px;
  background-color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.product-detail-container {
  flex: 1;
}

.product-header {
  background-color: #fff;
  padding: 15px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.breadcrumb {
  font-size: 14px;
  color: #666;
}

.breadcrumb a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s;
}

.breadcrumb a:hover {
  color: #ff6700;
}

.product-detail-container {
  padding: 30px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.product-basic-info {
  display: flex;
  align-items: flex-start;

  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 30px;
}

.product-image {
  flex: 0 0 33%;
  max-width: 50%;
  padding-right: 30px;
}

.product-info {
  margin-top: 10px;
  flex: 0 0 50%;
  max-width: 50%;
  padding-left: 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.product-name {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.product-desc {
  font-size: 16px;
  color: #666;
  /* margin-bottom: 20px; */
  line-height: 1.6;
}

.product-price {
  font-size: 24px;
  color: #ff6700;
  font-weight: bold;
  margin-bottom: 20px;
}

.product-tags {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.tag {
  background-color: #f5f5f5;
  color: #666;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
}

.product-actions {
  display: flex;
  gap: 15px;
}

.btn-buy {
  background-color: #ff6700;
  border-color: #ff6700;
}

.btn-cart {
  background-color: #fff;
  border-color: #ff6700;
  color: #ff6700;
}

/* 产品详情样式 */
.product-details {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

.product-tabs {
  display: flex;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
}

.tab {
  padding: 15px 30px;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: all 0.3s;
  position: relative;
}

.tab.active {
  color: #ff6700;
  font-weight: bold;
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #ff6700;
}

.tab-content {
  padding: 30px;
}

/* 规格参数样式 */
.specs-table {
  width: 100%;
  border-collapse: collapse;
}

.specs-table tr {
  border-bottom: 1px solid #eee;
}

.specs-table tr:last-child {
  border-bottom: none;
}

.specs-table td {
  padding: 15px 10px;
}

.spec-name {
  width: 30%;
  color: #666;
  font-weight: bold;
}

.spec-value {
  color: #333;
}

/* 产品特性样式 */
.feature-item {
  margin-bottom: 40px;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.feature-desc {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.feature-image {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* 用户评价样式 */
.review-item {
  border-bottom: 1px solid #eee;
  padding: 20px 0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.review-user {
  font-weight: bold;
  margin-right: 15px;
}

.review-date {
  color: #999;
  margin-right: 15px;
}

.review-rating {
  color: #ccc;
}

.star-active {
  color: #ff6700;
}

.review-content {
  color: #333;
  line-height: 1.6;
}

/* 相关产品样式 */
.related-products {
  margin-bottom: 30px;
}

.section-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;
  padding-left: 15px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 5px;
  height: 20px;
  width: 4px;
  background-color: #ff6700;
  border-radius: 2px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.product-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-card-image {
  width: 100%;
  height: 0;
  padding-bottom: 75%;
  position: relative;
  overflow: hidden;
}

.product-card-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-card-info {
  padding: 15px;
}

.product-card-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-card-price {
  font-size: 18px;
  color: #ff6700;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .product-basic-info {
    flex-direction: column;
  }

  .product-image,
  .product-info {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0;
  }

  .product-image {
    margin-bottom: 20px;
  }

  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .product-tabs {
    flex-direction: column;
  }

  .tab {
    padding: 10px 15px;
    text-align: center;
  }

  .product-grid {
    grid-template-columns: 1fr;
  }
}
</style>