<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <!-- 横幅 Banner -->
    <div class="joinus-banner">
      <h1 class="joinus-banner-title">企业社会责任</h1>
    </div>
    <!-- 社会责任内容区域 -->
    <div class="responsibility-grid">
      <!-- 公益慈善文本 -->
      <div class="resp-block resp-text">
        <h3>{{enterpriseInformations[0]?.title}}</h3>
        <p>{{enterpriseInformations[0]?.description}}</p>
      </div>
      
      <!-- 公益慈善图片 -->
      <div class="resp-block resp-image">
        <img :src="enterpriseInformations[0]?.cover_image" >
      </div>
      
      <!-- 校企合作图片 -->
      <div class="resp-block resp-image">
        <img :src="enterpriseInformations[1]?.cover_image" >
      </div>
      
      <!-- 校企合作文本 -->
      <div class="resp-block resp-text">
        <h3>{{enterpriseInformations[1]?.title}}</h3>
        <p>{{enterpriseInformations[1]?.description}}</p>
      </div>
      
      <!-- 党建共建文本 -->
      <div class="resp-block resp-text">
        <h3>{{enterpriseInformations[2]?.title}}</h3>
        <p>{{enterpriseInformations[2]?.description}}</p>
      </div>
      
      <!-- 党建共建图片 -->
      <div class="resp-block resp-image">
        <img :src="enterpriseInformations[2]?.cover_image" >
      </div>
    </div>
    
    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import officialApi from '@/api/master/officialWebsiteModule';
import NavBar from '@/components/website/one/NavBar.vue';
import Footer from '@/components/website/one/Footer.vue';
// 定义页面元信息
definePageMeta({
  layout: false
});

// 基础URL
const baseUrl = '/website/one/assets';

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = (path) => {
  return getAssetUrl('images', path);
};

// 服务支持 

const enterpriseInformations = ref([]);
const getEnterpriseInformations = () => {
    officialApi.getEnterpriseInformations({page:1,pageSize:3,cate_name:'服务支持'}).then(res => {
        if(res.code == 200){
            enterpriseInformations.value = res.data.items;
        }
    })
   
}

// 页面加载完成后执行的逻辑
onMounted(() => {
  getEnterpriseInformations();
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;
    
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, 'style-css');
  loadCSS(`${baseUrl}/css/responsibility.css`, 'responsibility-css');
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, 'bootstrap-css');

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      if (document.getElementById(id)) {
        resolve();
        return;
      }
      
      const script = document.createElement('script');
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = reject;
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(
        `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
        'bootstrap-script'
      )
        .then(() => {
          console.log('页面功能初始化完成');
        })
        .catch(error => {
          console.error('Bootstrap 加载失败:', error);
        });
    } catch (error) {
      console.error('脚本加载错误:', error);
    }
  }, 100);
});
</script>

<style scoped>
/* 页面样式会从外部CSS文件加载 */
.website-container {
  width: 100%;
  overflow-x: hidden;
}

/* 确保响应式布局 */
@media (max-width: 768px) {
  .joinus-banner-title {
    margin-left: 30px;
    font-size: 26px;
  }
  
  .responsibility-grid {
    padding: 20px 10px;
  }
}
</style>
