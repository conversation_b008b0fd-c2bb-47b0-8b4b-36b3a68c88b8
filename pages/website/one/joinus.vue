<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <!-- 加入我们 Banner -->
    <div class="joinus-banner">
      <h1 class="joinus-banner-title">加入我们</h1>
    </div>

    <!-- 选项卡导航 -->
    <div class="joinus-tabs">
      <div class="container">
        <ul class="nav-list">
          <li
            class="nav-item-tab"
            :class="{active: activeTab === 'social'}"
            @click="setActiveTab('social')"
          >
            <a class="nav-link" >社会招聘</a>
          </li>
          <li
            class="nav-item-tab"
            :class="{active: activeTab === 'campus'}"
            @click="setActiveTab('campus')"
          >
            <a class="nav-link">校园招聘</a>
          </li>
        </ul>
      </div>
    </div>

    <!-- 页面主要内容 -->
    <div class="container">
      <div class="tab-content">
        <!-- 社会招聘 -->
        <div class="tab-pane" :class="{'show-tab': activeTab === 'social'}" id="social">
          <div class="job-list">
            <h2 class="job-list-title">开启新的工作</h2>

            <!-- 职位项目 -->
            <div class="job-item" v-for="(job, index) in socialJobs" :key="'social-'+index">
              <div class="job-content" @click="goToDetail(job.id)">
                <h3 class="job-title">{{ job.title }}</h3>
                <div class="job-tags">
                  <span v-for="(tag, tagIndex) in job.tags" :key="'tag-'+tagIndex">{{ tag }}</span> |
                 
                  更新于<span v-time="job.updated_at"></span>
                </div>
                <p class="job-description" >{{job.position_requirement}}</p>
              </div>
              <div class="job-buttons">
                <button class="apply-btn" @click.stop="goToDetail(job.id)">申请职位</button>
                <button class="share-btn" @click.stop="shareJob(job)">分享职位</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 校园招聘 -->
        <div class="tab-pane" :class="{'show-tab': activeTab === 'campus'}" id="campus">
          <div class="job-list">
            <h2 class="job-list-title">校园招聘职位</h2>

            <!-- 校园招聘职位项目 -->
            <div class="job-item" v-for="(job, index) in campusJobs" :key="'campus-'+index">
              <div class="job-content" >
                <p class="job-description" v-html="job.position_details"></p>
              </div>
  
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
  
<script setup>
import recruitmentApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted } from "vue";
import NavBar from "@/components/website/one/NavBar.vue";
import Footer from "@/components/website/one/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: "/website/one/joinus"
});

// 基础URL
const baseUrl = "/website/one/assets";

// 当前激活的选项卡
const activeTab = ref("social");

// 设置激活的选项卡
const setActiveTab = tab => {
  activeTab.value = tab;
};

// 招聘列表数据
const allJobs = ref([]);
const socialJobs = ref([]); // 社招岗位
const campusJobs = ref([]); // 校招岗位

// 申请职位
const applyJob = job => {
  console.log("申请职位:", job.title);
};

// 跳转到详情页面
const goToDetail = (id) => {
  window.location.href = `/website/one/detail/${id}`;
};

// 分享职位
const shareJob = job => {
  console.log("分享职位:", job.title);
};

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = path => {
  return getAssetUrl("images", path);
};

// 获取招聘信息
const faceList = async() => {
  try {
    const response = await recruitmentApi.recruitManagement.faceList({page: 1, pageSize: 100});
    if (response && response.code === 200) {
      // 保存原始数据
      allJobs.value = response.data.items || [];
      
      // 根据招聘类型分类 (1=社招, 2=校招)
      socialJobs.value = allJobs.value.filter(job => job.recruitment_type === 1);
      campusJobs.value = allJobs.value.filter(job => job.recruitment_type === 2);
      
      // 处理数据字段，使其与前端渲染需要匹配
      socialJobs.value = socialJobs.value.map(job => {
        // 处理标签数据，如果是字符串则转为数组
        const tags = job.tags ? (typeof job.tags === 'string' ? job.tags.split(',') : job.tags) : [];
        
        return {
          ...job,
          tags: tags,
          updateDate: job.updated_at ? new Date(job.updated_at).toLocaleDateString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit'}) : '',
      
        };
      });
      
      campusJobs.value = campusJobs.value.map(job => {
        // 处理标签数据，如果是字符串则转为数组
        const tags = job.tags ? (typeof job.tags === 'string' ? job.tags.split(',') : job.tags) : [];
        
        return {
          ...job,
          tags: tags,
          updateDate: job.updated_at ? new Date(job.updated_at).toLocaleDateString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit'}) : '',

        };
      });
      
      console.log('社招数量:', socialJobs.value.length);
      console.log('校招数量:', campusJobs.value.length);
    } else {
      console.error('获取招聘信息失败:', response?.msg || '请求失败');
    }
  } catch (error) {
    console.error('获取招聘信息失败:', error);
  }
}

// 切换社招/校招标签
const switchTab = (tab) => {
  activeTab.value = tab;
}
// 页面加载完成后执行的逻辑
onMounted(() => {
  faceList()
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  loadCSS(`${baseUrl}/css/joinus.css`, "joinus-css");
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      if (document.getElementById(id)) {
        resolve();
        return;
      }

      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = reject;
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(
        `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
        "bootstrap-script"
      )
        .then(() => {
          console.log("页面功能初始化完成");
        })
        .catch(error => {
          console.error("Bootstrap 加载失败:", error);
        });
    } catch (error) {
      console.error("脚本加载错误:", error);
    }
  }, 100);
});
</script>

<style scoped>
/* 页面样式会从外部CSS文件加载 */
.website-container {
  background-color: #f5f7fa;
  width: 100%;
  overflow-x: hidden;
}

/* 选项卡样式 */
.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #e1e5eb;
}

.nav-item-tab {
  margin-right: 20px;
  cursor: pointer;
}

.nav-item-tab a {
  display: block;
  padding: 1rem 2rem;
  color: #333;
  text-decoration: none;
  border-bottom: 3px solid transparent;
}

.nav-item-tab.active a {
  border-bottom: 3px solid #0052d9;
  color: #0052d9;
}

/* 标签页内容样式 */
.tab-pane {
  display: none;
}

.tab-pane.show-tab {
  display: block;
}

/* 职位项目样式 */
.job-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.job-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.job-content {
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.job-content:hover {
  background-color: #f9f9f9;
}

.job-title {
  font-size: 18px;
  margin-bottom: 10px;
}

.job-title-link {
  color: #333;
  text-decoration: none;
  transition: color 0.3s ease;
}

.job-title-link:hover {
  color: #0052d9;
}

.job-tags {
  color: #666;
  margin-bottom: 10px;
}

.job-description {
  color: #333;
}

.job-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.apply-btn,
.share-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  text-align: center;
  text-decoration: none;
}

.apply-btn {
  background-color: #0052d9;
  color: white;
}

.share-btn {
  background-color: #f5f5f5;
  color: #333;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .job-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .job-buttons {
    margin-top: 15px;
    flex-direction: row;
    width: 100%;
  }

  .apply-btn,
  .share-btn {
    flex: 1;
  }

  .joinus-banner-title {
    margin-left: 30px;
    font-size: 26px;
  }

  .nav-item-tab a {
    padding: 0.7rem 1.2rem;
    font-size: 14px;
  }
}
</style>