<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <div id="mainCarousel" class="carousel slide" data-bs-ride="carousel">
      <div class="carousel-inner">
        <div class="carousel-item" :class="{ active: index === 0 }" v-for="(item,index) in basisData.banner_url" :key="index">
          <img :src="item" class="d-block w-100" alt="轮播图1" v-if="item.indexOf('mp4') == -1" />
          <video class="d-block w-100" autoplay muted playsinline v-else>
            <source :src="item" type="video/mp4" />
          </video>
          <div class="carousel-overlay"></div>
        </div>
      </div>
      <!-- 左右切换箭头 -->
      <button class="carousel-control-prev" type="button" data-bs-target="#mainCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">上一张</span>
      </button>
      <button class="carousel-control-next" type="button" data-bs-target="#mainCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">下一张</span>
      </button>
    </div>

    <!-- 核心价值观部分 -->
    <div class="core-values-section">
      <div class="container">
        <div class="row">
          <div class="col-md-12">
            <h2 class="section-title">{{companyInformations[0]?.title}}</h2>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <p class="section-description">
              {{companyInformations[0]?.description}}
            </p>
          </div>
        </div>
        <div class="row mt-5">
          <div class="col-md-12 text-center">
            <div class="image-container">
              <img :src="companyInformations[0]?.cover_image" class="core-values-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="news-box">
      <div class="news">
        <div class="news-container">
          <div class="col-md-12">
            <h2 class="section-title">核心服务</h2>
          </div>
          <div class="news-grid">
            <!-- 新闻项目1 -->
            <div class="news-item" v-for="(item,index) in caseList" :key="index">
              <a  class="news-link">
                <div class="news-image">
                  <img :src="item.cover_image" />
                </div>
                <div class="news-content">
                  <h3>{{item.title}}</h3>
                  <p>{{item.description}}</p>
                  <div class="news-date" v-time="item.created_at"></div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品中心部分 -->
    <div class="products-section">
      <div class="container">
        <div class="row">
          <div class="col-md-12">
            <h2 class="section-title">产品中心</h2>
          </div>
        </div>
        <div class="products-grid" style="cursor: pointer;" >
          <div class="product-item" v-for="(item, index) in productList" :key="index">
            <div class="product-image" @click="handleViewMore(`/website/four/productdetail/${item.id}`)">
              <img :src="item.image" :alt="item.name" class="product-img" />
            </div>
            <div class="product-name">{{item.name}}</div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-12 text-center">
            <button class="view-more-btn" @click="handleViewMore('/website/four/product')">查看更多</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 三大优势部分 -->
    <ThreeAdvantages />
    
    <!-- 新闻资讯模块 -->
    <div class="news-info-section">
      <div class="container">
        <div class="row">
          <div class="col-md-12">
            <h2 class="section-title">新闻资讯</h2>
          </div>
        </div>
        <div class="news-info-grid">
          <div class="news-info-item" @click="handleViewMore(`/website/four/newsdetail/${item.id}`)" v-for="(item, index) in newsInfoList" :key="index">
            <div class="news-info-date">
              <span class="day" v-time="item.created_at" format="dd"></span>
              <span class="month-year" v-time="item.created_at" format="yyyy-MM"></span>
            </div>
            <div class="news-info-image">
              <a-image :preview="false" :src="item.image" :alt="item.title" :width="'100%'" :height="200" fit="cover" />
            </div>
            <div class="news-info-content">
              <h3 class="news-info-title">{{ item.title }}</h3>
              <p class="news-info-desc">{{ item.summary }}</p>
              <a class="news-info-link" :href="`/website/four/newsdetail/${item.id}`">查看详情</a>
            </div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-12 text-center">
            <button class="view-more-btn"  @click="handleViewMore(`/website/four/news`)">查看更多</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 使用页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
<script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { onMounted, onBeforeUnmount, ref } from "vue";
import NavBar from "@/components/website/four/NavBar.vue";
import Footer from "@/components/website/four/Footer.vue";
import ThreeAdvantages from "@/components/website/four/ThreeAdvantages.vue";

// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = "/website/one/assets";

definePageMeta({
  layout: false,
  path: "/website/four/index"
});
// 使用useHead钩子管理头部元数据
useHead({
  title: "首页",
  meta: [
    { charset: "UTF-8" },
    { name: "viewport", content: "width=device-width, initial-scale=1.0" }
  ],
  link: [
    { rel: "stylesheet", href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css` }
    // { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' }
  ]
});

const handleViewMore = url => {
  navigateTo(url);
};

// 获取案例列表
const caseList = ref([]);
const companyInformations = ref([]);
const getCaseList = () =>{
    officialApi.caseManagement.faceList({page:1,pageSize:5}).then(res => {
        if(res.code == 200){
            caseList.value = res.data.items;
        }
    })
    officialApi.getEnterpriseInformations({page:1,pageSize:1,cate_name:'公司简介'}).then(res => {
        if(res.code == 200){
          companyInformations.value = res.data.items;
        }
    })
    
}

const basisData = ref({});
const getData = () => {
  officialApi.basisManagement.getData().then(res => {
    if (res.code == 200) {
      basisData.value = res.data;
    }
    if (res.data.banner_url) {
      basisData.value.banner_url = res.data.banner_url.split(",");
    }
    console.log(basisData.value, "xxxxx");
  });
};



// 获取产品列表
const productList = ref([]);
const getProductList = () => {
    officialApi.productManagement.list.getList({page: 1, pageSize: 8}).then(res => {
      if (res.code == 200) {
        productList.value = res.data.items;
      }
    });
 
};

// 获取新闻资讯列表
const newsInfoList = ref([]);
const getNewsInfoList = () => {
  officialApi.newsManagement.list.getList({page: 1, pageSize: 3}).then(res => {
    if (res.code == 200) {
      newsInfoList.value = res.data.items
    }
  });
};

// 查看更多新闻
const viewMoreNews = () => {
  navigateTo('/website/four/news');
};

// 公司信息
const enterpriseInforList = ref([]);
const getEnterpriseInformations = () => {
  officialApi.getEnterpriseInformations({ page: 1, pageSize: 3 }).then(res => {
    if (res.code == 200) {
      enterpriseInforList.value = res.data.items;
    }
  });
};


// 加载CSS函数
function loadCSS(href, id) {
  const link = document.createElement("link");
  link.rel = "stylesheet";
  link.href = href;
  link.id = id;
  document.head.appendChild(link);
}

// 定义加载脚本的函数
function loadScript(src, id) {
  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = src;
    script.id = id;
    script.onload = resolve;
    script.onerror = e => {
      console.error(`加载脚本失败: ${src}`, e);
      reject(e);
    };
    document.body.appendChild(script);
  });
}

// 页面加载完成后执行的逻辑
onMounted(() => {
  getData();
  getProductList();
  getNewsInfoList();
  getEnterpriseInformations();
  getCaseList();
  
  // 加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  loadCSS(`${baseUrl}/css/index.css`, "index-css");
  loadCSS(`${baseUrl}/css/home-news.css`, "home-news-css");
  
  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(
        `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
        "bootstrap-script"
      )
        .then(() => {
          // 初始化轮播图
          const mainCarousel = document.getElementById("mainCarousel");
          if (mainCarousel && window.bootstrap) {
            const carousel = new window.bootstrap.Carousel(mainCarousel, {
              interval: 5000, // 设置为5秒切换一次
              wrap: true,
              ride: "carousel", // 显式指定自动轮播
              pause: "hover" // 鼠标悬停时暂停轮播
            });
          }

          // 添加滚动特效
          window.addEventListener("scroll", function() {
            const newsTitle = document.querySelector(".home-news-title");
            if (newsTitle) {
              const rect = newsTitle.getBoundingClientRect();
              if (rect.top < window.innerHeight * 0.8) {
                newsTitle.classList.add("animate__fadeInDown");
              }
            }
          });

          console.log("页面功能初始化完成");
        })
        .catch(error => {
          console.error("Bootstrap 加载失败:", error);
        });
    } catch (error) {
      console.error("初始化页面功能失败:", error);
    }
  }, 500);
});


</script>

<style scoped lang="less">
.news-box {
  background-color: #f3f3f3 !important;
}
.core-values-section {
  padding: 60px 0;
  background-color: #ffffff;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.section-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  text-align: justify;
  margin-bottom: 20px;
}

.image-container {
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.core-values-image {
  width: 1200px;
  height: 468px;
  object-fit: cover;
  max-width: 100%;
  margin: 0 auto; /* 添加水平居中 */
  display: block; /* 确保图片作为块级元素 */
}

/* 新闻盒子样式 */
.news-box {
  padding: 50px 0;
  background-color: #fff;
}

.news {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.news-container {
  padding: 0 15px;
}

.home-news-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.news-item {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.news-item:hover {
  transform: translateY(-5px);
}

.news-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.news-image {
  height: 200px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-item:hover .news-image img {
  transform: scale(1.05);
}

.news-content {
  padding: 20px;
}

.news-content h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-content p {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-date {
  font-size: 0.8rem;
  color: #999;
}

/* 媒体查询 - 平板设备 */
@media (max-width: 992px) {
  .core-values-image {
    width: 100%;
    height: auto;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .home-news-title {
    font-size: 2.2rem;
  }

  .news-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 媒体查询 - 移动设备 */
@media (max-width: 768px) {
  .section-title {
    font-size: 1.8rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .core-values-image {
    width: 100%;
    height: auto;
  }

  .home-news-title {
    font-size: 1.8rem;
    height: auto !important;
    padding-bottom: 10px;
  }

  .news-box {
    padding: 30px 0;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .news-image {
    height: 180px;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .product-image {
    height: 180px;
  }

  .product-img {
    width: 180px;
    height: 180px;
  }
}

/* 小型移动设备 */
@media (max-width: 576px) {
  .section-title {
    font-size: 1.5rem;
  }

  .core-values-section {
    padding: 30px 0;
  }

  .home-news-title {
    font-size: 1.5rem;
  }

  .news-content h3 {
    font-size: 1.1rem;
  }

  .news-content p {
    font-size: 0.85rem;
    -webkit-line-clamp: 2;
  }

  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .product-image {
    height: 140px;
  }

  .product-img {
    width: 140px;
    height: 140px;
  }

  .product-name {
    font-size: 12px;
  }
  
  .products-section {
    padding: 30px 0;
  }
}

/* 产品中心样式 */
.products-section {
  padding: 60px 0;
  background-color: #fff;
}

.products-grid {
  
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin: 0 auto;
  max-width: 1200px;
}

.product-item {
  border: 1px solid #F2F2F2;
  text-align: center;
  padding: 10px;
  transition: transform 0.3s ease;
}

.product-item:hover {
  transform: translateY(-5px);
}

.product-image {
  width: 100%;
  height: 280px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-img {
  width: 280px;
  height: 280px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-item:hover .product-img {
  transform: scale(1.05);
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-top: 10px;
  text-align: center;
}

.view-more-btn {
  background-color: #F44217;
  color: white;
  width: 190px;
  height: 60px;
  padding: 10px 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 600;
}

// .view-more-btn:hover {
//   background-color: #3498db;
//   color: #fff;
// }

/* 媒体查询 - 平板设备 */
@media (max-width: 992px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .product-image {
    height: 220px;
  }

  .product-img {
    width: 220px;
    height: 220px;
  }
}

/* 媒体查询 - 中小型设备 */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .product-image {
    height: 180px;
  }

  .product-img {
    width: 180px;
    height: 180px;
  }

  .products-section {
    padding: 40px 0;
  }
}
/* 新闻资讯模块样式 */
.news-info-section {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.news-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.news-info-item {
  cursor: pointer;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.news-info-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.news-info-date {
  position: absolute;
  left: 15px;
  background-color: #F44217;
  color: #fff;
  padding: 10px;
  text-align: center;
  z-index: 2;
}

.news-info-date .day {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.news-info-date .month-year {
  display: block;
  font-size: 0.8rem;
}

.news-info-image {
  height: 200px;
  overflow: hidden;
  position: relative;
}

.news-info-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.news-info-item:hover .news-info-image :deep(img) {
  transform: scale(1.1);
}

.news-info-content {
  padding: 20px;
}

.news-info-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 3rem;
}

.news-info-desc {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 4rem;
}

.news-info-link {
  display: inline-block;
  padding: 8px 20px;
  border: 1px solid #ccc;
  border-radius: 20px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  background-color: #fff;
}

.news-info-link:hover {
  background-color: #ff6b00;
  color: #fff;
  border-color: #ff6b00;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .news-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .news-info-section {
    padding: 60px 0;
  }
  
  .news-info-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .news-info-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .news-info-section {
    padding: 40px 0;
  }
  
  .news-info-date {
    padding: 8px;
  }
  
  .news-info-date .day {
    font-size: 1.2rem;
  }
  
  .news-info-date .month-year {
    font-size: 0.7rem;
  }
  
  .news-info-image {
    height: 180px;
  }
  
  .news-info-content {
    padding: 15px;
  }
  
  .news-info-title {
    font-size: 1rem;
    margin-bottom: 8px;
  }
  
  .news-info-desc {
    font-size: 0.85rem;
    margin-bottom: 10px;
  }
}
</style>