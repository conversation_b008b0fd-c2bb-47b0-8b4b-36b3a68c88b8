<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl2" />

    <!-- 产品分类和列表区域 -->
    <div class="product-content">
      <div class="container">
        <div class="product-header-row">
          <!-- 产品中心标题区域 -->
          <div class="product-header">
            <h1 class="product-title">产品中心</h1>
            <p class="product-subtitle">Product Center</p>
          </div>

          <!-- <icon-caret-right /> -->
          <div class="categories-container">
            <div class="scroll-btn prev" @click="scrollCategories('left')">
              <icon-caret-left />
            </div>
            <div class="product-categories" ref="categoriesContainer">
              <div
                v-for="(category, index) in categoryList"
                :key="index"
                :class="['category-item', { active: currentCategoryIndex === index }]"
                @click="switchCategory(index)"
              >{{ category.name }}</div>
            </div>
            <div class="scroll-btn next" @click="scrollCategories('right')">
              <icon-caret-right />
            </div>
          </div>
        </div>

        <!-- 产品列表 -->
        <div class="product-list">
          <div class="row">
            <div
              class="col-md-3 col-sm-6 col-6 mb-4"
              v-for="(product, index) in displayedProducts"
              :key="index"
            >
              <div class="product-card" @click="goToProductDetail(product.id)">
                <div class="product-image">
                  <img :preview="false" :src="product.image" alt="产品图片" fit="cover" />
                </div>
                <div class="product-info">
                  <h3 class="product-name">{{ product.name }}</h3>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页导航 -->
        <div class="pagination-container">
          <a-pagination
            :total="filteredProducts.length"
            :current="currentPage"
            :page-size="itemsPerPage"
            @change="goToPage"
            class="red-pagination"
          />
        </div>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl2" />
  </div>
</template>

<script setup>
import procuctApi from "@/api/master/officialWebsiteModule";
import { ref, computed, onMounted } from "vue";
import NavBar from "@/components/website/four/NavBar.vue";
import Footer from "@/components/website/four/Footer.vue";

definePageMeta({
  layout: false,
  path: "/website/four/product"
});

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";

// 当前选中的分类索引
const currentCategoryIndex = ref(0);

// 产品数据
const products = ref([]);

// 分页相关
const currentPage = ref(1);
const itemsPerPage = 8; // 每页显示的产品数量

// 产品列表
const filteredProducts = computed(() => {
  // 直接返回产品列表，因为我们已经通过API获取了特定分类的产品
  return products.value;
});

const categoryList = ref([]);
// 产品查询参数
const productParams = ref({
  page: 1,
  pageSize: 100,
  category_id: "" // 修改为后端实际接收的参数名称
});

// 获取产品分类
const getProductList = async () => {
  try {
    const response = await procuctApi.productManagement.getList({
      page: 1,
      pageSize: 100
    });
    if (response.code === 200) {
      categoryList.value = response.data.items;
      // 如果有分类数据，默认选择第一个分类
      if (categoryList.value.length > 0) {
        currentCategoryIndex.value = 0;
        productParams.value.category_id = categoryList.value[0].id;
        // 获取第一个分类的产品列表
        getProductsByCategory();
      }
    }
  } catch (error) {
    console.error("获取产品分类失败:", error);
  }
};

// 获取产品列表
const getProductsByCategory = async () => {
  try {
    console.log("发送请求参数：", productParams.value);
    // 确保传递了分类ID参数
    const params = {
      ...productParams.value
    };
    const response = await procuctApi.productManagement.list.getList(params);
    console.log("获取产品列表响应：", response);
    if (response.code === 200) {
      products.value = response.data.items || [];
      console.log("更新后的产品列表：", products.value);
    }
  } catch (error) {
    console.error("获取产品列表失败:", error);
  }
};

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(filteredProducts.value.length / itemsPerPage);
});

// 当前页显示的产品
const displayedProducts = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return filteredProducts.value.slice(startIndex, endIndex);
});

// 显示的页码
const displayedPages = computed(() => {
  const pages = [];
  const maxVisiblePages = 5; // 最多显示的页码数量

  if (totalPages.value <= maxVisiblePages) {
    // 总页数小于等于最大显示页码数，显示所有页码
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    // 总页数大于最大显示页码数，显示部分页码
    let startPage = Math.max(
      1,
      currentPage.value - Math.floor(maxVisiblePages / 2)
    );
    let endPage = startPage + maxVisiblePages - 1;

    if (endPage > totalPages.value) {
      endPage = totalPages.value;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
  }

  return pages;
});

// 切换产品分类
function switchCategory(index) {
  currentCategoryIndex.value = index;
  currentPage.value = 1; // 切换分类后重置页码

  // 设置当前选中分类ID
  if (categoryList.value && categoryList.value.length > index) {
    console.log(
      "切换分类：",
      categoryList.value[index].name,
      "分类ID：",
      categoryList.value[index].id
    );
    productParams.value.category_id = categoryList.value[index].id;
    // 根据分类ID获取产品列表
    getProductsByCategory();
  }
}

// 跳转到指定页
function goToPage(page) {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
}

// 分类导航容器引用
const categoriesContainer = ref(null);

// 滚动分类导航
function scrollCategories(direction) {
  if (!categoriesContainer.value) return;

  const container = categoriesContainer.value;
  const scrollAmount = 200; // 每次滚动的距离

  if (direction === "left") {
    container.scrollLeft -= scrollAmount;
  } else {
    container.scrollLeft += scrollAmount;
  }
}

const goToProductDetail = id => {
  navigateTo(`/website/two/productdetail/${id}`);
};

// 页面加载完成后执行的逻辑
onMounted(() => {
  // 获取产品分类数据
  getProductList();
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
});

// 添加页面元信息
useHead({
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});
</script>
  
<style scoped>
  /* 红色背景的分页样式 */
/* .red-pagination :deep(.arco-pagination) {
  background-color: red;
} */

.red-pagination :deep(.arco-pagination-item-active)
 {
  background-color: #F44217;
  color: white;
}
/* 产品中心标题区域样式 */
.product-content {
  padding-top: 100px;
}

.product-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.product-header {
  text-align: left;
}

.product-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.product-subtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 0;
}

/* 产品分类导航样式 */
.categories-container {
  display: flex;
  align-items: center;
  max-width: 70%;
}

.product-categories {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  white-space: nowrap;
  padding: 5px 0;
}

/* 隐藏滚动条 */
.product-categories::-webkit-scrollbar {
  display: none;
}

.category-item {
  padding: 10px 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.category-item.active {
  background: #FFE5DE;
  color: #F44217 ;
}

/* 滚动按钮样式 */
.scroll-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 50%;
  cursor: pointer;
  z-index: 1;
  transition: all 0.3s ease;
  margin: 0 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.scroll-btn:hover {
  background-color: #f0f0f0;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.scroll-btn i {
  font-size: 14px;
  color: #666;
}

/* 产品列表样式 */
.product-list {
  margin-bottom: 40px;
}

.product-card {
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.product-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.product-image {
  border: 1px solid #eee;
  height: 0;
  padding-bottom: 75%; /* 4:3的比例 */
  position: relative;
  overflow: hidden;
}

.product-image :deep(img) {
  width: 100%;
  height: 100%;
  fit: cover;
  transition: transform 0.3s ease;
  position: absolute;
  top: 0;
  left: 0;
}

.product-image :deep(.arco-image) {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card:hover .product-image :deep(img) {
  transform: scale(1.05);
}

.product-info {
  padding: 15px;
  text-align: center;
}

.product-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0;
  color: #333;
}

/* 分页导航样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  margin-bottom: 60px;
}

.pagination {
  display: flex;
  gap: 5px;
}

.page-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-item.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.page-item.disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

.page-item:not(.disabled):not(.active):hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-header-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .product-header {
    margin-bottom: 20px;
  }

  .categories-container {
    max-width: 100%;
    width: 100%;
  }

  .product-categories {
    /* 保持水平滚动 */
    flex-direction: row;
    justify-content: flex-start;
    width: 100%;
    overflow-x: auto;
  }

  .category-item {
    white-space: nowrap;
    flex-shrink: 0;
  }
}
</style>
  