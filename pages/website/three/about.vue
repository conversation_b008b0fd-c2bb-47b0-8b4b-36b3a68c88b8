<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/three/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/three/about">关于我们</a>
            </li>
          </ol>
        </nav>
      </div>
    </div>
    <!-- 页面主要内容 -->
    <main class="main-content">
 
       <!-- 关于我们 -->
       <section class="about-section py-12">
        <div class="container mx-auto px-4">
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-3/5 mb-8 md:mb-0 md:pr-12 about-content" @mouseenter="aboutHovered = true" @mouseleave="aboutHovered = false">
              <h2 class="text-3xl font-bold mb-6" :class="{'text-red-600': aboutHovered}">关于我们</h2>
              <p
                class="mb-4 about-description"
                :class="{'text-gray-700': aboutHovered}"
              >{{companyInformations[0] ? companyInformations[0].description : ''}}</p>
              <a
                href="/website/three/about"
                class="inline-block bg-red-600 text-white py-2 px-6 hover:bg-red-700 transition duration-300 text-decoration-none about-btn"
                :class="{'pulse-effect': aboutHovered}"
              >了解更多</a>
            </div>
            <div class="md:w-2/5 about-image-container" @mouseenter="aboutImageHovered = true" @mouseleave="aboutImageHovered = false">
              <img src="/website/three/assets/images/3.png" alt="关于我们" class="w-full about-image" :class="{'scale-effect': aboutImageHovered}" />
            </div>
          </div>
        </div>
      </section>
      <!-- 企业优势 -->
      <section
        class="company-advantages py-12"
        :style="{ backgroundImage: 'url(/website/three/assets/images/server_bg.png)' }">
        <div class="container mx-auto px-4">
          <h2 class="text-center text-3xl font-bold mb-12 " style="color: #fff;" >企业优势</h2>

          <div class="advantages-wrapper relative">
            <!-- 优势项目容器 -->
            <div class="advantages-container flex flex-wrap justify-between items-stretch w-full">
              <!-- 优势1 -->
              <div class="advantage-item md:w-1/4 w-full px-3 flex flex-col items-center mb-8 md:mb-0" @mouseenter="advantageHovered[0] = true" @mouseleave="advantageHovered[0] = false">
                <div class="md:hidden advantage-content text-left mb-4" v-if="isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[0]}">01</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[0]}">{{enterpriseInformations2[0]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[0]?.description}}</p>
                </div>
                <div class="advantage-image-wrapper mb-6 flex justify-center">
                  <img
                    class="advantage-image"
                    :class="{'advantage-image-hover': advantageHovered[0]}"
                    :src="enterpriseInformations2[0] && enterpriseInformations2[0].cover_image ? enterpriseInformations2[0].cover_image : '/website/three/assets/images/1.png'"
                  />
                </div>
                <div class="hidden md:block advantage-content text-left" v-if="!isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[0]}">01</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[0]}">{{enterpriseInformations2[0]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[0]?.description}}</p>
                </div>
              </div>

              <!-- 优势2 -->
              <div class="advantage-item md:w-1/4 w-full px-3 flex flex-col items-center mb-8 md:mb-0" @mouseenter="advantageHovered[1] = true" @mouseleave="advantageHovered[1] = false">
                <div class="md:hidden advantage-content text-left mb-4" v-if="!isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[1]}">02</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[1]}">{{enterpriseInformations2[1]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[1]?.description}}</p>
                </div>
                <div class="hidden md:flex advantage-content mb-4" v-if="isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[1]}">02</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[1]}">{{enterpriseInformations2[1]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[1]?.description}}</p>
                </div>
                <div class="advantage-image-wrapper mb-6 flex justify-center">
                  <img
                    class="advantage-image"
                    :class="{'advantage-image-hover': advantageHovered[1]}"
                    :src="enterpriseInformations2[1] && enterpriseInformations2[1].cover_image ? enterpriseInformations2[1].cover_image : '/website/three/assets/images/2.png'"
                  />
                </div>
              </div>

              <!-- 优势3 -->
              <div
                class="advantage-item md:w-1/4 w-full px-3 flex flex-col items-center mb-8 md:mb-0" @mouseenter="advantageHovered[2] = true" @mouseleave="advantageHovered[2] = false">
                <div class="md:hidden advantage-content text-left mb-4" v-if="isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[2]}">03</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[2]}">{{enterpriseInformations2[2]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[2]?.description}}</p>
                </div>

                <div class="advantage-image-wrapper mb-6 flex justify-center">
                  <img
                    class="advantage-image"
                    :class="{'advantage-image-hover': advantageHovered[2]}"
                    :src="enterpriseInformations2[2] && enterpriseInformations2[2].cover_image ? enterpriseInformations2[2].cover_image : '/website/three/assets/images/1.png'"
                  />
                </div>
                <div class="hidden md:block advantage-content text-left" v-if="!isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[2]}">03</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[2]}">{{enterpriseInformations2[2]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[2]?.description}}</p>
                </div>
              </div>

              <!-- 优势4 -->
              <div class="advantage-item md:w-1/4 w-full px-3 flex flex-col items-center mb-8 md:mb-0" @mouseenter="advantageHovered[3] = true" @mouseleave="advantageHovered[3] = false">
                <div class="md:hidden advantage-content text-left mb-4" v-if="!isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[3]}">04</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[3]}">{{enterpriseInformations2[3]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[3]?.description}}</p>
                </div>
                <div class="hidden md:flex advantage-content text-left mb-4" v-if="isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[3]}">04</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[3]}">{{enterpriseInformations2[3]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[3]?.description}}</p>
                </div>
                <div class="advantage-image-wrapper mb-6 flex justify-center">
                  <img
                    class="advantage-image"
                    :class="{'advantage-image-hover': advantageHovered[3]}"
                    :src="enterpriseInformations2[3] && enterpriseInformations2[3].cover_image ? enterpriseInformations2[3].cover_image : '/website/three/assets/images/2.png'"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 使用页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
  
  
<script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { onMounted, onBeforeUnmount, ref, onUnmounted, reactive } from "vue";
import NavBar from "@/components/website/three/NavBar.vue";
import Footer from "@/components/website/three/Footer.vue";

// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = "/website/one/assets";

definePageMeta({
  layout: false,
  path: "/website/three/about"
});

// 移动端判断
const isMobile = ref(false);

// 鼠标悬停状态变量
const aboutHovered = ref(false);
const aboutImageHovered = ref(false);
const advantageHovered = ref([false, false, false, false]);
// 使用useHead钩子管理头部元数据
useHead({
  title: "关于我们",
  meta: [
    { charset: "UTF-8" },
    { name: "viewport", content: "width=device-width, initial-scale=1.0" }
  ],
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});

// 检测屏幕大小的函数
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768;
};

// 服务支持 企业优势 公司简介
const enterpriseInformations2 = ref([]);
const companyInformations = ref([]);
const getEnterpriseInformations = () => {
  officialApi.getEnterpriseInformations({page:1,pageSize:1,cate_name:'公司简介'}).then(res => {
        if(res.code == 200){
          companyInformations.value = res.data.items;
        }
    })

    officialApi.getEnterpriseInformations({page:1,pageSize:4,}).then(res => {
        if(res.code == 200){
          enterpriseInformations2.value = res.data.items;
        }
    })
}
// 页面加载完成后执行的逻辑
onMounted(() => {
  getEnterpriseInformations()
  // 初始判断是否为移动端
  checkScreenSize();

  // 添加窗口大小变化事件监听
  window.addEventListener("resize", checkScreenSize);

  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = e => {
        console.error(`加载脚本失败: ${src}`, e);
        reject(e);
      };
      document.body.appendChild(script);
    });
  }

});


</script>
  
<style scoped lang="less">
/* 关于我们动画效果 */
.about-content {
  transition: all 0.3s ease;
}

.about-description {
  transition: color 0.3s ease;
}

.about-btn {
  transition: all 0.3s ease;
}

.pulse-effect {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.about-image {
  transition: transform 0.5s ease;
}

.scale-effect {
  transform: scale(1.05);
}

/* 企业优势动画效果 */
.advantage-image {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-image-hover {
  transform: scale(1.05);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.advantage-number, .advantage-title {
  transition: color 0.3s ease;
}

  /* 面包屑导航样式 */
.breadcrumb-container {
  margin-top: -24px;
  background-color: #F8F8F8  ;
  padding: 15px 0;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  text-decoration: none;
  color: #666;
}

.breadcrumb-item.active {
  color: #666;
}


.news-list-container {
  padding: 50px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.news-item {
  width: 100%;
  display: block;
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.news-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-item-content {
  background-color: #F8F8F8;
  display: flex;
  align-items: center;
  padding: 35px;
}

.news-date {
  align-items: baseline;
  margin-bottom: 10px;
}

.news-date-day {
  font-weight: 600;
  font-size: 30px;
  color: #a5a5a5;
  line-height: 1;
  margin-right: 5px;
}

.news-date-year {
  font-weight: 400;
  font-size: 16px;
  color: #a5a5a5;
  margin-top: 4px;
  margin-bottom: 5px;
}

.news-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
}

.news-item-image {
  width: 240px;
  height: 140px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.news-item-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-item-image :deep(.arco-image) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.news-item:hover .news-item-image :deep(img) {
  transform: scale(1.05);
}

.news-item-title {
  font-weight: 600;
  font-size: 24px;
  color: #262626;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.news-item:hover .news-item-title {
  color: #E70012 ;
}

.news-item-desc {
  font-weight: 400;
  font-size: 16px;
  color: #7f7f7f;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}


.service-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.25rem;
  
  @media (min-width: 48rem) {
    flex-direction: row;
    
    &:nth-child(even) {
      flex-direction: row-reverse;
    }
  }
  
  .service-content {
    position: relative;
    z-index: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px;
    
    @media (min-width: 48rem) {
      padding: 32px;
      height: 21.125rem;
    }
    
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
    }
    
    &.service-content-1::before {
      background-color: #f3f4f6; /* bg-gray-100 */
    }
    
    &.service-content-2::before {
      background-color: #333333;
    }
    
    &.service-content-3::before {
      background-color: #E70012;
    }
  }
}

.responsive-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: block;
  
  @media (min-width: 48rem) {
    
    width: 40.625rem;
    height: 21.125rem;
    object-fit: cover;
   
  }
}
.company-advantages {
  background-image: url("/website/three/assets/images/server_bg.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
}

.advantage-image-wrapper {
  width: 241px;
  height: 187px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.advantage-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: relative;
}

.container-title {
  font-weight: 600;
  font-size: 2.75rem;
  color: #ffffff;
  line-height: 3.875rem;
  text-align: center;
  margin-bottom: 2.5rem;

  @media (max-width: 48rem) {
    font-size: 2rem;
    line-height: 2.75rem;
    margin-bottom: 1.875rem;
  }
}

.advantages-container {
  @media (max-width: 48rem) {
    flex-direction: column;
  }
}

.advantage-content {
  width: 100%;
  height: 14.375rem; /* 固定高度，仅在桌面端生效 */
  display: flex;
  flex-direction: column;
  text-align: left;

  @media (max-width: 48rem) {
    height: auto;
    min-height: auto;
    margin-bottom: 0;
  }

  .advantage-number {
    font-weight: 600;
    font-size: 3.125rem;
    color: #ffffff;

    @media (max-width: 48rem) {
      font-size: 2.25rem;
    }
  }

  .advantage-title {
    font-weight: 600;
    font-size: 1.625rem;
    color: #ffffff;
    margin-bottom: .625rem;

    @media (max-width: 48rem) {
      font-size: 1.375rem;
    }
  }

  .advantage-content {
    font-weight: 400;
    font-size: 1rem;
    color: #ffffff;

    @media (max-width: 48rem) {
      font-size: .875rem;
    }
  }
}

.carousel-overlay {
  background-color: none !important;
}
.website-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}


.hero-section {
  background-image: url("/placeholder/hero-bg.jpg");
  background-size: cover;
  background-position: center;
  height: 31.25rem;
  position: relative;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background-color: rgba(0, 0, 0, 0.5); */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保 Bootstrap 和 Tailwind 的兼容性 */
.container {
  width: 100%;
  padding-right: .9375rem;
  padding-left: .9375rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 36rem) {
  .container {
    max-width: 33.75rem;
  }
}

@media (min-width: 48rem) {
  .container {
    max-width: 45rem;
  }
}

@media (min-width: 62rem) {
  .container {
    max-width: 60rem;
  }
}

@media (min-width: 75rem) {
  .container {
    max-width: 76.25rem;
  }
}

/* 成功案例样式 */
.success-cases {
  background-color: #f8f9fa;
}

.cases-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  
  @media (max-width: 48rem) {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  @media (max-width: 36rem) {
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }
}

.case-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 .25rem .375rem rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  line-height: 0;
  
  &:hover {
    transform: translateY(-0.3125rem);
    
    .case-overlay {
      opacity: 1;
      
      .case-content {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
  
  .case-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
    line-height: 0;
    vertical-align: middle;
  }
  
  &:hover .case-image {
    transform: scale(1.05);
  }
}

.case-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 24px;
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .case-content {
    transform: translateY(1.25rem);
    transition: all 0.3s ease;
    opacity: 0;
  }
  
  h3 {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: bold;
    color: #ffffff;
    
    @media (max-width: 48rem) {
      font-size: 16px;
    }
  }
  
  p {
    color: #ffffff;
    font-size: 14px;
    margin: 0;
    
    @media (max-width: 48rem) {
      font-size: 12px;
    }
    
    @media (max-width: 36rem) {
      display: none;
    }
  }
}

/* 加入我们样式 */
.join-us {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  color: #ffffff;
}

.join-us-container {
  display: flex;
  flex-direction: column;
  
  @media (min-width: 48rem) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.join-us-content {
  flex: 1;
  margin-bottom: 2rem;
  
  @media (min-width: 48rem) {
    margin-bottom: 0;
    margin-right: 3rem;
  }
}

.join-us-title {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
  
  @media (max-width: 48rem) {
    font-size: 2rem;
  }
}

.join-us-desc {
  font-size: 1rem;
  line-height: 1.6;
  margin-top: 1rem;
  max-width: 30rem;
}

.join-us-form {
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.submit-btn {
  width: 100%;
  height: 3rem;
  font-size: 1rem;
  font-weight: 500;
  background-color: #E70012;
  border-color: #E70012;
  
  &:hover {
    background-color: darken(#E70012, 10%);
    border-color: darken(#E70012, 10%);
  }
}
</style>