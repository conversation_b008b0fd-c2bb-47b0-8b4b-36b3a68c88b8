<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/three/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/three/news">新闻中心</a>
            </li>
          </ol>
        </nav>
      </div>
    </div>
    <!-- 页面主要内容 -->
    <main class="main-content">
      <!-- 新闻咨询 -->
      <div class="news-list-container">
        <h2 class="text-left text-3xl font-bold mb-12 ml-2" >新闻中心</h2>
        <div class="container">
          <!-- 新闻项目 -->
          <a class="news-item" v-for="(item, index) in newsList" :key="index">
            <div class="news-item-content" @click="goToProductDetail(item.id)">
              <div class="news-item-info">
                <div class="news-date">
                  <div class="news-date-day" v-time="item.created_at" format="yyyy"></div>
                  <div class="news-date-year" v-time="item.created_at" format="MM-dd"></div>
                </div>
                <h3 class="news-item-title">{{ item.title }}</h3>
                <p class="news-item-desc">{{ item.summary }}</p>
              </div>
              <div class="news-item-image">
                <a-image  :src="item.image" :alt="item.title" fit="cover" :preview="false" />
              </div>
            </div>
          </a>
        </div>
      </div>
    </main>

    <!-- 使用页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
  
  
<script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { onMounted, onBeforeUnmount, ref, onUnmounted, reactive } from "vue";
import NavBar from "@/components/website/three/NavBar.vue";
import Footer from "@/components/website/three/Footer.vue";

// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = "/website/one/assets";

definePageMeta({
  layout: false,
  path: "/website/three/news"
});

// 使用useHead钩子管理头部元数据
useHead({
  title: "广东八灵科技发展有限公司",
  meta: [
    { charset: "UTF-8" },
    { name: "viewport", content: "width=device-width, initial-scale=1.0" }
  ],
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});
//获取新闻列表
const newsList = ref([]);
const getNewsList = () => {
    officialApi.newsManagement.list.getList({page:1,pageSize:3}).then(res => {
        if(res.code == 200){
          newsList.value = res.data.items;
        }
    })
}

const goToProductDetail = (id) => {
  navigateTo(`/website/three/newsdetail/${id}`);
};
// 页面加载完成后执行的逻辑
onMounted(() => {
  getNewsList()
  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = e => {
        console.error(`加载脚本失败: ${src}`, e);
        reject(e);
      };
      document.body.appendChild(script);
    });
  }
});

</script>
  
<style scoped lang="less">
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: white;
  padding-top: 80px; /* 为固定导航栏留出空间 */
}
.news-list-container {
  padding: 50px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.news-item {
  width: 100%;
  display: block;
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.news-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-item-content {
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  padding: 35px;
}

.news-date {
  align-items: baseline;
  margin-bottom: 10px;
}

.news-date-day {
  font-weight: 600;
  font-size: 30px;
  color: #a5a5a5;
  line-height: 1;
  margin-right: 5px;
}

.news-date-year {
  font-weight: 400;
  font-size: 16px;
  color: #a5a5a5;
  margin-top: 4px;
  margin-bottom: 5px;
}

.news-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
}

.news-item-image {
  width: 240px;
  height: 140px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.news-item-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-item-image :deep(.arco-image) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.news-item:hover .news-item-image :deep(img) {
  transform: scale(1.05);
}

.news-item-title {
  font-weight: 600;
  font-size: 24px;
  color: #262626;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.news-item:hover .news-item-title {
  color: #e70012;
}

.news-item-desc {
  font-weight: 400;
  font-size: 16px;
  color: #7f7f7f;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}
/* 面包屑导航样式 */
.breadcrumb-container {
  margin-top: -24px;
  background-color: #F8F8F8  ;
  padding: 15px 0;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  text-decoration: none;
  color: #666;
}

.breadcrumb-item.active {
  color: #666;
}
</style>