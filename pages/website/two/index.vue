<template>
     <!-- 使用导航栏组件 -->
     <NavBar :base-url="baseUrl2" logo-url="basisData.logo_url" />
    <div class="banner-section" style="font-size: 0;">
      <div id="mainCarousel" class="carousel slide" data-bs-ride="carousel">
        <div class="carousel-inner">
          <div class="carousel-item" v-for="(item, index) in basisData.banner_url" :key="index" :class="{ active: index === 0 }">
            <!-- 添加调试信息 -->
            <img :src="item" class="d-block w-100" alt="轮播图" v-if="item.indexOf('mp4') === -1"/>
            <video class="d-block w-100" autoplay muted playsinline v-else>
              <source :src="item" type="video/mp4">
            </video>
            <div class="carousel-overlay"></div>
          </div>
        </div>
        <!-- 左右切换箭头 -->
        <button class="carousel-control-prev" type="button" data-bs-target="#mainCarousel" data-bs-slide="prev">
          <span class="carousel-control-prev-icon" aria-hidden="true"></span>
          <span class="visually-hidden">上一张</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#mainCarousel" data-bs-slide="next">
          <span class="carousel-control-next-icon" aria-hidden="true"></span>
          <span class="visually-hidden">下一张</span>
        </button>
      </div>
    </div>
    <!-- 解决方案模块 -->
    <div class="solution-section">
      <div class="container">
        <div class="section-title">
          <h2>针对企业运营的数字化解决方案</h2>
          <p>秉承“深耕数字科技，助力产业升级”的企业使命，坚持助力客户成功的经营理念，累计为7000多家企业运营提供了数字化解决方案和产品。</p>
        </div>
        
        <div class="solution-content">
          <div class="solution-menu">
            <div 
              v-for="(item, index) in caseList" 
              :key="index"
              :class="['solution-menu-item', { active: currentSolutionIndex === index }]"
              @click="switchSolution(index)"
            >
              {{ item.title }}
            </div>
          </div>
          
          <div class="solution-display" @click="navigateTo(`/website/two/plans/${caseList[currentSolutionIndex]?.id}`)">
      
            <a-image :preview="false" :src="caseList[currentSolutionIndex]?.cover_image" alt="解决方案图片" class="solution-image" />
            <div class="solution-description">
              <p>{{ caseList[currentSolutionIndex]?.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数智化转型产品模块 -->
    <div class="digital-products-section">
      <div class="container">
        <div class="section-title">
          <h2>数智化转型产品</h2>
          <p>未来客户成功的核心要素，取力于地产业产生变革的数字化领域赋能的伴侣</p>
        </div>
        
        <div class="products-grid">
          <div class="product-card" v-for="(item, index) in enterpriseInformations" :key="index">

            <div class="product-image">
              <img :src="item.cover_image" :alt="item.title" />
            </div>
            <h3>{{item.title}}</h3>
            <p>{{item.description}}</p>
          </div>

        </div>
      </div>
    </div>

    <!-- 企业案例模块 -->
    <div class="case-studies-section">
      <div class="container">
        <div class="section-title">
          <h2>超7000家企业运营企业案例</h2>
          <p>深耕数字科技，助力产业升级，成为地产业生态链数字化领域赋能的伴侣</p>
        </div>
        
        <div class="case-studies-grid" >
          <div class="case-study-card" v-for="(item, index) in caseList" :key="index">
            <div class="case-image" @click="navigateTo(`/website/two/plans/${item.id}`)">
              <a-image :preview="false" :src="item.cover_image" alt="数字营销" fit="cover" />
            </div>
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
            <a class="learn-more-btn" @click="navigateTo(`/website/two/plans/${item.id}`)">了解更多</a>
          </div>

        </div>
      </div>
    </div>

    <!-- 新闻资讯模块 -->
    <div class="news-section">
      <div class="container">
        <div class="news-title-wrapper">
          <div class="blue-square"></div>
          <h2>新闻资讯</h2>
          <div class="blue-square"></div>
        </div>
        <p class="news-subtitle">获取最新资讯 洞察行业动态</p>
        
        <div class="news-content">
          <div class="news-image">
            <a-image src="/website/two/assets/images/news.png" alt="新闻资讯" fit="cover" :preview="false"/>
          </div>
          
          <div class="news-list">
            <div class="news-item" v-for="(item, index) in newsList" :key="index" @click="navigateTo(`/website/two/newsdetail/${item.id}`)">
              <div class="news-title">{{ item.title }}</div>
              <div class="news-date" v-time="item.created_at"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <Footer :base-url="baseUrl2" :logo-url="basisData.logo_url" :wx-url="basisData.wechat_qrcode"/>
</template>
<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { onMounted, onBeforeUnmount, ref, computed } from 'vue';
import NavBar from '@/components/website/two/NavBar.vue';
import Footer from '@/components/website/two/Footer.vue';
definePageMeta({
    layout: false,
    path: '/website/two/index'
})
// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = '/website/one/assets';
const baseUrl2 = '/website/two/assets';

// 当前选中的解决方案索引
const currentSolutionIndex = ref(0);

// 获取首页新闻
const newsList = ref([]);
const newlist = () =>{
    officialApi.newsManagement.list.getList({page:1,pageSize:5}).then(res => {
        if(res.code == 200){
            newsList.value = res.data.items;
        }
    })
}

// 获取供应链数据
const basisData = ref({});
const getData = () =>{
    officialApi.basisManagement.getData().then(res => {
        if(res.code == 200){
            basisData.value = res.data;
        }
        if(res.data.banner_url){
            basisData.value.banner_url = res.data.banner_url.split(',');
        }
        console.log(basisData.value,'xxxxx');
    })
}

// 获取案例列表
const caseList = ref([]);
const getCaseList = () =>{
    officialApi.caseManagement.faceList({page:1,pageSize:4}).then(res => {
        if(res.code == 200){
            caseList.value = res.data.items;
        }
    })
}

//企业信息
const enterpriseInformations = ref([]);
const enterpriseInforList= ref([]);
const getEnterpriseInformations = () =>{
    officialApi.getEnterpriseInformations({page:1,pageSize:3}).then(res => {
        if(res.code == 200){
            enterpriseInforList.value = res.data.items;
        }
    })
    officialApi.getEnterpriseInformations({page:1,pageSize:6,cate_name:'服务支持'}).then(res => {
        if(res.code == 200){
            enterpriseInformations.value = res.data.items;
        }
    })
}

// 当前选中的解决方案数据
// const currentSolution = computed(() => {
//   return solutionItems.value[currentSolutionIndex.value];
// });

// 切换解决方案
const switchSolution = (index) => {
  currentSolutionIndex.value = index;
};
useHead({
  title: '首页',
  meta: [
    { charset: 'UTF-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' }
  ],
  link: [
    { rel: 'stylesheet', href: `/website/one/assets/vendor/bootstrap/bootstrap.min.css` },
    { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' }
  ]
});


// 页面加载完成后执行的逻辑
onMounted(() => {
  getData()
  newlist()
  getCaseList()
  getEnterpriseInformations()
  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, 'style-css');
  loadCSS(`${baseUrl}/css/index.css`, 'index-css');
  loadCSS(`${baseUrl}/css/home-news.css`, 'home-news-css');
  loadCSS(`/website/one/assets/vendor/bootstrap/bootstrap.min.css`, 'bootstrap-css');

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = (e) => {
        console.error(`加载脚本失败: ${src}`, e);
        reject(e);
      };
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(`/website/one/assets/vendor/bootstrap/bootstrap.bundle.min.js`, 'bootstrap-script')
        .then(() => {
          // 初始化轮播图
          const mainCarousel = document.getElementById('mainCarousel');
          if (mainCarousel && window.bootstrap) {
            const carousel = new window.bootstrap.Carousel(mainCarousel, {
              interval: 5000, // 设置为5秒切换一次
              wrap: true,
              ride: 'carousel', // 显式指定自动轮播
              pause: 'hover' // 鼠标悬停时暂停轮播
            });
          }
          
          // 添加滚动特效
          window.addEventListener('scroll', function() {
            const newsTitle = document.querySelector('.home-news-title');
            if (newsTitle) {
              const rect = newsTitle.getBoundingClientRect();
              if (rect.top < window.innerHeight * 0.8) {
                newsTitle.classList.add('animate__fadeInDown');
              }
            }
          });
          
          console.log('轮播图初始化完成');
        })
        .catch(error => {
          console.error('Bootstrap 加载失败:', error);
        });
    } catch (error) {
      console.error('初始化页面功能失败:', error);
    }
  }, 500);
});



</script>
<style scoped lang="less">
.banner-section {
  width: 100%;
  overflow: hidden; /* 防止溢出 */
  position: relative; /* 为子元素的定位提供参考 */
  padding: 0; /* 移除内边距 */
  margin: 0; /* 移除外边距 */
  font-size: 0; /* 解决图片与 div 之间的间隙 */
  margin-top: 55px; /* 为NavBar留出空间 */
  
  @media (max-width: 768px) {
    margin-top: 56px; /* 移动端的NavBar高度稍小 */
  }
  
  #mainCarousel {
    width: 100%;
    
            
    .carousel {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin-top: 0;
            height: 100vh;
        }
        .carousel-item {
            position: relative;
            height: 100vh;
        }
        .carousel-item img,
        .carousel-item video {
            object-fit: cover;
            height: 100%;
            width: 100%;
        }
        .carousel-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            // background-color: rgba(0, 0, 0, 0.4);
        }
    
    .carousel-control-prev,
    .carousel-control-next {
      width: 5%;
      opacity: 0.8;
      
      &:hover {
        opacity: 1;
      }
    }
    
    .carousel-indicators {
      margin-bottom: 20px;
      
      button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.5);
        margin: 0 5px;
        
        &.active {
          background-color: #fff;
        }
      }
    }
  }
}

/* 解决方案区域样式 */
.solution-section {
  padding: 80px 0;
  background-color: #f5f7fa;
  font-size: 16px; /* 恢复正常字体大小 */
  
  @media (max-width: 768px) {
    padding: 40px 0;
  }
  
  .container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .section-title {
    text-align: center;
    margin-bottom: 50px;
    
    @media (max-width: 768px) {
      margin-bottom: 30px;
    }
    
    h2 {
      font-size: 32px;
      color: #333;
      margin-bottom: 20px;
      font-weight: bold;
      
      @media (max-width: 768px) {
        font-size: 24px;
        margin-bottom: 15px;
      }
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin: 0 auto;
      line-height: 1.6;
      
      @media (max-width: 768px) {
        font-size: 14px;
        line-height: 1.5;
        padding: 0 15px;
      }
    }
  }
  
  .solution-content {
    display: flex;
    align-items: center; /* 使用 align-items 而不是 align-content 实现垂直居中 */
    justify-content: center;
    gap: 150px;
    min-height: 400px; /* 设置最小高度确保有足够空间垂直居中 */
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 30px;
      min-height: auto;
    }
  }
  
  .solution-menu {
    flex-shrink: 0;
    border-radius: 4px;
    overflow: hidden;
    width: 220px;
    background-color: #EEEEEE;
    height: fit-content; /* 使高度适应内容，不继承父元素高度 */
    /* 移除 align-self: flex-start 以允许父元素的垂直居中生效 */
    
    @media (max-width: 768px) {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 0;
    }
    
    &-item {
      height: 80px; /* 固定每个菜单项的高度为80px */
      line-height: 80px; /* 垂直居中文本 */
      padding: 0 20px; /* 调整左右内边距，去掉上下内边距 */
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 16px;
      color: #333;
      text-align: center;
      margin-bottom: 1px;
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
      justify-content: center; /* 水平居中 */
      
      &:hover {
        background-color: #d9e3ff;
        color: #1A65FF;
      }
      
      &.active {
        background-color: #1A65FF;
        color: #FFFFFF;
        font-weight: bold;
      }
      
      @media (max-width: 768px) {
        text-align: center;
        flex: 1;
        min-width: 120px;
        margin-bottom: 0;
        margin-right: 1px;
        height: 40px; /* 确保在移动端也保持相同高度 */
        line-height: 40px;
        font-size: 14px;
        padding: 0 4px;
      }
    }
  }
  
  .solution-display {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 20px;
    background-color: #FFFFFF;
    border-radius: 8px;
    position: relative;
    align-self: center; /* 确保垂直居中 */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    
    :deep(.arco-image) {
      width: 100% !important;
      height: 330px !important;
      
      .arco-image-img {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
      }
    }
    
    @media (max-width: 992px) {
      width: 100%;
      margin-top: 20px;
      
      :deep(.arco-image) {
        width: 100% !important;
        height: 200px !important;
      }
    }
    
    @media (max-width: 768px) {
      margin-top: 10px;
      box-shadow: none;
    }
    
    .solution-image {
      width: 600px;
      height: 330px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      
      @media (max-width: 992px) {
        width: 100%;
        height: auto;
      }
    }
    
    .solution-description {
      padding: 0px 20px;
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, -50%);
      p {
        width: 100%;
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
        line-height: 25px;
        text-align: center;
        
        @media (max-width: 768px) {
          font-size: 16px;
          line-height: 22px;
        }
      }
      
      @media (max-width: 768px) {
        padding: 0 10px;
      }
    }
  }
}

/* 数智化转型产品模块样式 */
.digital-products-section {
  padding: 60px 0;
  background-color: #E7F1FB;
  font-size: 16px;
  
  @media (max-width: 768px) {
    padding: 40px 0;
  }
  
  .container {
    width: 55%;
    max-width: 1200px;
    margin: 0 auto;
    
    @media (max-width: 992px) {
      width: 90%;
    }
  }
  
  .section-title {
    text-align: center;
    margin-bottom: 50px;
    
    h2 {
      font-size: 32px;
      color: #333;
      margin-bottom: 20px;
      font-weight: bold;
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin: 0 auto;
      line-height: 1.6;
      max-width: 800px;
    }
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;

  /* 移动端一行只展示一个图片 */
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
  
  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 576px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

.product-card {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  
  @media (max-width: 768px) {
    padding: 15px;
  }
  
  @media (max-width: 576px) {
    padding: 10px;
  }
  

  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .product-image {
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
    // border: 1px dashed #ccc;
    // border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    object-fit: cover;
    img{
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  h3 {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
    
    @media (max-width: 576px) {
      font-size: 16px;
      margin-bottom: 5px;
    }
  }
  
  p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    
    @media (max-width: 576px) {
      font-size: 12px;
    }
  }
}
.section-title {
    text-align: center;
    margin-bottom: 50px;
    
    h2 {
      font-size: 32px;
      color: #333;
      margin-bottom: 20px;
      font-weight: bold;
      position: relative;
      display: inline-block;
      
      &:before, &:after {
        content: '■';
        color: #1A65FF;
        font-size: 24px;
        position: relative;
        top: -2px;
        margin: 0 10px;
      }
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin: 0 auto;
      line-height: 1.6;
      max-width: 800px;
    }
  }

/* 企业案例模块样式 */
.case-studies-section {
  padding: 60px 0;
  background-color: #FFFFFF;
  font-size: 16px;
  
  @media (max-width: 768px) {
    padding: 40px 0;
  }
  
  .container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
 
}

.case-studies-grid {
  /* 移动端一行只展示一个图片 */
  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
  }
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 40px;
  
  @media (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 576px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

.case-study-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  @media (max-width: 768px) {
    margin-bottom: 15px;
  }
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .case-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    
    :deep(.arco-image) {
      width: 100% !important;
      height: 200px !important;
      
      .arco-image-img {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
      }
    }
  }
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 15px 0 10px;
    padding: 0 15px;
    text-align: center;
    
    @media (max-width: 576px) {
      font-size: 16px;
      margin: 10px 0 5px;
      padding: 0 5px;
    }
  }
  
  p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    padding: 0 15px;
    margin-bottom: 15px;
    text-align: center;
    
    @media (max-width: 576px) {
      font-size: 12px;
      padding: 0 5px;
      margin-bottom: 10px;
    }
  }
  
  .learn-more-btn {
    display: block;
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background-color: #FFFFFF;
    color: #1A65FF;
    border: 1px solid #1A65FF;
    border-radius: 18px;
    margin: 15px auto;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    
    @media (max-width: 576px) {
      width: 90px;
      height: 30px;
      line-height: 30px;
      font-size: 12px;
      margin: 10px auto;
      border-radius: 15px;
    }
    
    &:hover {
      background-color: #1A65FF;
      color: #FFFFFF;
    }
  }
}

/* 新闻资讯模块样式 */
.news-section {
  padding: 60px 0;
  background-color: #E7F1FB;
  font-size: 16px;
  
  @media (max-width: 768px) {
    padding: 40px 0;
  }
  
  .container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .news-title-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    
    h2 {
      font-size: 28px;
      font-weight: bold;
      color: #333;
      margin: 0 10px;
      
      @media (max-width: 768px) {
        font-size: 22px;
      }
    }
    
    .blue-square {
      width: 8px;
      height: 8px;
      background-color: #1A65FF;
      margin: 0 5px;
      
      @media (max-width: 768px) {
        width: 6px;
        height: 6px;
      }
    }
  }
  
  .news-subtitle {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin-bottom: 30px;
    
    @media (max-width: 768px) {
      font-size: 12px;
      margin-bottom: 20px;
    }
  }
}

.news-content {
  display: flex;
  gap: 40px;
  margin-top: 40px;
  
  @media (max-width: 992px) {
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
  }
  
  .news-image {
    max-width: 40%;
    border-radius: 4px;
    overflow: hidden;
  
    @media (max-width: 992px) {
      flex: 0 0 100%;
      max-width: 100%;
      height: 250px;
    }
    
    a-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .news-list { 
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #e7f1fb;
    overflow: hidden;
   
    
    @media (max-width: 992px) {
      flex: 0 0 100%;
      max-width: 100%;
      margin-top: 20px;
    }
    
    .news-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 0;
      border-radius: 0px !important;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #e7f1fb;
      box-shadow: none !important;
      
      @media (max-width: 768px) {
        padding: 10px 0;
      }
      
      &:hover {
        .news-title {
          color: #1A65FF;
        }
      }
      
      .news-title {
        font-size: 16px;
        color: #333;
        transition: color 0.3s ease;
        flex: 1;
        
        @media (max-width: 768px) {
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .news-date {
        font-size: 14px;
        color: #999;
        margin-left: 20px;
        margin: auto;
        @media (max-width: 768px) {
          font-size: 12px;
          margin-left: 10px;
        }
      }
    }
  }
}
</style>