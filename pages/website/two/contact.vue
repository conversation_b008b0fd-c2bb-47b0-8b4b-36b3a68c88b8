<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl2" />
    
    <div class="contact-container">
      <div class="page-header">
        <h2 class="text-center text-2xl font-bold my-8">联系留言</h2>
      </div>

      <div class="contact-form-container">
        <div>
          <a-form :model="formData">
            <!-- 姓名 -->
            <a-form-item field="submitter_name" label="姓名" :rules="[{required:true,message:'请输入姓名'}]" >
              <a-input v-model="formData.submitter_name" placeholder="请填写姓名" />
            </a-form-item>
            
            <!-- 手机 -->
            <a-form-item field="phone" label="手机" :rules="[
              {required:true, message:'请输入手机号码'},
              {validator: (value) => validatePhone(value), message: '请输入正确的手机号码格式'}
            ]">
              <a-input v-model="formData.phone" placeholder="请填写手机号码" />
            </a-form-item>
            
            <!-- 邮件 -->
            <a-form-item field="email" label="邮件">
              <a-input v-model="formData.email" placeholder="请填写邮箱" />
            </a-form-item>

            <!-- <a-form-item field="message_location" label="职位" >
              <a-input v-model="formData.message_location" placeholder="请输入职位名称" />
            </a-form-item> -->
            <!-- 留言 -->
            <a-form-item field="message_details" label="留言">
              <a-textarea v-model="formData.message_details" placeholder="请填写留言" />
            </a-form-item>
            
            <!-- 提交按钮 -->
            <a-form-item>
              <a-button type="primary" long @click="handleSubmit">提交申请</a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
    
    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl2" />
  </div>
</template>

<script setup>
import recruitmentApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted } from "vue";
import NavBar from "@/components/website/two/NavBar.vue";
import Footer from "@/components/website/two/Footer.vue";
import { Message } from "@arco-design/web-vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: "/website/two/contact"
});

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";

// 表单数据
const formData = ref({
  submitter_name: "",
  phone: "",
  email: "",
  // message_location: "",
  message_details: ""
});

// 手机号码校验函数
const validatePhone = (phone) => {
  // 中国手机号码校验正则表达式，11位数字，以1开头
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 表单提交处理
const handleSubmit = () => {
  // 表单验证
  if (!formData.value.submitter_name) {
    Message.warning("请填写姓名");
    return;
  }
 
  // 手机号码格式校验
  if (!validatePhone(formData.value.phone)) {
    Message.warning("请填写正确的手机号");
    return;
  }

  // if (!formData.value.message_location) {
  //   Message.warning("请填写职位");
  //   return;
  // }
  
  // 这里应替换为实际的API调用
  recruitmentApi.messageManagement.create(formData.value).then(res => {
      if(res.code == 200){
        Message.success('申请提交成功！');
         // 重置表单
        formData.value = {
          submitter_name: "",
          phone: "",
          email: "",
          // message_location: "",
          message_details: ""
        };
      }else{
        Message.error(res.message);
      }
    })
  // 模拟提交成功
};

// 页面加载完成后执行的逻辑
onMounted(() => {
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
});

// 添加页面元信息
useHead({
  link: [
    { rel: 'stylesheet', href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css` },
    { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' }
  ]
});
</script>

<style scoped lang="less">
/* 网站容器样式 */
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: #F9F9F9;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 20px 0; /* 为固定导航栏留出空间 */
}

.contact-form-container {
  margin-bottom: 50px !important;
  padding: 50px 85px 45px 0px;
  background-color: white;
  max-width: 900px;
  margin: 0 auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .contact-container {
    padding: 60px 15px 0;
  }
  
  .contact-form-container {
    padding: 30px 20px;
    margin-bottom: 30px !important;
  }
  
  .page-header h2 {
    font-size: 1.5rem;
    margin: 15px 0;
  }
  
  :deep(.arco-form-item) {
    margin-bottom: 15px;
  }
  
  :deep(.arco-form-item-label-col) {
    padding-bottom: 5px;
  }
  
  :deep(.arco-btn) {
    height: 40px;
    font-size: 16px;
  }
}
</style>
