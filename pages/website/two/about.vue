<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl2" />
    
    <div class="about-container">
      <div class="page-header">
        <h2 class="text-center text-2xl font-bold my-8">公司介绍</h2>
      </div>

      <div class="content-section">
        <div class="section mb-10" >
          <h2 class="text-xl font-bold mb-4">{{companyInformations[0]?.title}}</h2>
          <p
            class="text-gray-700 leading-relaxed"
          >{{companyInformations[0]?.description}}</p>
        </div>
        <div class="image-section my-10">
          <a-image
            :preview="false"
            src="/website/two/assets/images/about.png"
            alt="公司大楼"
            width="100%"
            height="auto"
          />
        </div>
      </div>
    </div>
    <!-- 企业文化模块 -->
    <div class="culture-section">
      <div class="culture-container">
        <h2 class="text-2xl font-bold my-8 md:text-center">企业文化</h2>

        <div class="culture-cards grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 卡片1 -->
          <div class="culture-card bg-white rounded-lg overflow-hidden shadow-md hover-effect" v-for="(item,index) in enterpriseInforList" :key="index">
            <div class="card-image fixed-height">
              <a-image
               :preview="false"
               :src="item.cover_image"
                width="100%"
                height="100%"
                fit="cover"
              />
            </div>
            <div class="card-content p-4">
              <h3 class="text-lg font-bold text-blue-600 mb-2">{{item.title}}</h3>
              <p class="text-xs text-gray-500 mb-2">{{item.description}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl2" />
  </div>
</template>

<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted } from "vue";
import NavBar from "@/components/website/two/NavBar.vue";
import Footer from "@/components/website/two/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: "/website/two/about"
});

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";


//企业信息
const enterpriseInforList= ref([]);
const companyInformations = ref([]);
const getEnterpriseInformations = () =>{
    officialApi.getEnterpriseInformations({page:1,pageSize:4}).then(res => {
        if(res.code == 200){
            enterpriseInforList.value = res.data.items;
        }
    })
    officialApi.getEnterpriseInformations({page:1,pageSize:1,cate_name:'公司简介'}).then(res => {
        if(res.code == 200){
          companyInformations.value = res.data.items;
        }
    })
}

// 页面加载完成后执行的逻辑
onMounted(() => {
  getEnterpriseInformations()
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
});

// 添加页面元信息
useHead({
  link: [
    { rel: 'stylesheet', href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css` },
    { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' }
  ]
});
</script>

<style scoped lang="less">
/* 网站容器样式 */
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: white;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 40px 0; /* 为固定导航栏留出空间 */
}

.content-section {
  padding:20px ;
}

.section h2 {
  color: #2c3e50;
  padding-bottom: 10px;
}

.image-section {
  text-align: center;
}

.fixed-height {
  height: 200px;
  overflow: hidden;
  position: relative;
}

.fixed-height .arco-image {
  transition: transform 0.5s ease;
}

.hover-effect:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.hover-effect:hover .fixed-height .arco-image {
  transform: scale(1.05);
}

.hover-effect:hover .card-content h3 {
  color: #1e40af;
}

.hover-effect:hover .card-content p {
  color: #374151;
}

.culture-card {
  display: flex;
  flex-direction: column;
  text-align: left;
  height: 100%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.card-content {
  flex-grow: 1;
}
.culture-section {
  background-color: #E7F1FB;
  padding: 20px 0;
}

.culture-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0px 20px 40px 20px;
}

/* 移动端兼容性样式 */
@media (max-width: 768px) {
  .about-container {
    padding: 80px 15px 0;
  }
  
  .culture-container {
    padding: 0 15px;
  }
  
  .culture-cards {
    grid-gap: 15px;
  }
  
  .card-content {
    padding: 15px !important;
  }
  
  .fixed-height {
    height: 160px;
  }
  
  .culture-section {
    padding: 20px 0;
  }
}
.culture-container,.page-header{
  text-align: center;
  h2 {
      font-size: 32px;
      color: #333;
      margin-bottom: 30px;
      font-weight: bold;
      position: relative;
      display: inline-block;
      
      &:before, &:after {
        content: '■';
        color: #1A65FF;
        font-size: 24px;
        position: relative;
        top: -2px;
        margin: 0 10px;
      }
    }
}


@media (min-width: 769px) and (max-width: 1024px) {
  .culture-container {
    padding: 0 30px;
  }
  
  .culture-section {
    padding: 30px 0;
  }
}

@media (min-width: 1025px) and (max-width: 1200px) {
  .culture-container {
    padding: 0 40px;
  }
}
</style>
