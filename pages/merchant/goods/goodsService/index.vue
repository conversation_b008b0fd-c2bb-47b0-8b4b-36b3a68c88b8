
<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :data="tableData">
      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-popconfirm content="确定要删除该服务吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>

      <!-- 自定义列 - 图片 -->
      <template #image="{ record }">
        <a-image
          v-if="record.image"
          :src="record.image"
          width="60"
          height="60"
          :preview-visible="false"
          fit="contain"
        />
        <span v-else>暂无图片</span>
      </template>
    </ma-crud>

    <!-- 服务编辑组件 -->
    <service-edit
      v-model:visible="modalVisible"
      :title="modalTitle"
      :data="currentService"
      @submit="handleServiceSubmit"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconEdit, IconDelete, IconPlus } from '@arco-design/web-vue/es/icon';
import ServiceEdit from './components/ServiceEdit.vue';

definePageMeta({
  name: "merchant-goodsService",
  path: "/merchant/goods/goodsService",
})

const crudRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增服务');
const currentService = ref({});
const currentId = ref(null);

// 模拟表格数据
const tableData = ref([
  {
    id: 1,
    name: "7天无理由退货",
    image: "https://img1.baidu.com/it/u=1485012388,2433171947&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
    description: "商品自签收之日起7天内可无理由退货",
    sort: 1,
    status: 1,
    created_at: "2025-04-01 10:30:04"
  },
  {
    id: 2,
    name: "48小时发货",
    image: "https://img2.baidu.com/it/u=2947593269,1474799885&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
    description: "下单后48小时内发货",
    sort: 2,
    status: 1,
    created_at: "2025-04-02 14:20:15"
  },
  {
    id: 3,
    name: "正品保障",
    image: "https://img0.baidu.com/it/u=3024588702,195874636&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
    description: "所售商品均为正品，假一赔十",
    sort: 3,
    status: 1,
    created_at: "2025-04-03 09:45:30"
  }
]);

// 打开新增弹窗
const handleAdd = () => {
  modalTitle.value = '新增服务';
  currentService.value = {
    name: '',
    image: '',
    description: '',
    sort: 0,
    status: 1
  };
  currentId.value = null;
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record) => {
  modalTitle.value = '编辑服务';
  currentService.value = { ...record };
  currentId.value = record.id;
  modalVisible.value = true;
};

// 处理删除
const handleDelete = (id) => {
  // 这里是模拟删除，实际项目中应该调用API
  tableData.value = tableData.value.filter(item => item.id !== id);
  Message.success('删除成功');
};

// 处理服务表单提交
const handleServiceSubmit = (formData) => {
  try {
    if (currentId.value === null) {
      // 新增操作
      const newId = Math.max(...tableData.value.map(item => item.id), 0) + 1;
      const newService = {
        ...formData,
        id: newId,
        created_at: new Date().toLocaleString()
      };
      tableData.value.push(newService);
      Message.success('新增服务成功');
    } else {
      // 编辑操作
      const index = tableData.value.findIndex(item => item.id === currentId.value);
      if (index !== -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          ...formData
        };
        Message.success('编辑服务成功');
      }
    }
    modalVisible.value = false;
  } catch (error) {
    console.error('提交失败:', error);
    Message.error('操作失败，请重试');
  }
};

// CRUD 配置
const crud = reactive({
  api: '', // 实际项目中应该填写API路径
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 150,
  add: {
    show: false, // 使用自定义新增按钮
  },
  edit: {
    show: false, // 使用自定义编辑按钮
  },
  delete: {
    show: false, // 使用自定义删除按钮
  },
  pagination: {
    pageSize: 10,
    showTotal: true,
    showPageSize: true,
  },
});

// 表格列配置
const columns = reactive([
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
    sortable: true,
  },
  {
    title: '服务名称',
    dataIndex: 'name',
    width: 150,
    sortable: true,
  },
  {
    title: '图片',
    dataIndex: 'image',
    width: 100,
  },
  {
    title: '服务描述',
    dataIndex: 'description',
    width: 250,
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180,
    sortable: true,
  }
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>