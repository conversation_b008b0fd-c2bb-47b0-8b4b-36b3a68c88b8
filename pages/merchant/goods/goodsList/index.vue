<template>
  <div class="ma-content-block p-4">
    <a-tabs v-model:active-key="activeTab" @change="handleTabChange">
      <a-tab-pane key="all" title="全部商品"></a-tab-pane>
      <a-tab-pane key="on_sale" title="销售中"></a-tab-pane>
      <a-tab-pane key="in_stock" title="仓库中"></a-tab-pane>
      <a-tab-pane key="discontinued" title="已下架"></a-tab-pane>
    </a-tabs>

    <ma-crud :options="crudOptions" :columns="columns" :data="displayedData" :expandable="expandableConfig" ref="crudRef">
      <!-- SPU 图片 -->
      <template #spu_image="{ record }">
        <a-image :src="record.spu_image" width="60" height="60" fit="cover" />
      </template>

      <!-- SPU 状态 -->
      <template #status="{ record }">
        <a-tag v-if="record.status === 'on_sale'" color="green">销售中</a-tag>
        <a-tag v-else-if="record.status === 'in_stock'" color="blue">仓库中</a-tag>
        <a-tag v-else-if="record.status === 'discontinued'" color="red">已下架</a-tag>
        <span v-else>{{ record.status }}</span>
      </template>

      <!-- SPU 标签 -->
      <template #tags="{ record }">
        <template v-if="record.tags && Array.isArray(record.tags)">
          <a-tag v-for="tag in record.tags" :key="tag" class="mr-1 mb-1">{{ tag }}</a-tag>
        </template>
        <span v-else>{{ record.tags }}</span>
      </template>

      <!-- SPU 服务 -->
      <template #service="{ record }">
        <template v-if="record.service && Array.isArray(record.service)">
          <a-tag v-for="srv in record.service" :key="srv" color="arcoblue" class="mr-1 mb-1">{{ srv }}</a-tag>
        </template>
        <span v-else>{{ record.service }}</span>
      </template>

      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <a-space>
          <a-link @click="handleEdit(record)">编辑</a-link>
          <a-popconfirm content="确定要删除此商品吗?" @ok="handleDelete(record.id)">
            <a-link status="danger">删除</a-link>
          </a-popconfirm>
          <!-- 可以添加更多操作，如下架/上架等 -->
        </a-space>
      </template>

      <!-- SKU 展开行 -->
      <template #expand-row="{ record }">
        <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded">
          <div class="max-w-6xl mx-auto">
            <a-table
              :data="record.skus"
              :pagination="false"
              size="small"
              :scroll="{ x: '100%' }"
            >
              <template #columns>
                <a-table-column title="SKU图片" data-index="sku_image" :width="80" align="center">
                  <template #cell="{ record: skuRecord }">
                    <a-image :src="skuRecord.sku_image" width="40" height="40" fit="cover" />
                  </template>
                </a-table-column>
                <a-table-column title="SKU名称" data-index="sku_name" :width="150"></a-table-column>
                <a-table-column title="销售价" data-index="price" :width="100" align="right"></a-table-column>
                <a-table-column title="市场价" data-index="market_price" :width="100" align="right"></a-table-column>
                <a-table-column title="成本价" data-index="cost_price" :width="100" align="right"></a-table-column>
                <a-table-column title="销量" data-index="sales" :width="80" align="center"></a-table-column>
                <a-table-column title="库存" data-index="stock" :width="80" align="center"></a-table-column>
                <a-table-column title="单位" data-index="unit" :width="80"></a-table-column>
                <a-table-column title="型号" data-index="model" :width="120"></a-table-column>
                <a-table-column title="状态" data-index="status" :width="80" align="center">
                  <template #cell="{ record: skuRecord }">
                    <a-tag v-if="skuRecord.status === 'enabled'" color="green" size="small">上架</a-tag>
                    <a-tag v-else-if="skuRecord.status === 'disabled'" color="red" size="small">下架</a-tag>
                    <span v-else>{{ skuRecord.status }}</span>
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </div>
        </div>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';

// 定义页面路由元信息
definePageMeta({
  name: 'merchant-goodsList',
  path: '/merchant/goods/goodsList'
})
const crudRef = ref();
const activeTab = ref('all');
const masterData = ref([
  {
    id: 1, spu_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', name: '演示商品 SP001 - 一款多功能智能手表',
    category_id: 1, category_name: '智能穿戴', brand_id: 1, brand_name: 'MineBrand', tags: ['新品', '热销', '智能'], tag_ids: [1, 2, 3],
    status: 'on_sale', sales: 1024, service: ['7天无理由', '包邮'], service_ids: [1, 2], created_at: '2024-12-01 10:00:00',
    skus: [
      { id: 101, sku_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', sku_name: '黑色款', price: 199.00, market_price: 299.00, cost_price: 99.00, sales: 500, stock: 1000, unit: '件', model: 'MWatch-Blk', status: 'enabled' },
      { id: 102, sku_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', sku_name: '白色款', price: 199.00, market_price: 299.00, cost_price: 99.00, sales: 524, stock: 800, unit: '件', model: 'MWatch-Wht', status: 'enabled' },
    ]
  },
  {
    id: 2, spu_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', name: '演示商品 SP002 - 高性能无线蓝牙耳机',
    category_id: 2, category_name: '影音娱乐', brand_id: 2, brand_name: 'SoundMax', tags: ['蓝牙5.3', '降噪'], tag_ids: [4, 5],
    status: 'in_stock', sales: 512, service: ['一年保修'], service_ids: [3], created_at: '2024-11-15 14:30:00',
    skus: [
      { id: 201, sku_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', sku_name: '标准版', price: 299.00, market_price: 399.00, cost_price: 150.00, sales: 512, stock: 500, unit: '副', model: 'SEar-Std', status: 'enabled' },
    ]
  },
  {
    id: 3, spu_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', name: '演示商品 SP003 - 已下架旧款手机',
    category_id: 3, category_name: '手机通讯', brand_id: 3, brand_name: 'OldMobile', tags: ['经典'], tag_ids: [6],
    status: 'discontinued', sales: 2000, service: [], service_ids: [], created_at: '2023-01-20 09:00:00',
    skus: [
      { id: 301, sku_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', sku_name: '64GB', price: 999.00, market_price: 1999.00, cost_price: 500.00, sales: 2000, stock: 0, unit: '台', model: 'OM-64G', status: 'disabled' },
    ]
  },
  {
    id: 4, spu_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', name: '演示商品 SP004 - 智能家居摄像头',
    category_id: 1, category_name: '智能穿戴', brand_id: 1, brand_name: 'MineBrand', tags: ['安防', '智能'], tag_ids: [7, 3],
    status: 'on_sale', sales: 300, service: ['一年保修', '云存储'], service_ids: [3, 4], created_at: '2024-10-25 11:00:00',
    skus: [
      { id: 401, sku_image: 'https://img.freepik.com/premium-vector/soft-cotton-pajamas-expecting-mothers-pregnant-women-comfortable-sleepwear-illustration_176411-3913.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379', sku_name: '室内版', price: 399.00, market_price: 499.00, cost_price: 180.00, sales: 300, stock: 600, unit: '台', model: 'MCam-In', status: 'enabled' },
    ]
  },
]);
const router = useRouter()
const displayedData = ref([]);
const filterData = () => {
  const key = activeTab.value;
  if (key === 'all') {
    displayedData.value = [...masterData.value];
  } else {
    displayedData.value = masterData.value.filter(item => item.status === key);
  }
};

// 处理添加商品
const handleAdd = () => {
  console.log('触发添加商品');
  import('@/utils/common').then(module => {
    const router = useRouter();
    module.navigateWithTag(router, '/merchant/goods/addGoods');
  });
};

// --- ma-crud Basic Options (No API or Data here) ---
const crudOptions = reactive({
  // --- CRUD 功能开关 ---
  add: { show: true, text: '添加商品', action: handleAdd /* api: '/api/master/goods/save' */ },
  // edit and delete handled via operation column
  searchColNumber: 3,
  expandRowByClick: true,
  showExpandRow: true,
  showIndex: false,

});

const expandableConfig = reactive({
  // title: '展开', 
  // width: 60,  
});

const mockCategories = [
  { id: 1, name: '智能穿戴' },
  { id: 2, name: '影音娱乐' },
  { id: 3, name: '手机通讯' },
  { id: 4, name: '家用电器' },
];
const mockBrands = [
  { id: 1, name: 'MineBrand' },
  { id: 2, name: 'SoundMax' },
  { id: 3, name: 'OldMobile' },
  { id: 4, name: 'HomeAppliance Co.' },
];
const mockTags = [
  { id: 1, name: '新品' },
  { id: 2, name: '热销' },
  { id: 3, name: '智能' },
  { id: 4, name: '蓝牙5.3' },
  { id: 5, name: '降噪' },
  { id: 6, name: '经典' },
  { id: 7, name: '安防' },
];
const mockServices = [
  { id: 1, name: '7天无理由' },
  { id: 2, name: '包邮' },
  { id: 3, name: '一年保修' },
  { id: 4, name: '云存储' },
];
const statusOptions = [
  { label: '销售中', value: 'on_sale' },
  { label: '仓库中', value: 'in_stock' },
  { label: '已下架', value: 'discontinued' },
];
// SPU 表格列定义
const columns = reactive([
  { title: 'ID', dataIndex: 'id', addDisplay: false, editDisplay: false, width: 70, align: 'center', fixed: 'left', show: false },
  { title: '商品图片', dataIndex: 'spu_image', width: 80, align: 'center', formType: 'upload' /* Still define for form */ },
  { title: '商品名称', dataIndex: 'name', search: true, width: 250,},
  {
    title: '商品分类', dataIndex: 'category_name', search: true, width: 120,
    formType: 'select', dict: { data: mockCategories, props: { label: 'name', value: 'id' } }
  },
  {
    title: '商品品牌', dataIndex: 'brand_name', search: true, width: 120,
    formType: 'select', dict: { data: mockBrands, props: { label: 'name', value: 'id' } }
  },
  {
    title: '商品标签', dataIndex: 'tags', width: 150,
    formType: 'select', multiple: true, dict: { data: mockTags, props: { label: 'name', value: 'id' } }
  },
  {
    title: '状态', dataIndex: 'status', width: 90, align: 'center', search: true,
    formType: 'select', dict: { data: statusOptions }
  },
  { title: '销量', dataIndex: 'sales', width: 90, align: 'center', sortable: { sortDirections: ['ascend', 'descend'], sorter: true } },
  {
    title: '商品服务', dataIndex: 'service', width: 150,
    formType: 'checkbox', dict: { data: mockServices, props: { label: 'name', value: 'id' } }
  },
  { title: '创建时间', dataIndex: 'created_at', width: 170, align: 'center', search: true, formType: 'range', showTime: true, sortable: { sortDirections: ['ascend', 'descend'], sorter: true } },
  { title: '操作', dataIndex: 'operation', width: 120, align: 'center', fixed: 'right' }
]);

// 标签页切换处理
const handleTabChange = (key) => {
  activeTab.value = key;
  filterData();
};

const handleEdit = (record) => {
  console.log('触发编辑商品 (Mock):', record.id);
  Message.info('编辑功能需结合表单实现');
};

const handleDelete = async (id) => {
  console.log('触发删除商品 (Mock):', id);
  const masterIndex = masterData.value.findIndex(item => item.id === id);
  if (masterIndex > -1) {
    masterData.value.splice(masterIndex, 1);
    const displayIndex = displayedData.value.findIndex(item => item.id === id);
    if (displayIndex > -1) {
      displayedData.value.splice(displayIndex, 1);
    }
    Message.success('删除成功 (Mock)');
  } else {
    Message.error('删除失败 (Mock) - 未找到商品');
  }
};

// --- 生命周期钩子 ---
onMounted(() => {
  filterData();
});

</script>
<script>
export default { name: "master-goodsList" }
</script>
<style scoped>
/* 可以添加一些自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}

:deep(.arco-tabs-nav) {
  padding-left: 16px;
  /* 让 tabs 和下面的内容对齐 */
}

:deep(.arco-table-cell .arco-image-img) {
  object-fit: cover;
  /* 确保图片按比例填充 */
}

.bg-gray-100 {
  background-color: #f7f8fa;
}

.dark .bg-gray-700 {
  background-color: #2a2a2b;
  /* 适配暗黑模式的背景色 */
}
</style>