<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div>
    <a-card class="mb-4">
      <div class="mb-4">
        <a-button type="primary" @click="addAttribute">
          <template #icon><icon-plus /></template>
          添加属性
        </a-button>
      </div>
      
      <a-table :columns="columns" :data="attributes" :pagination="false" bordered>
        <template #empty>
          <div class="text-center py-4 text-gray-500">暂无属性数据</div>
        </template>
        <template #operation="{ record, rowIndex }">
          <a-button type="text" size="small" @click="editAttribute(record, rowIndex)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-button type="text" status="danger" size="small" @click="deleteAttribute(rowIndex)">
            <template #icon><icon-delete /></template>
            删除
          </a-button>
        </template>
      </a-table>
    </a-card>
    
    <!-- 属性编辑对话框 -->
    <a-modal v-model="attributeModalVisible" :title="isEdit ? '编辑属性' : '添加属性'" @cancel="cancelAttributeModal" @ok="confirmAttributeModal" :ok-button-props="{ disabled: !attributeForm.name || !attributeForm.value }">
      <a-form :model="attributeForm" layout="vertical">
        <a-form-item field="name" label="属性名称" required>
          <a-input v-model="attributeForm.name" placeholder="请输入属性名称" />
        </a-form-item>
        <a-form-item field="value" label="属性值" required>
          <a-input v-model="attributeForm.value" placeholder="请输入属性值" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconPlus, IconEdit, IconDelete } from '@arco-design/web-vue/es/icon';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  categoryId: {
    type: [Number, String],
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

// 属性列表
const attributes = ref([]);

// 监听商品分类变化，获取对应的属性选项
watch(() => props.categoryId, (newVal) => {
  if (newVal) {
    fetchCategoryAttributes(newVal);
  }
}, { immediate: true });

// 获取分类对应的属性
const fetchCategoryAttributes = async (categoryId) => {
  try {
    // 模拟从API获取数据
    // 实际开发中，这里应该调用后端API获取分类对应的属性
    console.log('获取分类ID为', categoryId, '的属性选项');
    
    // 模拟数据
    const mockAttributes = [
      { name: '材质', value: '' },
      { name: '尺寸', value: '' },
      { name: '颜色', value: '' }
    ];
    
    // 将获取到的属性添加到属性列表中
    attributes.value = mockAttributes;
    
    // 同步到父组件
    syncToParent();
  } catch (error) {
    console.error('获取分类属性失败', error);
    Message.error('获取分类属性失败');
  }
};

// 表格列定义
const columns = [
  {
    title: '属性名称',
    dataIndex: 'name',
  },
  {
    title: '属性值',
    dataIndex: 'value',
  },
  {
    title: '操作',
    slotName: 'operation',
    width: 150,
    align: 'center'
  }
];

// 属性表单
const attributeForm = reactive({
  name: '',
  value: ''
});

// 属性对话框
const attributeModalVisible = ref(false);
const isEdit = ref(false);
const editingIndex = ref(-1);

// 添加属性
const addAttribute = () => {
  isEdit.value = false;
  attributeForm.name = '';
  attributeForm.value = '';
  attributeModalVisible.value = true;
};

// 编辑属性
const editAttribute = (record, index) => {
  isEdit.value = true;
  editingIndex.value = index;
  attributeForm.name = record.name;
  attributeForm.value = record.value;
  attributeModalVisible.value = true;
};

// 删除属性
const deleteAttribute = (index) => {
  attributes.value.splice(index, 1);
  syncToParent();
};

// 取消属性对话框
const cancelAttributeModal = () => {
  attributeModalVisible.value = false;
};

// 确认属性对话框
const confirmAttributeModal = () => {
  if (!attributeForm.name || !attributeForm.value) {
    Message.warning('请填写完整的属性信息');
    return;
  }
  
  if (isEdit.value) {
    // 编辑现有属性
    attributes.value[editingIndex.value] = { ...attributeForm };
  } else {
    // 添加新属性
    attributes.value.push({ ...attributeForm });
  }
  
  attributeModalVisible.value = false;
  syncToParent();
};

// 同步数据到父组件
const syncToParent = () => {
  emit('update:modelValue', { attributes: attributes.value });
};

// 表单验证
const validate = () => {
  // 属性信息不是必填的，所以直接返回成功
  return Promise.resolve(true);
};

// 暴露方法给父组件
defineExpose({
  validate,
  attributes
});
</script>

<style scoped>
/* 如果需要可以添加样式 */
</style>
