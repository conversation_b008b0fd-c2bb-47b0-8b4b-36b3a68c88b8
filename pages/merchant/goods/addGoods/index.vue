<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <a-card class="mb-4">
      <template #title>
        <div class="text-lg font-semibold">添加商品</div>
      </template>
      
      <!-- 步骤导航 -->
      <a-steps :current="currentStep + 1" class="mb-6">
        <a-step title="商品搜索" />
        <a-step title="基本信息" />
        <a-step title="属性信息" />
        <a-step title="规格/SKU" />
        <a-step title="商品详情" />
      </a-steps>
      
      <!-- 步骤内容区域 -->
      <div class="step-content">
        <!-- 步骤0：商品搜索 -->
        <div v-show="currentStep === 0">
          <div class="mb-4">
            <a-input-search
              v-model="searchKeyword"
              placeholder="请输入商品名称、ID或关键词搜索"
              search-button
              @search="searchProducts"
              allow-clear
            />
          </div>
          
          <a-table
            :data="searchResults"
            :loading="searchLoading"
            :pagination="pagination"
            @page-change="onPageChange"
          >
            <template #columns>
              <a-table-column title="商品图片" data-index="image">
                <template #cell="{ record }">
                  <a-image :src="record.image" width="60" height="60" fit="cover" />
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="name" />
              <a-table-column title="商品ID" data-index="id" />
              <a-table-column title="分类" data-index="category" />
              <a-table-column title="价格" data-index="price" />
              <a-table-column title="操作">
                <template #cell="{ record }">
                  <a-button type="text" @click="selectProduct(record)">选择</a-button>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
        
        <!-- 步骤1：基本信息 -->
        <div v-show="currentStep === 1">
          <BasicInfo 
            ref="basicInfoRef" 
            v-model="basicForm" 
            @open-label-manage="openLabelManage" 
            @open-service-manage="openServiceManage"
          />
        </div>
        
        <!-- 步骤2：属性信息 -->
        <div v-show="currentStep === 2">
          <AttributeInfo 
            ref="attributeInfoRef" 
            v-model="attributeForm"
          />
        </div>
        
        <!-- 步骤3：规格/SKU -->
        <div v-show="currentStep === 3">
          <SkuInfo 
            ref="skuInfoRef" 
            v-model="skuForm"
          />
        </div>
        
        <!-- 步骤4：商品详情 -->
        <div v-show="currentStep === 4">
          <ProductDetail 
            ref="productDetailRef" 
            v-model:detailValue="detailForm" 
            v-model:otherValue="otherForm"
          />
        </div>
      </div>
      
      <!-- 步骤按钮 -->
      <div class="flex justify-between mt-4">
        <a-button @click="prevStep" :disabled="currentStep === 0">上一步</a-button>
        <div>
          <a-button type="outline" @click="saveAsDraft" class="mr-2">保存草稿</a-button>
          <a-button v-if="currentStep < 4" type="primary" @click="nextStep">下一步</a-button>
          <a-button v-else type="primary" @click="submitForm">提交</a-button>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRoute } from 'vue-router';

// 获取路由参数
const route = useRoute();
const { id } = route.query;

// 判断是否为编辑模式
const isEdit = computed(() => !!id);

// 定义页面路由元信息
definePageMeta({
  name: 'merchant-addGoods',
  path: '/merchant/goods/addGoods'
})

// 导入组件
import BasicInfo from './components/BasicInfo.vue';
import AttributeInfo from './components/AttributeInfo.vue';
import SkuInfo from './components/SkuInfo.vue';
import ProductDetail from './components/ProductDetail.vue';

// 当前步骤
const currentStep = ref(0);

// 组件引用
const basicInfoRef = ref(null);
const attributeInfoRef = ref(null);
const skuInfoRef = ref(null);
const productDetailRef = ref(null);

// 基本信息表单
const basicForm = reactive({
  name: '',
  categoryId: '',
  brandId: '',
  unit: '',
  description: ''
});

// 属性信息表单
const attributeForm = reactive({
  attributes: []
});

// 标签和服务表单
const labelForm = reactive({
  labels: []
});

const serviceForm = reactive({
  services: []
});

// 规格表单
const skuForm = reactive({
  skuType: 1,
  specs: []
});

// 商品详情表单
const detailForm = reactive({
  content: ''
});

// 其他设置表单
const otherForm = reactive({
  status: 1,
  sort: 0,
  virtualSales: 0,
  keywords: '',
  seoTitle: '',
  seoDescription: ''
});

// 搜索关键词
const searchKeyword = ref('');

// 搜索结果
const searchResults = ref([]);

// 搜索加载状态
const searchLoading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 下一步
const nextStep = async () => {
  try {
    // 根据当前步骤进行表单验证
    // if (currentStep.value === 0) {
    //   // 基本信息步骤验证
    //   await basicInfoRef.value.validate();
    // } else if (currentStep.value === 1) {
    //   // 属性信息步骤验证
    //   await attributeInfoRef.value.validate();
    // } else if (currentStep.value === 2) {
    //   // 规格/SKU步骤验证
    //   await skuInfoRef.value.validate();
    // } else if (currentStep.value === 3) {
    //   // 商品详情步骤验证
    //   await productDetailRef.value.validate();
    // }
    
    // 验证通过，进入下一步
    if (currentStep.value < 4) {
      currentStep.value++;
    }
  } catch (error) {
    console.error('表单验证失败:', error);
    Message.error('请完善表单信息');
  }
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 打开标签管理
const openLabelManage = () => {
  Message.info('打开标签管理');
  // 这里应该打开标签管理弹窗
};

// 打开服务管理
const openServiceManage = () => {
  Message.info('打开服务管理');
  // 这里应该打开服务管理弹窗
};

// 保存草稿
const saveAsDraft = () => {
  Message.success('已保存为草稿');
  // 这里应该调用保存草稿的API
};

// 提交表单
const submitForm = async () => {
  try {
    // 验证最后一步表单
    await productDetailRef.value.validate();
    
    // 这里应该进行完整的表单验证和数据提交
    console.log('提交的商品数据:', {
      basic: basicForm,
      attribute: attributeForm,
      labels: labelForm,
      services: serviceForm,
      sku: skuForm,
      detail: detailForm,
      other: otherForm
    });
    
    Message.success('商品添加成功');
    // 这里应该调用提交商品的API
  } catch (error) {
    console.error('表单验证失败:', error);
    Message.error('请完善表单信息');
  }
};

// 搜索商品
const searchProducts = async () => {
  // 这里应该调用搜索商品的API
  searchLoading.value = true;
  setTimeout(() => {
    searchLoading.value = false;
    searchResults.value = [
      { id: 1, name: '商品1', image: 'https://example.com/image1.jpg', category: '分类1', price: 100 },
      { id: 2, name: '商品2', image: 'https://example.com/image2.jpg', category: '分类2', price: 200 },
      { id: 3, name: '商品3', image: 'https://example.com/image3.jpg', category: '分类3', price: 300 }
    ];
    pagination.total = 30;
  }, 1000);
};

// 选择商品
const selectProduct = (product) => {
  console.log('选择的商品:', product);
  // 这里应该处理选择商品的逻辑
};

// 分页变化
const onPageChange = (page) => {
  pagination.current = page;
  // 这里应该调用分页的API
};

// 初始化
onMounted(() => {
  // 确保当前步骤为基本信息（第一步）
  currentStep.value = 0;
});
</script>