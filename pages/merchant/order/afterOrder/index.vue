<template>
  <div class="order-list-container">
    <!-- 顶部标签页 -->
    <div class="order-tabs">
      <div class="tab-item active">售后订单</div>
    </div>
    <div class="search-area">
      <!-- 顶部统计卡片 -->
      <div class="stats-cards-container">
        <div class="stats-scroll-btn stats-scroll-left" @click="scrollStatsCards('left')">
          <icon-left />
        </div>
        <div class="stats-cards" ref="statsCardsRef">
          <div 
            v-for="card in statsCards" 
            :key="card.id"
            class="stats-card" 
            :class="{ active: activeStatsCard === card.id }"
            @click="setActiveStatsCard(card.id)"
          >
            <div class="stats-corner-mark" v-if="activeStatsCard === card.id">
              <icon-check class="check-icon" />
            </div>
            <div class="stats-title">{{ card.title }}</div>
            <div class="stats-value">{{ card.value }}</div>
          </div>
        </div>
        <div class="stats-scroll-btn stats-scroll-right" @click="scrollStatsCards('right')">
          <icon-right />
        </div>
      </div>

      <!-- 订单标签 -->
      <div class="order-tags">
        <div class="tag-item">快递配送</div>
        <div class="tag-item">到店自提</div>
        <div class="tag-item">同城配送</div>
        <div class="tag-item">
          <span>预售订单</span>
          <span class="tag-count">仅含预售订单(0)</span>
        </div>
        <div class="tag-item">
          <span>拼团订单</span>
          <span class="tag-count">仅含拼团订单(0)</span>
        </div>
        <div class="tag-item">
          <span>积分兑换订单</span>
          <span class="tag-count">(0)</span>
        </div>
        <div class="tag-item">
          <span>香港手淘发货</span>
          <span class="tag-count">()</span>
        </div>
        <div class="tag-right">
          <span>包含售罄()</span>
          <a-button size="small" type="text">问题订单(?)</a-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <SearchForm
        v-model:formData="searchForm"
        :formItems="formItems"
        :showAdvancedSearch="showAdvancedSearch"
        @search="handleSearch"
        @reset="resetSearch"
      >
        <!-- 自定义左侧按钮 -->
        <template #left-actions>
          <a-button type="primary" status="success">导出订单</a-button>
          <a-button type="primary" status="warning">导出记录</a-button>
          <a-dropdown>
            <a-button>
              更多筛选
              <icon-down />
            </a-button>
            <template #content>
              <a-doption>选项1</a-doption>
              <a-doption>选项2</a-doption>
            </template>
          </a-dropdown>
        </template>
        
        <!-- 自定义右侧内容 -->
        <!-- <template #right-actions>
          <a-checkbox v-model="searchForm.onlyShowSelected">仅显示已选订单</a-checkbox>
        </template> -->
      </SearchForm>

      <!-- 操作标签 -->
      <div class="operation-tags">
        <div class="tag-item">批量付款</div>
        <div class="tag-item">打单发货</div>
        <div class="tag-item">批量发货</div>
        <div class="tag-item">批量改价</div>
        <div class="tag-item">批量改地址</div>
        <div class="tag-item">
          更多
          <icon-down />
        </div>
        <div class="tag-item">不需要物流发货</div>
        <div class="tag-item">不需要物流发货</div>
        <div class="tag-item">仅退款申请</div>
        <div class="tag-item">下载订单</div>
        <div class="add-order-btn-mobile">
          <a-button type="primary" size="small" class="add-order-btn">添加订单</a-button>
        </div>
      </div>
    </div>

    <a-table :columns="columns" :data="tableData" :pagination="false" :span-method="spanMethod"
       v-model:selectedKeys="selectedRowKeys" row-key="rowId" class="order-table"
      :hoverable="false">
      <template #productInfo-cell="{ record }">
        <div v-if="record.type === 'header'" class="order-header-cell">
          <div class="order-header-left">
            <!-- Header Checkbox for Select/Deselect All on Page -->
            <a-checkbox 
              :model-value="isAllSelectedOnPage"
              :indeterminate="isIndeterminate"
              @change="handleSelectAllOnPage"
              class="header-checkbox order-checkbox" 
            />
            退款编号 {{ record.originalId }}
            <a-button shape="circle" size="mini" class="copy-btn-inline" @click="copyOrderId(record.originalId)">
              <icon-copy />
            </a-button>
            关联订单编号 {{ record.relatedOrderId }}
            <span class="order-time-inline">下单时间 {{ record.orderTime }}</span>
          </div>
          <div class="order-header-right">
            <a-button type="text" size="small" class="message-btn">
              <icon-message /> 高频购买
            </a-button>
            <a-button type="text" size="small" class="add-btn">
              <icon-plus /> 添加标注
            </a-button>
          </div>
        </div>
        <div v-else class="product-info-cell-content">
          <img :src="record.imageUrl" class="product-image" />
          <div class="product-details">
            <div class="product-name">{{ record.productName }}</div>
            <div class="product-meta">
              <div>{{ record.color }}</div>
              <div>商家编号：{{ record.merchantCode }}</div>
              <div>商品编ID：{{ record.productId }}</div>
              <div class="product-tags">
                <span class="risk-tag">除名加赠</span>
                <span class="days-tag">7天</span>
                <span class="speed-tag">极速退</span>
              </div>
              <div class="store-tags">
                <span class="store-tag">小店自营</span>
                <span class="goods-tag">商品卡</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template #refund-type-cell="{ record }">
        <div v-if="record.type === 'details'" class="status-tag">
          {{ record.refundType || '仅退款' }}
        </div>
      </template>

      <template #priceQuantity-cell="{ record }">
        <div v-if="record.type === 'details'" class="price-cell-content">
          <div class="price">¥{{ record.price }}</div>
          <div class="quantity">x{{ record.quantity }}</div>
          <a-link v-if="record.returnLink" class="return-link" status="warning">{{ record.returnLink }}</a-link>
        </div>
      </template>

      <template #payment-method-cell="{ record }">
        <div v-if="record.type === 'details'" class="status-tag">
          {{ record.paymentMethod }}
        </div>
      </template>

      <template #refund-method-cell="{ record }">
        <div v-if="record.type === 'details'" class="status-tag">
          {{ record.refundMethod || '原路退回' }}
        </div>
      </template>

      <template #submit-time-cell="{ record }">
        <div v-if="record.type === 'details'" class="time-info">
          {{ record.submitTime || record.orderTime }}
        </div>
      </template>

      <template #payment-cell="{ record }">
        <div v-if="record.type === 'details'" class="payment-cell-content">
          <div class="price highlight">¥{{ record.totalPayable }}</div>
        </div>
      </template>

      <template #consumer-cell="{ record }">
        <div v-if="record.type === 'details'" class="consumer-info">
          {{ record.consumer }}
        </div>
      </template>

      <template #refund-reason-cell="{ record }">
        <div v-if="record.type === 'details'" class="reason-info">
          {{ record.refundReason }}
        </div>
      </template>

      <template #refund-remark-cell="{ record }">
        <div v-if="record.type === 'details'" class="remark-info">
          {{ record.refundRemark }}
        </div>
      </template>

      <template #status-cell="{ record }">
        <div v-if="record.type === 'details'" class="status-tag" :class="getStatusClass(record.orderStatus)">
          {{ record.orderStatus }}
        </div>
      </template>

      <template #operations-cell="{ record }">
        <div v-if="record.type === 'details' && record.productIndex === 0" class="operations-cell-content">
          <div class="operation-buttons">
            <!-- 全部订单状态下都可以查看订单详情 -->
            <a-button type="text" size="small">订单详情</a-button>
            
            <!-- 退货申请状态 -->
            <template v-if="record.orderStatus === '退货申请'">
              <a-button type="text" size="small" status="primary">同意退货</a-button>
              <a-button type="text" size="small" status="warning">拒绝申请</a-button>
            </template>
            
            <!-- 退货中状态 -->
            <template v-if="record.orderStatus === '退货中'">
              <a-button type="text" size="small" status="primary">确认收货</a-button>
              <a-button type="text" size="small" status="warning">拒绝收货</a-button>
            </template>
            
            <!-- 关闭订单申请状态 -->
            <template v-if="record.orderStatus === '关闭订单申请'">
              <a-button type="text" size="small" status="primary">同意关闭</a-button>
              <a-button type="text" size="small" status="warning">拒绝关闭</a-button>
            </template>
            
            <!-- 退款全额状态 -->
            <template v-if="record.orderStatus === '退款全额'">
              <a-button type="text" size="small" status="primary">同意退款</a-button>
              <a-button type="text" size="small" status="warning">拒绝退款</a-button>
            </template>
            
            <!-- 已关闭状态下不显示额外操作按钮 -->
          </div>
        </div>
      </template>
 
    </a-table>
    <!-- 分页组件 -->
    <div class="pagination-container">
      <a-pagination :total="originalOrders.length" show-total show-jumper size="small" :page-size="pageSize"
        show-page-size :current="currentPage" @change="handlePageChange" @pageSizeChange="handlePageSizeChange" />
    </div>
  </div>
</template>

<script setup>
import SearchForm from './components/SearchForm/index.vue';
import { ref, reactive, computed, onMounted } from 'vue';
import { IconCopy, IconMessage, IconPlus, IconDown, IconRight, IconLeft, IconCheck } from '@arco-design/web-vue/es/icon';
import { Message } from '@arco-design/web-vue';


// 定义页面路由元信息
definePageMeta({
  name: 'merchant-afterOrder',
  path: '/merchant/order/afterOrder'
})


const showAdvancedSearch = ref(false);

// 表单项定义
const formItems = reactive([
  {
    field: 'orderId',
    label: '退款编号',
    type: 'input',
    placeholder: '请输入',
    span: 1
  },
  {
    field: 'relatedOrderId',
    label: '关联订单编号',
    type: 'input',
    placeholder: '请输入',
    span: 1
  },
  {
    field: 'productInfo',
    label: '商品名称/ID',
    type: 'input',
    placeholder: '请输入',
    span: 1
  },
  {
    field: 'refundType',
    label: '退单类型',
    type: 'select',
    placeholder: '请选择',
    options: [
      { label: '全部', value: '全部' },
      { label: '仅退款', value: '仅退款' },
      { label: '退货退款', value: '退货退款' },
    ],
    defaultValue: '全部',
    span: 1
  },
  {
    field: 'buyerPhone',
    label: '买家手机',
    type: 'input',
    placeholder: '请输入',
    span: 1
  },
  {
    field: 'refundReason',
    label: '退款原因',
    type: 'select',
    placeholder: '全部',
    options: [
      { label: '全部', value: '全部' },
      { label: '商品质量问题', value: '商品质量问题' },
      { label: '商品不符合描述', value: '商品不符合描述' },
      { label: '收到商品破损', value: '收到商品破损' },
      { label: '商品缺货', value: '商品缺货' },
      { label: '买家不想要了', value: '买家不想要了' },
      { label: '其他', value: '其他' }
    ],
    defaultValue: '全部',
    span: 1
  },
  {
    field: 'afterSaleStatus',
    label: '售后状态',
    type: 'select',
    placeholder: '全部',
    options: [
      { label: '全部', value: '全部' },
      { label: '退货申请', value: '退货申请' },
      { label: '退货中', value: '退货中' },
      { label: '关闭订单申请', value: '关闭订单申请' },
      { label: '退款全额', value: '退款全额' },
      { label: '关闭', value: '关闭' }
    ],
    defaultValue: '全部',
    span: 1
  },
  {
    field: 'orderTime',
    label: '提交时间',
    isTimeRange: true,
    timePresetField: 'timePreset',
    startDateField: 'startDate',
    endDateField: 'endDate',
    presetOptions: [
      { label: '今天', value: 'today' },
      { label: '昨天', value: 'yesterday' },
      { label: '近7天', value: '7days' },
      { label: '近30天', value: '30days' },
      { label: '自定义', value: 'custom' }
    ],
    defaultPreset: '7days',
    span: 2
  }
]);

const searchForm = reactive({
  orderId: '',
  relatedOrderId: '',
  productInfo: '',
  buyerPhone: '',
  refundType: '全部',
  refundReason: '全部',
  afterSaleStatus: '全部',
  timePreset: '7days',
  startDate: null,
  endDate: null,
  onlyShowSelected: false
});

// 统计卡片数据
const statsCards = reactive([
  { id: 'all', title: '全部', value: '3,743' },
  { id: 'returnApply', title: '售后审批', value: '12' },
  { id: 'returnRefund', title: '待收货确认', value: '8' },
  { id: 'closeOrderApply', title: '待退款确认', value: '3' },
  { id: 'refundAll', title: '已完结', value: '15' },
  { id: 'closed', title: '拒绝退款', value: '5' }
]);

// 当前选中的统计卡片
const activeStatsCard = ref('all');

// 设置当前选中的统计卡片
const setActiveStatsCard = (id) => {
  activeStatsCard.value = id;
};

// 统计卡片滚动相关
const statsCardsRef = ref(null);

// 滚动统计卡片
const scrollStatsCards = (direction) => {
  if (!statsCardsRef.value) return;
  
  const scrollAmount = 200; // 每次滚动的像素数
  const currentScroll = statsCardsRef.value.scrollLeft;
  
  if (direction === 'left') {
    statsCardsRef.value.scrollTo({
      left: Math.max(0, currentScroll - scrollAmount),
      behavior: 'smooth'
    });
  } else {
    statsCardsRef.value.scrollTo({
      left: currentScroll + scrollAmount,
      behavior: 'smooth'
    });
  }
};

const originalOrders = ref([
  { 
    id: 'TK001',
    relatedOrderId: 'ORD001',
    refundType: '仅退款',
    totalPayable: '29.90',
    paymentDetail: '1**********',
    address: '广东省汕尾市汕尾城区********** 这是一个非常长的地址看看会不会换行或者省略号',
    consumer: '张*',
    orderStatus: '退货申请',
    submitTime: '2023-07-01 10:30:00',
    refundReason: '商品质量问题',
    refundRemark: '收到商品有破损',
    deliveryDetail: '等待商家确认',
    isShippable: true,
    hasAddressEdit: true,
    hasLogisticsCheck: false,
    orderTime: '2023-07-01 10:30:00',
    products: [
      {
        productId: 'PROD101',
        productName: '女士舒适纯棉短袖T恤',
        imageUrl: 'https://img.freepik.com/free-psd/white-t-shirt-mockup_125540-697.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379',
        color: '白色',
        size: 'M',
        price: '29.90',
        quantity: 1,
        merchantCode: 'M12345',
        returnLink: '申请退款'
      }
    ]
  },
  {
    id: 'TK002',
    relatedOrderId: 'ORD002',
    refundType: '退货退款',
    totalPayable: '159.00',
    paymentDetail: '2**********',
    address: '浙江省杭州市西湖区**********',
    consumer: '李*',
    orderStatus: '退货中',
    submitTime: '2023-07-02 14:45:00',
    refundReason: '商品不符合描述',
    refundRemark: '尺寸偏小',
    deliveryDetail: '买家已退货，等待商家确认收货',
    hasAddressEdit: false,
    hasLogisticsCheck: true,
    orderTime: '2023-07-02 14:45:00',
    products: [
      {
        productId: 'PROD201',
        productName: '男士休闲运动鞋',
        imageUrl: 'https://img.freepik.com/free-photo/pair-trainers_144627-3799.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379',
        color: '黑色',
        size: '42',
        price: '159.00',
        quantity: 1,
        merchantCode: 'M23456',
        returnLink: '退款中'
      }
    ]
  },
  {
    id: 'TK003',
    relatedOrderId: 'ORD003',
    refundType: '仅退款',
    totalPayable: '49.90',
    paymentDetail: '3**********',
    address: '江苏省南京市鼓楼区**********',
    consumer: '王*',
    orderStatus: '退款全额',
    submitTime: '2023-07-03 09:15:00',
    refundReason: '商品缺货',
    refundRemark: '下单后客服通知缺货',
    deliveryDetail: '等待商家确认退款',
    hasAddressEdit: false,
    hasLogisticsCheck: false,
    orderTime: '2023-07-03 09:15:00',
    products: [
      {
        productId: 'PROD301',
        productName: '儿童益智拼图玩具',
        imageUrl: 'https://img.freepik.com/free-photo/flat-lay-colorful-childrens-toys_23-2148354805.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379',
        color: '多彩',
        size: '标准',
        price: '49.90',
        quantity: 1,
        merchantCode: 'M34567',
        returnLink: '退款中'
      }
    ]
  },
  {
    id: 'TK004',
    relatedOrderId: 'ORD004',
    refundType: '退货退款',
    totalPayable: '79.90',
    paymentDetail: '4**********',
    address: '上海市浦东新区**********',
    consumer: '赵*',
    orderStatus: '关闭订单申请',
    submitTime: '2023-07-04 16:20:00',
    refundReason: '买家不想要了',
    refundRemark: '买家申请取消订单',
    deliveryDetail: '等待商家确认',
    hasAddressEdit: true,
    hasLogisticsCheck: true,
    orderTime: '2023-07-04 16:20:00',
    products: [
      {
        productId: 'PROD401',
        productName: '智能手环',
        imageUrl: 'https://img.freepik.com/free-photo/modern-stationary-collection-arrangement_23-2149309643.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379',
        color: '黑色',
        size: '均码',
        price: '79.90',
        quantity: 1,
        merchantCode: 'M45678',
        returnLink: '处理中'
      }
    ]
  },
  {
    id: 'TK005',
    relatedOrderId: 'ORD005',
    refundType: '仅退款',
    totalPayable: '129.00',
    paymentDetail: '5**********',
    address: '广东省广州市天河区**********',
    consumer: '陈*',
    orderStatus: '关闭',
    submitTime: '2023-07-05 11:30:00',
    refundReason: '其他',
    refundRemark: '商家同意退款',
    deliveryDetail: '售后已完成',
    hasAddressEdit: false,
    hasLogisticsCheck: false,
    orderTime: '2023-07-05 11:30:00',
    products: [
      {
        productId: 'PROD501',
        productName: '便携式蓝牙音箱',
        imageUrl: 'https://img.freepik.com/free-photo/wireless-speaker-white-background_53876-146215.jpg?size=338&ext=jpg&uid=R77081325&ga=GA1.1.1880011253.1687651379',
        color: '蓝色',
        size: '标准',
        price: '129.00',
        quantity: 1,
        merchantCode: 'M56789',
        returnLink: '已完成'
      }
    ]
  }
]);

const currentPage = ref(1);
const pageSize = ref(10);

const paginatedOrders = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return originalOrders.value.slice(start, end);
});

const tableData = computed(() => {
  const data = [];
  paginatedOrders.value.forEach(order => {
    const productCount = order.products.length;
    data.push({ // 表头行数据
      rowId: `${order.id}-header`, // 表头唯一ID
      type: 'header',
      originalId: order.id,
      relatedOrderId: order.relatedOrderId,
      orderTime: order.orderTime,
      productCount: productCount, // 用于合并单元格计算
    });
    // 详情行数据 (每个商品一行)
    order.products.forEach((product, productIndex) => {
      data.push({
        rowId: `${order.id}-product-${productIndex}`, // 商品行唯一ID
        type: 'details',
        productIndex: productIndex, // 商品索引，用于合并单元格
        productCount: productCount, // 商品总数，用于合并单元格
        // 订单级信息 (用于合并单元格或在详情行显示)
        originalId: order.id,
        relatedOrderId: order.relatedOrderId,
        refundType: order.refundType,
        totalPayable: order.totalPayable,
        paymentDetail: order.paymentDetail,
        address: order.address,
        consumer: order.consumer,
        orderStatus: order.orderStatus,
        submitTime: order.submitTime,
        refundReason: order.refundReason,
        refundRemark: order.refundRemark,
        deliveryDetail: order.deliveryDetail,
        isShippable: order.isShippable, // 订单级别的可发货状态
        hasAddressEdit: order.hasAddressEdit,
        hasLogisticsCheck: order.hasLogisticsCheck,
        orderTime: order.orderTime,
        // 商品级信息
        productId: product.productId,
        productName: product.productName,
        imageUrl: product.imageUrl,
        color: product.color,
        size: product.size,
        price: product.price,
        quantity: product.quantity,
        merchantCode: product.merchantCode,
        returnLink: product.returnLink
      });
    });
  });
  return data;
});

// --- 表格配置 ---
const selectedRowKeys = ref([]);
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: false, 
  selectedRowKeys: selectedRowKeys,
  onlyCurrent: false,
});

const selectableRowKeysOnPage = computed(() => {
  return tableData.value
    .filter(row => row.type === 'details')
    .map(row => row.rowId);
});

const isAllSelectedOnPage = computed(() => {
  const pageKeys = selectableRowKeysOnPage.value;
  if (pageKeys.length === 0) return false;
  return pageKeys.every(key => selectedRowKeys.value.includes(key));
});

const isIndeterminate = computed(() => {
  const pageKeys = selectableRowKeysOnPage.value;
  const selectedOnPageCount = pageKeys.filter(key => selectedRowKeys.value.includes(key)).length;
  return selectedOnPageCount > 0 && selectedOnPageCount < pageKeys.length;
});

const handleSelectAllOnPage = (checked) => {
  const pageKeys = selectableRowKeysOnPage.value;
  if (checked) {
    const newKeys = [...new Set([...selectedRowKeys.value, ...pageKeys])];
    selectedRowKeys.value = newKeys;
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter(key => !pageKeys.includes(key));
  }
};

const columns = reactive([
  {
    title: '商品信息',
    slotName: 'productInfo-cell',
    width: 380,
  },
  { title: '退单类型', slotName: 'refund-type-cell', width: 100, align: 'center' },
  { title: '单价/数量', slotName: 'priceQuantity-cell', width: 120, align: 'center' },
  { title: '退款金额', slotName: 'payment-cell', width: 150, align: 'right' },
  { title: '买家', slotName: 'consumer-cell', width: 100 },
  { title: '提交时间', slotName: 'submit-time-cell', width: 150 },
  { title: '退款原因', slotName: 'refund-reason-cell', width: 150, align: 'center' },
  { title: '退款备注', slotName: 'refund-remark-cell', width: 150 },
  { title: '状态', slotName: 'status-cell', width: 100 },
  { title: '操作', slotName: 'operations-cell', width: 150, align: 'center', fixed: 'right' },
]);

// 用于合并单元格的 Span 方法
const spanMethod = ({ record, column, rowIndex }) => {
  if (record.type === 'header') { // 表头行: 第一列合并所有列
    if (column.slotName === 'productInfo-cell') {
      return { rowspan: 1, colspan: columns.length };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  if (record.type === 'details') {
    // 详情行: 对特定列进行垂直合并
    const isFirstProduct = record.productIndex === 0;
    const rowspan = isFirstProduct ? record.productCount : 0;
    // 需要垂直合并的列 (订单级别信息)
    const colsToSpan = [
      'refund-type-cell',
      'payment-cell',
      'consumer-cell',
      'status-cell',
      'submit-time-cell',
      'refund-reason-cell',
      'refund-remark-cell',
      'operations-cell',
    ];

    if (colsToSpan.includes(column.slotName)) {
      return { rowspan: rowspan, colspan: 1 };
    }
  }
  // 其他单元格不合并
  return { rowspan: 1, colspan: 1 };
};

// 复制订单 ID 函数
const copyOrderId = async (id) => {
  try {
    await navigator.clipboard.writeText(id);
    Message.success(`订单号 ${id} 已复制`);
  } catch (err) {
    Message.error('复制失败');
    console.error('Failed to copy: ', err);
  }
};

// --- 分页处理程序 ---
const handlePageChange = (page) => {
  currentPage.value = page;
};

const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; 
};

// 获取指定字段的选项
const getOptionsForField = (fieldName) => {
  const item = formItems.find(item => item.field === fieldName);
  return item ? item.options : [];
};

// 获取时间预设选项
const getTimePresetOptions = () => {
  const item = formItems.find(item => item.isTimeRange);
  return item ? item.presetOptions : [];
};

// 根据订单状态返回对应的CSS类名
const getStatusClass = (status) => {
  const statusMap = {
    '退货申请': 'status-pending',
    '退货中': 'status-processing',
    '关闭订单申请': 'status-warning',
    '退款全额': 'status-success',
    '关闭': 'status-closed'
  };
  return statusMap[status] || 'status-default';
};

const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value;
};

const handleSearch = () => {
  currentPage.value = 1;
  // TODO: 实际搜索逻辑
  console.log('搜索条件:', searchForm);
};

const resetSearch = () => {
  // 重置页码
  currentPage.value = 1;
  console.log('表单已重置');
};
</script>

<style scoped lang="less">
// css样式写在index.css 防止单文件代码太长 AI读取不了
@import "./afterOrder.css";
</style>