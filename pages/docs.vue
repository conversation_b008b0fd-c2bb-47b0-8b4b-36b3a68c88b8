<template>
  <div class="docs-container">
    <h1>文档导航</h1>
    
    <a-card class="docs-card">
      <template #title>思维导图</template>
      <a-descriptions :data="mindmapData" layout="vertical" bordered />
      <div class="mindmap-link">
        <a-link href="https://kdocs.cn/l/cjus4BfABsGS" target="_blank">
          <template #icon>
            <IconLink />
          </template>
          查看思维导图：聚灵云4.0
        </a-link>
      </div>
    </a-card>
    
    <a-divider />
    
    <a-card class="docs-card">
      <template #title>系统文档</template>
      <a-table :columns="columns" :data="tableData" :pagination="false" />
    </a-card>
  </div>
</template>

<script setup>
import { IconLink } from '@arco-design/web-vue/es/icon';

const mindmapData = [
  {
    label: '文档名称',
    value: '聚灵云4.0思维导图'
  },
  {
    label: '更新时间',
    value: '2025-03-26'
  },
  {
    label: '文档类型',
    value: '思维导图'
  }
];

const columns = [
  {
    title: '文档名称',
    dataIndex: 'name',
  },
  {
    title: '文档类型',
    dataIndex: 'type',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    slotName: 'operation',
  },
];

const tableData = [
  {
    key: '1',
    name: '系统架构说明',
    type: '技术文档',
    updateTime: '2025-03-25',
    link: '#'
  },
  {
    key: '2',
    name: '用户手册',
    type: '使用手册',
    updateTime: '2025-03-24',
    link: '#'
  },
  {
    key: '3',
    name: 'API接口文档',
    type: '接口文档',
    updateTime: '2025-03-23',
    link: '#'
  },
  {
    key: '4',
    name: '数据库设计',
    type: '技术文档',
    updateTime: '2025-03-22',
    link: '#'
  },
  {
    key: '5',
    name: '聚灵云4.0思维导图',
    type: '思维导图',
    updateTime: '2025-03-26',
    link: 'https://kdocs.cn/l/cjus4BfABsGS'
  }
];
</script>

<style scoped>
.docs-container {
  padding: 20px;
}

.docs-card {
  margin-bottom: 20px;
}

.mindmap-link {
  margin-top: 20px;
  font-size: 16px;
}
</style>
