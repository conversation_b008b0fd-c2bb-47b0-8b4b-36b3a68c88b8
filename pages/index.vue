<template>
  <div class="home-container">
    <div class="content" :class="{'content-mobile': isMobile}">
      <h1>欢迎使用聚灵云4.0</h1>
      <p>请选择您要进入的系统</p>



      <div class="card-container">
        <a-row :gutter="isMobile ? [16, 16] : 24">
          <a-col :xs="24" :sm="24" :md="12" :lg="8">
            <a-card hoverable @click="navigateTo('/master/login')">
              <template #cover>
                <div class="card-icon">
                  <IconStorage />
                </div>
              </template>
              <a-card-meta title="中控台">
                <template #description>管理系统和用户</template>
              </a-card-meta>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8">
            <!-- @click="navigateTo('/website/one/index')"  -->
            <a-card hoverable @click="handleClick">
              <template #cover>
                <div class="card-icon">
                  <IconGift />
                </div>
              </template>
              <a-card-meta title="官网端">
                <template #description>官网端</template>
              </a-card-meta>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8">
            <a-card hoverable @click="navigateTo('/mall/')">
              <template #cover>
                <div class="card-icon">
                  <IconTag />
                </div>
              </template>
              <a-card-meta title="商城端">
                <template #description>管理商城和会员</template>
              </a-card-meta>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24" style="margin-top: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="8">
            <a-card hoverable @click="navigateTo('/provider/index')">
              <template #cover>
                <div class="card-icon">
                  <IconDesktop />
                </div>
              </template>
              <a-card-meta title="服务商端">
                <template #description>管理服务和预约</template>
              </a-card-meta>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8">
            <a-card hoverable @click="navigateTo('/supplier/')">
              <template #cover>
                <div class="card-icon">
                  <IconGift />
                </div>
              </template>
              <a-card-meta title="供应链端">
                <template #description>管理产品和库存</template>
              </a-card-meta>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="8">
            <a-card hoverable @click="navigateTo('/merchant/login')">
              <template #cover>
                <div class="card-icon">
                  <IconApps />
                </div>
              </template>
              <a-card-meta title="商家端">
                <template #description>管理商品和订单</template>
              </a-card-meta>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24" style="margin-top: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="8">
            <a-card hoverable @click="navigateTo('/h5/SecurityCode/InspectionCenter')">
              <template #cover>
                <div class="card-icon">
                  <IconH5 />
                </div>
              </template>
              <a-card-meta title="溯源码移动端">
                <template #description>溯源码</template>
              </a-card-meta>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <a-divider />

      <h2>微信支付测试</h2>
      <a-card>
        <a-space>
          <a-button type="primary" @click="testWechatPay" :loading="paymentLoading">
            <IconGift />
            测试微信支付
          </a-button>
          <a-button v-if="paymentResult.codeUrl" @click="showQRCode = true">
            查看二维码
          </a-button>
        </a-space>

        <div v-if="paymentResult.outTradeNo" style="margin-top: 16px;">
          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="订单号">{{ paymentResult.outTradeNo }}</a-descriptions-item>
            <a-descriptions-item label="金额">{{ (paymentResult.amount / 100).toFixed(2) }}元</a-descriptions-item>
            <a-descriptions-item label="描述">{{ paymentResult.description }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </a-card>

      <a-divider />

      <h2>系统文档</h2>
      <a-card>
        <a-tabs default-active-key="1">
          <a-tab-pane key="1" title="文档列表">
            <a-table :columns="columns" :data="tableData" :pagination="false" :scroll="isMobile ? { x: '100%' } : {}" :class="{'mobile-table': isMobile}">
              <template #operation="{ record }">
                <a-link :href="record.link" target="_blank">
                  <IconLink />
                  查看
                </a-link>
              </template>
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="2" title="开发者文档">
            <GitlabReadme 
              projectUrl="https://ali.git.8080bl.com/julingcloud4/nuxtjs" 
              token="ZmS9Cuoz3CzsC715YyDM" 
              branch="master" 
              filePath="README.md" 
            />
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <a-divider />
      
      <h2>代码更新日志</h2>
      <a-card :style="{ width: '100%' }">
        <CommitHistory />
      </a-card>
    </div>
  </div>



  <!-- 微信支付二维码模态框 -->
  <a-modal v-model:visible="showQRCode" @cancel="showQRCode = false" :footer="false" :width="isMobile ? '95%' : '400px'">
    <template #title>
      <div style="text-align: center;">
        <span>微信支付二维码</span>
      </div>
    </template>
    <div style="text-align: center; padding: 20px;">
      <div v-if="paymentResult.codeUrl" id="qrcode" style="margin: 0 auto;"></div>
      <p style="margin-top: 16px; color: #666;">请使用微信扫码支付</p>
      <p style="color: #999; font-size: 12px;">金额：{{ (paymentResult.amount / 100).toFixed(2) }}元</p>
    </div>
  </a-modal>

  <a-modal v-model:visible="visible" @ok="handleOk" @cancel="handleCancel" :footer="false" :width="isMobile ? '95%' : '700px'">
    <template #title>
      <div class="website-select-title">
        <span>请选择您要进入的官网</span>
      </div>
    </template>
    <div class="website-select-container">
      <div class="website-select-grid">
        <div class="website-option" @click="selectWebsite(1)">
          <div class="website-preview">
            <a-image src="'/assets/images/website-preview-1.jpg'" alt="第一套官网预览" fallback="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp" />
          </div>
          <div class="website-info">
            <h3>第一套 - 企业商务</h3>
            <p>适合大型企业、商务服务类网站</p>
          </div>
          <div class="website-tag">推荐</div>
        </div>
        <div class="website-option" @click="selectWebsite(2)">
          <div class="website-preview">
            <a-image :src="'/assets/images/website-preview-2.jpg'" alt="第二套官网预览" fallback="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp" />
          </div>
          <div class="website-info">
            <h3>第二套 - 创意设计</h3>
            <p>适合设计、创意、艺术类网站</p>
          </div>
        </div>
        <div class="website-option" @click="selectWebsite(3)">
          <div class="website-preview">
            <a-image :src="'/assets/images/website-preview-3.jpg'" alt="第三套官网预览" fallback="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp" />
          </div>
          <div class="website-info">
            <h3>第三套 - 科技创新</h3>
            <p>适合科技、IT、创新类网站</p>
          </div>
        </div>
        <div class="website-option" @click="selectWebsite(4)">
          <div class="website-preview">
            <a-image :src="'/assets/images/website-preview-4.jpg'" alt="第四套官网预览" fallback="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp" />
          </div>
          <div class="website-info">
            <h3>第四套 - 电子商务</h3>
            <p>适合电商、产品展示类网站</p>
          </div>
          <div class="website-tag new">新增</div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { useRuntimeConfig } from '#imports';
import {
  IconH5,
  IconStorage,
  IconApps,
  IconDesktop,
  IconGift,
  IconTag,
  IconLink,
  IconDelete,
  IconQuestionCircle,
  IconExclamationCircle,
} from "@arco-design/web-vue/es/icon";
import CommitHistory from "~/components/base/CommitHistory.vue";
import { onMounted, onUnmounted, ref, nextTick } from 'vue';

// 导航函数
const navigateTo = (path) => {
  window.location.href = path;
};

const runtimeConfig = useRuntimeConfig();
const visible = ref(false);
const isMobile = ref(false);

// 微信支付相关状态
const paymentLoading = ref(false);
const showQRCode = ref(false);
const paymentResult = ref({
  codeUrl: '',
  outTradeNo: '',
  amount: 0,
  description: ''
});

// 检测设备是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

    const handleClick = () => {
      navigateTo('/website/index')
      return
      visible.value = true;
      // navigateTo('/website/one/index');
    };
    
    const handleOk = () => {
      visible.value = false;
    };
    
    const handleCancel = () => {
      visible.value = false;
    };
    
    const selectWebsite = (id) => {
      visible.value = false;
      // 根据选择的官网ID进行跳转
      switch(id) {
        case 1:
          navigateTo('/website/one/index');
          break;
        case 2:
          navigateTo('/website/two/index');
          break;
        case 3:
          navigateTo('/website/three/index');
          break;
        case 4:
          navigateTo('/website/four/index');
          break;
        default:
          navigateTo('/website/one/index');
      }
    }

// 测试微信支付
const testWechatPay = async () => {
  try {
    paymentLoading.value = true;

    const response = await fetch('https://v4api.ioa.8080bl.com/api/v1/master/wechat-pay/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        description: '微信支付测试订单'
      })
    });

    const result = await response.json();

    if (result.success) {
      paymentResult.value = result.data;

      // 显示成功消息
      window.$message?.success('支付订单创建成功！');

      // 生成二维码
      await generateQRCode(result.data.codeUrl);

      // 显示二维码
      showQRCode.value = true;
    } else {
      window.$message?.error(result.message || '创建支付订单失败');
    }
  } catch (error) {
    console.error('测试微信支付失败:', error);
    window.$message?.error('网络请求失败，请检查服务器是否启动');
  } finally {
    paymentLoading.value = false;
  }
};

// 生成二维码
const generateQRCode = async (codeUrl) => {
  try {
    // 等待DOM更新
    await nextTick();

    // 清空之前的二维码
    const qrElement = document.getElementById('qrcode');
    if (qrElement) {
      qrElement.innerHTML = '';

      // 创建canvas元素
      const canvas = document.createElement('canvas');
      canvas.width = 200;
      canvas.height = 200;

      // 使用简单的二维码显示方式
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, 200, 200);

      ctx.fillStyle = '#000000';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('微信支付二维码', 100, 50);
      ctx.fillText('请使用微信扫码支付', 100, 100);
      ctx.fillText('二维码URL:', 100, 130);

      // 显示URL的一部分
      const shortUrl = codeUrl.length > 30 ? codeUrl.substring(0, 30) + '...' : codeUrl;
      ctx.font = '10px Arial';
      ctx.fillText(shortUrl, 100, 150);

      qrElement.appendChild(canvas);

      // 同时显示可点击的链接
      const linkDiv = document.createElement('div');
      linkDiv.style.marginTop = '10px';
      linkDiv.innerHTML = `<a href="${codeUrl}" target="_blank" style="color: #1890ff; text-decoration: underline;">点击打开支付链接</a>`;
      qrElement.appendChild(linkDiv);
    }
  } catch (error) {
    console.error('生成二维码失败:', error);
    window.$message?.error('生成二维码失败');
  }
};

// 清理本地缓存函数
const clearLocalCache = () => {
  try {
    // 清理localStorage
    localStorage.clear();
    
    // 清理sessionStorage
    sessionStorage.clear();
    
    // 清理所有Cookie
    const cookies = document.cookie.split(";");
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
    }
    
    // 显示成功消息
    window.$message.success("本地缓存已成功清理！");
    
    // 延迟1秒后刷新页面以应用更改
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  } catch (error) {
    // 显示错误消息
    window.$message.error("清理缓存时出错：" + error.message);
  }
};



definePageMeta({
  layout: false,
});
// 文档表格列定义
const columns = [
  {
    title: "文档名称",
    dataIndex: "name",
  },
  {
    title: "文档类型",
    dataIndex: "type",
  },
  {
    title: "更新时间",
    dataIndex: "updateTime",
  },
  {
    title: "操作",
    dataIndex: "operation",
    slotName: "operation",
  },
];

// 文档表格数据
const tableData = [
  {
    key: "1",
    name: "聚灵云4.0思维导图",
    type: "思维导图",
    updateTime: "2025-03-26",
    link: "https://kdocs.cn/l/cjus4BfABsGS",
  },
  {
    key: "2",
    name: "系统使用手册",
    type: "PDF文档",
    updateTime: "2025-03-25",
    link: "#",
  },
];
</script>

<style scoped>
/* 官网选择弹窗样式 */
.website-select-title {
  font-size: 18px;
  font-weight: bold;
  color: #1d2129;
  text-align: center;
  margin-bottom: 10px;
}

.website-select-container {
  padding: 10px 0;
}

.website-select-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.website-option {
  position: relative;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.website-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-color: #165dff;
}

.website-preview {
  height: 160px;
  overflow: hidden;
}

.website-preview :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.website-option:hover .website-preview :deep(img) {
  transform: scale(1.05);
}

.website-info {
  padding: 12px 15px;
  background-color: #fff;
}

.website-info h3 {
  font-size: 16px;
  font-weight: bold;
  margin: 0 0 5px 0;
  color: #1d2129;
}

.website-info p {
  font-size: 14px;
  color: #86909c;
  margin: 0;
}

.website-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #165dff;
  color: white;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(22, 93, 255, 0.3);
}

.website-tag.new {
  background-color: #ff7d00;
  box-shadow: 0 2px 6px rgba(255, 125, 0, 0.3);
}
</style>

<style scoped>
.home-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  width: 80%;
  max-width: 1200px;
  padding: 40px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-mobile {
  width: 95%;
  padding: 20px;
}

h1 {
  text-align: center;
  margin-bottom: 8px;
  color: #1d2129;
  font-size: clamp(1.5rem, 5vw, 2rem);
}

p {
  text-align: center;
  margin-bottom: 32px;
  color: #4e5969;
}

h2 {
  margin-top: 32px;
  margin-bottom: 16px;
  color: #1d2129;
  font-size: clamp(1.2rem, 4vw, 1.5rem);
}

.card-container {
  margin-top: 32px;
}

/* 移动端表格样式 */
.mobile-table :deep(.arco-table-container) {
  overflow-x: auto;
}

.mobile-table :deep(.arco-table-th) {
  white-space: nowrap;
  min-width: 100px;
}

@media screen and (max-width: 767px) {
  .card-container {
    margin-top: 16px;
  }
  
  h1 {
    margin-bottom: 4px;
  }
  
  p {
    margin-bottom: 16px;
  }
  
  h2 {
    margin-top: 24px;
    margin-bottom: 12px;
  }
}

.card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background-color: #f2f3f5;
  font-size: 48px;
  color: #165dff;
}
</style>
