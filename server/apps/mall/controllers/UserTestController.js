/**
 * 商城用户测试控制器
 * 仅用于开发和调试目的，生产环境应禁用
 */
const MallBaseController = require('./base/MallBaseController');
const bcrypt = require('bcryptjs');
const redisUtil = require('../../../core/utils/RedisUtil');

class UserTestController extends MallBaseController {
  constructor(prisma) {
    super(prisma);
  }

  /**
   * 测试手机号查询（仅用于调试）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testPhoneQuery(req, res) {
    try {
      // 仅在开发环境中可用
      if (process.env.NODE_ENV === 'production') {
        return this.fail(res, '该接口仅用于开发和测试', 403);
      }
      
      const { phone } = req.query;
      
      if (!phone) {
        return this.fail(res, '手机号不能为空', 400);
      }
      
      // 查询用户
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status
          FROM "base"."mall_user" 
          WHERE "phone" = ${phone}
          AND "deleted_at" IS NULL
        `;
        
        return this.success(res, { users }, '查询成功');
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        return this.fail(res, '查询失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('测试手机号查询过程中出错:', error);
      return this.fail(res, '查询失败，请稍后再试', 500);
    }
  }

  /**
   * 测试登录过程（仅用于调试）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testLogin(req, res) {
    try {
      // 仅在开发环境中可用
      if (process.env.NODE_ENV === 'production') {
        return this.fail(res, '该接口仅用于开发和测试', 403);
      }
      
      const { username, password } = req.body;
      
      if (!username || !password) {
        return this.fail(res, '用户名和密码不能为空', 400);
      }
      
      // 查询用户
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, password, nickname, phone, email, avatar, status
          FROM "base"."mall_user" 
          WHERE ("username" = ${username} OR "phone" = ${username})
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在', 400);
        }
        
        const user = users[0];
        
        // 验证密码
        const isPasswordValid = await bcrypt.compare(password, user.password);
        
        return this.success(res, {
          user: {
            id: user.id.toString(),
            username: user.username,
            nickname: user.nickname,
            phone: user.phone,
            email: user.email,
            avatar: user.avatar,
            status: user.status
          },
          passwordCheck: {
            inputPassword: password,
            hashedPassword: user.password,
            isValid: isPasswordValid
          }
        }, '测试登录成功');
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        return this.fail(res, '测试登录失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('测试登录过程中出错:', error);
      return this.fail(res, '测试登录失败，请稍后再试', 500);
    }
  }

  /**
   * 测试密码验证（仅用于调试）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testPassword(req, res) {
    try {
      // 仅在开发环境中可用
      if (process.env.NODE_ENV === 'production') {
        return this.fail(res, '该接口仅用于开发和测试', 403);
      }
      
      const { password } = req.query;
      
      if (!password) {
        return this.fail(res, '密码不能为空', 400);
      }
      
      // 生成盐值和哈希密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);
      
      return this.success(res, {
        originalPassword: password,
        salt,
        hashedPassword
      }, '密码哈希生成成功');
    } catch (error) {
      console.error('测试密码验证过程中出错:', error);
      return this.fail(res, '密码哈希生成失败，请稍后再试', 500);
    }
  }

  /**
   * 测试 Redis 连接（仅用于调试）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testRedis(req, res) {
    try {
      // 仅在开发环境中可用
      if (process.env.NODE_ENV === 'production') {
        return this.fail(res, '该接口仅用于开发和测试', 403);
      }
      
      // 测试 Redis 连接
      const isConnected = redisUtil.isConnected;
      const testResult = await redisUtil.testConnection();
      
      return this.success(res, {
        isConnected,
        testResult,
        redisConfig: {
          host: process.env.REDIS_HOST,
          port: process.env.REDIS_PORT,
          db: process.env.REDIS_DB
        }
      }, testResult ? 'Redis连接正常' : 'Redis连接异常');
    } catch (error) {
      console.error('测试Redis连接过程中出错:', error);
      return this.fail(res, '测试Redis连接失败，请稍后再试', 500);
    }
  }

  /**
   * 创建测试订单
   */
  async createTestOrder(req, res) {
    try {
      const { userId } = req.body;

      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }

      // 生成订单号
      const orderSn = 'TEST' + Date.now();

      // 创建测试订单
      const order = await this.prisma.orders.create({
        data: {
          order_sn: orderSn,
          user_id: BigInt(userId),
          order_status: 0, // 待支付
          payment_status: 0, // 未支付
          payment_method_id: 1, // 微信支付
          total_amount: '0.01', // 测试金额1分钱
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });

      return this.success(res, '测试订单创建成功', {
        orderId: order.id.toString(),
        orderSn: order.order_sn,
        totalAmount: order.total_amount,
        orderStatus: order.order_status,
        paymentStatus: order.payment_status
      });
    } catch (error) {
      console.error('创建测试订单失败:', error);
      return this.fail(res, '创建测试订单失败', 500);
    }
  }

  /**
   * 查看订单详情
   */
  async getOrderDetail(req, res) {
    try {
      const { orderId } = req.query;

      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 查询订单详情
      const order = await this.prisma.orders.findUnique({
        where: {
          id: BigInt(orderId)
        }
      });

      if (!order) {
        return this.fail(res, '订单不存在', 404);
      }

      return this.success(res, '获取订单详情成功', {
        orderId: order.id.toString(),
        orderSn: order.order_sn,
        userId: order.user_id.toString(),
        orderStatus: order.order_status,
        paymentStatus: order.payment_status,
        paymentMethodId: order.payment_method_id,
        totalAmount: order.total_amount,
        createdAt: order.created_at.toString(),
        updatedAt: order.updated_at.toString()
      });
    } catch (error) {
      console.error('获取订单详情失败:', error);
      return this.fail(res, '获取订单详情失败', 500);
    }
  }
}

module.exports = UserTestController;
