const BaseController = require('../../../core/controllers/BaseController');
const IpLocationService = require('../services/IpLocationService');

/**
 * IP地址归属地查询控制器
 * 提供IP地址归属地查询相关接口
 */
class IpLocationController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super();
    this.ipLocationService = new IpLocationService();
  }

  /**
   * 查询当前用户IP地址归属地
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async queryCurrentIp(req, res) {
    try {
      // 获取客户端IP地址
      const ip = this.ipLocationService.getClientIp(req);
      
      // 查询IP地址归属地
      const location = this.ipLocationService.query(ip);
      
      // 返回结果
      res.status(200).json({
        code: 200,
        message: '查询成功',
        data: {
          ip,
          country: location.country,
          area: location.area,
          location: `${location.country} ${location.area}`
        }
      });
    } catch (error) {
      console.error('查询IP地址归属地失败:', error);
      this.fail(res, error.message || '查询IP地址归属地失败', 500);
    }
  }

  /**
   * 查询指定IP地址归属地
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async queryIp(req, res) {
    try {
      const { ip } = req.params;
      
      // 验证IP地址格式
      if (!this.isValidIp(ip)) {
        return this.fail(res, '无效的IP地址格式', 400);
      }
      
      // 查询IP地址归属地
      const location = this.ipLocationService.query(ip);
      
      // 返回结果
      res.status(200).json({
        code: 200,
        message: '查询成功',
        data: {
          ip,
          country: location.country,
          area: location.area,
          location: `${location.country} ${location.area}`
        }
      });
    } catch (error) {
      console.error('查询IP地址归属地失败:', error);
      this.fail(res, error.message || '查询IP地址归属地失败', 500);
    }
  }

  /**
   * 验证IP地址格式
   * @param {string} ip - IP地址
   * @returns {boolean} - 是否有效
   */
  isValidIp(ip) {
    const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    if (!ipRegex.test(ip)) {
      return false;
    }
    
    const parts = ip.split('.');
    for (let i = 0; i < 4; i++) {
      const part = parseInt(parts[i], 10);
      if (part < 0 || part > 255) {
        return false;
      }
    }
    
    return true;
  }
}

module.exports = IpLocationController;