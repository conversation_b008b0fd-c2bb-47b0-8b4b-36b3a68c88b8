const BaseController = require('../../../core/controllers/BaseController');
const OrderPaymentService = require('../services/OrderPaymentService');
const Joi = require('joi');

/**
 * 商城订单支付控制器
 */
class OrderPaymentController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.orderPaymentService = new OrderPaymentService(prisma);
  }

  /**
   * 创建支付订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createPayment(req, res) {
    try {
      // 验证请求参数
      const schema = Joi.object({
        orderId: Joi.string().required().messages({
          'any.required': '订单ID不能为空',
          'string.empty': '订单ID不能为空'
        }),
        paymentMethodId: Joi.number().integer().min(1).max(5).required().messages({
          'any.required': '支付方式不能为空',
          'number.base': '支付方式必须是数字',
          'number.integer': '支付方式必须是整数',
          'number.min': '支付方式无效',
          'number.max': '支付方式无效'
        }),
        useBalance: Joi.boolean().optional().default(false).messages({
          'boolean.base': '使用余额标识必须是布尔值'
        }),
        balanceAmount: Joi.number().min(0).optional().default(0).messages({
          'number.base': '余额金额必须是数字',
          'number.min': '余额金额不能为负数'
        })
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }

      const { orderId, paymentMethodId, useBalance, balanceAmount } = value;
      const userId = req.user.id;

      // 创建支付
      const result = await this.orderPaymentService.createPayment(orderId, paymentMethodId, userId, {
        useBalance,
        balanceAmount
      });

      return this.success(res, result, '创建支付成功');
    } catch (error) {
      console.error('创建支付失败:', error);
      return this.fail(res, error.message || '创建支付失败', 500);
    }
  }

  /**
   * 查询支付状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async queryPaymentStatus(req, res) {
    try {
      const { paymentSn } = req.params;
      
      if (!paymentSn) {
        return this.fail(res, '支付流水号不能为空', 400);
      }

      const userId = req.user.id;

      // 查询支付状态
      const result = await this.orderPaymentService.queryPaymentStatus(paymentSn, userId);

      return this.success(res, result, '查询支付状态成功');
    } catch (error) {
      console.error('查询支付状态失败:', error);
      return this.fail(res, error.message || '查询支付状态失败', 500);
    }
  }

  /**
   * 取消支付
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async cancelPayment(req, res) {
    try {
      const { paymentSn } = req.params;
      
      if (!paymentSn) {
        return this.fail(res, '支付流水号不能为空', 400);
      }

      const userId = req.user.id;

      // 取消支付
      const result = await this.orderPaymentService.cancelPayment(paymentSn, userId);

      return this.success(res, result, '取消支付成功');
    } catch (error) {
      console.error('取消支付失败:', error);
      return this.fail(res, error.message || '取消支付失败', 500);
    }
  }

  /**
   * 获取订单支付记录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrderPaymentRecords(req, res) {
    try {
      const { orderId } = req.params;
      
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      const userId = req.user.id;

      // 获取支付记录
      const result = await this.orderPaymentService.getOrderPaymentRecords(orderId, userId);

      return this.success(res, result, '获取支付记录成功');
    } catch (error) {
      console.error('获取支付记录失败:', error);
      return this.fail(res, error.message || '获取支付记录失败', 500);
    }
  }

  /**
   * 获取支付统计信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getPaymentStatistics(req, res) {
    try {
      const userId = req.user.id;
      const { startTime, endTime } = req.query;

      // 获取支付统计
      const result = await this.orderPaymentService.getPaymentStatistics(userId, {
        startTime: startTime ? parseInt(startTime) : undefined,
        endTime: endTime ? parseInt(endTime) : undefined
      });

      return this.success(res, result, '获取支付统计成功');
    } catch (error) {
      console.error('获取支付统计失败:', error);
      return this.fail(res, error.message || '获取支付统计失败', 500);
    }
  }
}

module.exports = OrderPaymentController;
