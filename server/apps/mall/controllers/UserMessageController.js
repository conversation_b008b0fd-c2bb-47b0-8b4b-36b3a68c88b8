/**
 * 商城用户消息控制器
 * 处理用户消息相关的HTTP请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const UserMessageService = require('../services/UserMessageService');

class UserMessageController extends BaseController {
  constructor(prisma) {
    super();
    this.messageService = new UserMessageService(prisma);
  }

  /**
   * 获取用户消息列表
   * GET /api/mall/user/messages
   */
  async getMessageList(req, res) {
    try {
      // 从JWT token中获取用户ID
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 获取查询参数
      const {
        page = 1,
        pageSize = 10,
        messageType,
        isRead,
        priority
      } = req.query;

      // 参数验证
      if (page < 1 || pageSize < 1 || pageSize > 100) {
        return this.fail(res, '分页参数无效', 400);
      }

      // 转换参数类型
      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };

      if (messageType) {
        options.messageType = messageType;
      }

      if (isRead !== undefined) {
        options.isRead = isRead === 'true';
      }

      if (priority) {
        options.priority = parseInt(priority);
      }

      // 获取消息列表
      const result = await this.messageService.getMessageList(userId, options);

      return this.success(res, result, '获取消息列表成功');
    } catch (error) {
      console.error('获取消息列表失败:', error);
      return this.fail(res, '获取消息列表失败', 500);
    }
  }

  /**
   * 获取未读消息数量
   * GET /api/mall/user/messages/unread-count
   */
  async getUnreadCount(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const count = await this.messageService.getUnreadCount(userId);

      return this.success(res, { count }, '获取未读消息数量成功');
    } catch (error) {
      console.error('获取未读消息数量失败:', error);
      return this.fail(res, '获取未读消息数量失败', 500);
    }
  }

  /**
   * 标记消息为已读
   * PUT /api/mall/user/messages/:messageId/read
   */
  async markAsRead(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const { messageId } = req.params;
      if (!messageId) {
        return this.fail(res, '消息ID不能为空', 400);
      }

      await this.messageService.markAsRead(userId, messageId);

      return this.success(res, null, '标记消息已读成功');
    } catch (error) {
      console.error('标记消息已读失败:', error);
      return this.fail(res, '标记消息已读失败', 500);
    }
  }

  /**
   * 批量标记消息为已读
   * PUT /api/mall/user/messages/batch-read
   */
  async batchMarkAsRead(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const { messageIds } = req.body;
      if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
        return this.fail(res, '消息ID列表不能为空', 400);
      }

      await this.messageService.markMultipleAsRead(userId, messageIds);

      return this.success(res, null, '批量标记消息已读成功');
    } catch (error) {
      console.error('批量标记消息已读失败:', error);
      return this.fail(res, '批量标记消息已读失败', 500);
    }
  }

  /**
   * 删除消息
   * DELETE /api/mall/user/messages/:messageId
   */
  async deleteMessage(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const { messageId } = req.params;
      if (!messageId) {
        return this.fail(res, '消息ID不能为空', 400);
      }

      await this.messageService.deleteMessage(userId, messageId);

      return this.success(res, null, '删除消息成功');
    } catch (error) {
      console.error('删除消息失败:', error);
      return this.fail(res, '删除消息失败', 500);
    }
  }

  /**
   * 清空所有消息
   * DELETE /api/mall/user/messages/clear-all
   */
  async clearAllMessages(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      await this.messageService.clearAllMessages(userId);

      return this.success(res, null, '清空所有消息成功');
    } catch (error) {
      console.error('清空所有消息失败:', error);
      return this.fail(res, '清空所有消息失败', 500);
    }
  }

  /**
   * 创建消息（管理员功能）
   * POST /api/mall/user/messages
   */
  async createMessage(req, res) {
    try {
      const {
        userId,
        title,
        content,
        messageType = 'system',
        priority = 1,
        actionUrl,
        actionText,
        icon
      } = req.body;

      // 参数验证
      if (!userId || !title) {
        return this.fail(res, '用户ID和消息标题不能为空', 400);
      }

      if (!['system', 'order', 'promotion', 'service'].includes(messageType)) {
        return this.fail(res, '消息类型无效', 400);
      }

      if (![1, 2, 3].includes(priority)) {
        return this.fail(res, '优先级无效', 400);
      }

      const messageData = {
        userId,
        title,
        content,
        messageType,
        priority,
        actionUrl,
        actionText,
        icon,
        createdBy: req.user?.id
      };

      const message = await this.messageService.createMessage(messageData);

      return this.success(res, message, '创建消息成功');
    } catch (error) {
      console.error('创建消息失败:', error);
      return this.fail(res, '创建消息失败', 500);
    }
  }
}

module.exports = UserMessageController;
