const BaseController = require('../../../core/controllers/BaseController');
const WechatAuthService = require('../services/WechatService/WechatAuthService');

/**
 * 微信公众号控制器
 * 处理微信公众号相关的接口请求
 */
class WechatController extends BaseController {
  /**
   * 生成用户ID
   * @returns {Promise<BigInt>} - 返回生成的用户ID
   */
  async generateUserId() {
    try {
      // 使用雪花算法生成ID
      const { Snowflake } = require('../../../core/utils/Snowflake');
      const snowflake = new Snowflake(1, 1);
      return BigInt(snowflake.nextId().toString());
    } catch (error) {
      console.error('生成用户ID失败:', error);
      // 如果雪花算法失败，使用时间戳加随机数生成ID
      return BigInt(Date.now().toString() + Math.floor(Math.random() * 1000).toString());
    }
  }
  constructor() {
    super();
    this.wechatAuthService = null;
    this.app = null;
    this.userWechatController = null;
    this.prisma = null;
    this.baseUrl = process.env.BASE_URL || 'https://v4.ioa.8080bl.com';
  }
  
  /**
   * 初始化服务
   * @param {Object} wechatAuthService - 微信认证服务
   * @param {Object} app - 应用实例
   * @param {Object} userWechatController - 用户微信控制器实例
   */
  initServices(wechatAuthService, app, userWechatController) {
    this.wechatAuthService = wechatAuthService;
    this.app = app;
    this.userWechatController = userWechatController;
    
    // 初始化 Prisma 客户端
    const { PrismaClient } = require('@prisma/client');
    this.prisma = new PrismaClient();
    console.log('微信控制器初始化 Prisma 客户端成功');
  }

  /**
   * 处理微信服务器的验证请求
   * 用于验证服务器的有效性
   * @param {object} req - 请求对象
   * @param {object} res - 响应对象
   */
  async verifyServer(req, res) {
    try {
      const { signature, timestamp, nonce, echostr } = req.query;

      // 参数校验
      if (!signature || !timestamp || !nonce) {
        return this.fail(res, '缺少必要的参数');
      }

      // 验证签名
      const isValid = this.wechatAuthService.verifySignature(signature, timestamp, nonce);

      if (isValid) {
        // 如果是验证请求，返回echostr
        if (echostr) {
          return res.send(echostr);
        }
        return this.success(res, { verified: true });
      } else {
        return this.fail(res, '签名验证失败');
      }
    } catch (error) {
      console.error('微信服务器验证失败:', error);
      return this.fail(res, '微信服务器验证处理异常');
    }
  }

  /**
   * 处理微信服务器的消息推送
   * @param {object} req - 请求对象
   * @param {object} res - 响应对象
   */
  async handleMessage(req, res) {
    try {
      const { signature, timestamp, nonce, encrypt_type, msg_signature } = req.query;
      const xmlData = req.body;

      // 验证签名
      const isValid = this.wechatAuthService.verifySignature(signature, timestamp, nonce);
      if (!isValid) {
        return this.fail(res, '签名验证失败');
      }

      // 解析消息
      const message = req.body;
      console.log('接收到微信消息:', message);

      let replyMessage = null;

      // 处理不同类型的消息
      if (message.MsgType === 'event') {
        // 如果是关注事件，回复欢迎消息
        if (message.Event === 'subscribe') {
          replyMessage = {
            type: 'text',
            content: '欢迎关注我们的公众号！'
          };
        } else {
          // 处理其他事件，如扫码事件
          replyMessage = await this.handleEvent(message);
        }
      } else if (message.MsgType === 'text') {
        // 处理文本消息
        console.log('收到文本消息:', message.Content);
        
        // 默认回复消息
        replyMessage = {
          type: 'text',
          content: `您发送的消息是: ${message.Content}`
        };
      }

      // 如果有回复消息，生成XML并发送
      if (replyMessage) {
        const now = Math.floor(Date.now() / 1000);
        let replyXml = '';
        
        if (replyMessage.type === 'text') {
          replyXml = `<xml>
            <ToUserName><![CDATA[${message.FromUserName}]]></ToUserName>
            <FromUserName><![CDATA[${message.ToUserName}]]></FromUserName>
            <CreateTime>${now}</CreateTime>
            <MsgType><![CDATA[text]]></MsgType>
            <Content><![CDATA[${replyMessage.content}]]></Content>
          </xml>`;
        } else if (replyMessage.type === 'news' && replyMessage.articles && replyMessage.articles.length > 0) {
          // 图文消息回复
          const articlesXml = replyMessage.articles.map(article => `
            <item>
              <Title><![CDATA[${article.title}]]></Title>
              <Description><![CDATA[${article.description}]]></Description>
              <PicUrl><![CDATA[${article.picUrl}]]></PicUrl>
              <Url><![CDATA[${article.url}]]></Url>
            </item>
          `).join('');
          
          replyXml = `<xml>
            <ToUserName><![CDATA[${message.FromUserName}]]></ToUserName>
            <FromUserName><![CDATA[${message.ToUserName}]]></FromUserName>
            <CreateTime>${now}</CreateTime>
            <MsgType><![CDATA[news]]></MsgType>
            <ArticleCount>${replyMessage.articles.length}</ArticleCount>
            <Articles>${articlesXml}</Articles>
          </xml>`;
        }
        
        if (replyXml) {
          res.set('Content-Type', 'application/xml');
          return res.send(replyXml);
        }
      }

      // 如果没有特定回复，返回成功响应
      return res.send('success');
    } catch (error) {
      console.error('处理微信消息失败:', error);
      return res.send('success');
    }
  }
  
  /**
   * 处理微信事件消息
   * @param {object} event - 事件消息
   */
  async handleEvent(event) {
    try {
      const eventType = event.Event;
      
      // 处理扫码事件
      if (eventType === 'SCAN') {
        // 返回 handleScanEvent 的结果
        return await this.handleScanEvent(event);
      } 
      // 处理关注事件
      else if (eventType === 'subscribe') {
        // 如果是扫码关注，也需要处理扫码事件
        if (event.EventKey && event.EventKey.startsWith('qrscene_')) {
          // 提取场景值，去掉前缀 'qrscene_'
          const sceneStr = event.EventKey.substring(8);
          // 返回 handleScanEvent 的结果
          return await this.handleScanEvent({
            ...event,
            EventKey: sceneStr
          });
        }
        
        console.log('新用户关注:', event.FromUserName);
        // 返回欢迎消息
        return {
          type: 'text',
          content: '感谢关注我们的公众号！'
        };
      } 
      // 处理取消关注事件
      else if (eventType === 'unsubscribe') {
        console.log('用户取消关注:', event.FromUserName);
      }
      
      // 默认返回空
      return null;
    } catch (error) {
      console.error('处理微信事件失败:', error);
      return null;
    }
  }
  
  /**
   * 处理扫码事件
   * @param {object} event - 扫码事件消息
   */
  async handleScanEvent(event) {
    try {
      const openid = event.FromUserName;
      const sceneStr = event.EventKey;
      
      console.log(`用户 ${openid} 扫描了二维码，场景值: ${sceneStr}`);
      
      // 检查是否为登录场景
      if (global.wechatLoginScenes && global.wechatLoginScenes[sceneStr]) {
        const loginScene = global.wechatLoginScenes[sceneStr];
        
        // 检查是否过期
        if (Date.now() > loginScene.expireTime) {
          console.log('扫码登录请求已过期:', sceneStr);
          loginScene.status = 'EXPIRED';
          return;
        }
        
        // 获取用户信息
        const userInfo = await this.wechatAuthService.getUserInfo(openid);
        
        // 更新登录场景状态
        loginScene.status = 'SCANNED';
        loginScene.openid = openid;
        loginScene.userInfo = userInfo;
        loginScene.scanTime = Date.now();
        
        console.log('用户扫码成功，等待确认:', userInfo.nickname);
        
        // 发送消息给用户，请求确认登录
        // 生成微信授权URL，使用场景值作为state参数
        console.log('生成微信授权URL，场景值:', sceneStr);
        
        // 获取回调域名
        const callbackDomain = process.env.API_BASE_URL || 'https://v4api.ioa.8080bl.com';
        
        // 生成微信授权URL
        const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.wechatAuthService.appId}&redirect_uri=${encodeURIComponent(`${callbackDomain}/api/v1/mall/wechat/login/callback`)}&response_type=code&scope=snsapi_userinfo&state=${sceneStr}#wechat_redirect`;
        
        console.log('生成的微信授权URL:', authUrl);
        
        // 将授权URL保存到登录场景中
        loginScene.authUrl = authUrl;
        
        return {
          type: 'text',
          content: `您正在进行网页扫码登录，确认是本人操作吗？\n\n点击确认登录: ${authUrl}`
        };
      } 
      // 检查是否为绑定场景
      else if (sceneStr.startsWith('bind_') && global.wechatBindScenes && global.wechatBindScenes[sceneStr]) {
        // 处理微信绑定场景
        if (this.userWechatController) {
          console.log('调用 handleBindEvent 处理绑定事件:', sceneStr);
          const result = await this.userWechatController.handleBindEvent(event, openid, sceneStr);
          console.log('绑定处理结果:', result);
          if (result) {
            // 绑定成功，发送消息给用户
            return {
              type: 'text',
              content: '恭喜您，微信账号绑定成功！您现在可以使用微信扫码快速登录。'
            };
          } else {
            // 绑定失败，发送错误消息
            return {
              type: 'text',
              content: '微信账号绑定失败，请重新尝试。'
            };
          }
        } else {
          console.error('用户微信控制器未初始化');
          // 返回错误消息
          return {
            type: 'text',
            content: '系统错误，请稍后再试。'
          };
        }
      } else {
        console.log('未找到对应的场景请求:', sceneStr);
      }
    } catch (error) {
      console.error('处理扫码事件失败:', error);
    }
    
    // 如果没有特殊处理，返回默认消息
    return null;
  }

  /**
   * 获取微信扫码登录二维码
   * @param {object} req - 请求对象
   * @param {object} res - 响应对象
   */
  async getLoginQrCode(req, res) {
    try {
      // 生成随机的场景值
      const sceneStr = `login_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
      
      // 获取回调域名
      const callbackDomain = 'https://v4api.ioa.8080bl.com';
      
      // 生成当前时间戳，用于state参数
      const timestamp = Date.now();
      // 简化state参数格式，使用场景值作为唯一标识
      const state = sceneStr;
      
      console.log('生成的登录场景:', sceneStr);
      console.log('生成的state参数:', state);
      
      // 生成授权URL
      const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.wechatAuthService.appId}&redirect_uri=${encodeURIComponent(`${callbackDomain}/mall/auth/wechat`)}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`;
      
      // 创建二维码
      const qrcodeResult = await this.wechatAuthService.createQRCodeTicket(sceneStr, 1800); // 设置30分钟有效期
      
      // 初始化全局对象来存储扫码登录的场景
      if (!global.wechatLoginScenes) {
        global.wechatLoginScenes = {};
      }
      
      // 存储扫码登录的场景信息
      global.wechatLoginScenes[sceneStr] = {
        ticket: qrcodeResult.ticket,
        createTime: Date.now(),
        expireTime: Date.now() + (qrcodeResult.expireSeconds * 1000),
        status: 'CREATED', // 初始状态：CREATED、SCANNED、CONFIRMED、EXPIRED、CANCELED
        authUrl, // 授权URL
        state // 状态参数
      };
      
      // 返回二维码信息
      return this.success(res, {
        ticket: sceneStr,
        qrcodeUrl: qrcodeResult.qrcodeUrl,
        expiresIn: qrcodeResult.expireSeconds
      });
    } catch (error) {
      console.error('获取微信扫码登录二维码失败:', error);
      return this.fail(res, '获取微信扫码登录二维码失败');
    }
  }

  /**
   * 检查扫码登录状态
   * @param {object} req - 请求对象
   * @param {object} res - 响应对象
   */
  async checkLoginStatus(req, res) {
    try {
      const { sceneStr } = req.query;

      // 参数校验
      if (!sceneStr) {
        return this.fail(res, '缺少场景值参数');
      }

      // 检查登录场景是否存在
      if (!global.wechatLoginScenes || !global.wechatLoginScenes[sceneStr]) {
        return this.fail(res, '登录请求不存在或已过期');
      }

      const loginScene = global.wechatLoginScenes[sceneStr];

      // 检查是否过期
      if (Date.now() > loginScene.expireTime) {
        loginScene.status = 'EXPIRED';
        return this.fail(res, '登录请求已过期');
      }

      // 根据不同状态返回不同的响应
      switch (loginScene.status) {
        case 'CREATED':
          // 二维码已创建，等待扫码
          return this.success(res, {
            status: 'WAITING',
            message: '等待扫码'
          });

        case 'SCANNED':
          // 已扫码，等待确认
          return this.success(res, {
            status: 'SCANNED',
            message: '已扫码，等待确认',
            userInfo: {
              nickname: loginScene.userInfo.nickname,
              headimgurl: loginScene.userInfo.headimgurl
            }
          });

        case 'CONFIRMED':
          // 已确认登录，返回登录令牌和用户信息
          const userInfo = loginScene.userInfo;
          
          // 登录成功后不要立即删除，而是标记为已完成并设置一个短暂的缓存期
          // 这样前端在重定向过程中仍然可以查询到登录状态
          loginScene.status = 'COMPLETED';
          loginScene.completedTime = Date.now();
          // 30秒后自动清理
          setTimeout(() => {
            if (global.wechatLoginScenes && global.wechatLoginScenes[sceneStr]) {
              delete global.wechatLoginScenes[sceneStr];
              console.log(`登录场景 ${sceneStr} 已自动清理`);
            }
          }, 30000);
          
          return this.success(res, {
            status: 'SUCCESS',
            message: '登录成功',
            token: loginScene.token,
            userInfo: {
              openid: userInfo.openid,
              nickname: userInfo.nickname,
              headimgurl: userInfo.headimgurl,
              sex: userInfo.sex,
              country: userInfo.country,
              province: userInfo.province,
              city: userInfo.city,
              loginTime: new Date().toISOString()
            }
          });

        case 'CANCELED':
          // 用户取消登录
          delete global.wechatLoginScenes[sceneStr];
          return this.fail(res, '用户取消登录');

        case 'EXPIRED':
          // 登录请求已过期
          delete global.wechatLoginScenes[sceneStr];
          return this.fail(res, '登录请求已过期');

        case 'COMPLETED':
          // 登录已完成，但前端可能仍在查询状态
          // 检查是否已超过缓存期限（30秒）
          if (Date.now() - loginScene.completedTime > 30000) {
            delete global.wechatLoginScenes[sceneStr];
            return this.fail(res, '登录请求不存在或已过期');
          }
          
          // 返回成功状态和用户信息
          return this.success(res, {
            status: 'SUCCESS',
            message: '登录成功',
            token: loginScene.token,
            userInfo: {
              openid: loginScene.userInfo.openid,
              nickname: loginScene.userInfo.nickname,
              headimgurl: loginScene.userInfo.headimgurl,
              sex: loginScene.userInfo.sex,
              country: loginScene.userInfo.country,
              province: loginScene.userInfo.province,
              city: loginScene.userInfo.city,
              loginTime: new Date().toISOString()
            }
          });
          
        default:
          return this.fail(res, '未知状态');
      }
    } catch (error) {
      console.error('检查扫码登录状态失败:', error);
      return this.fail(res, '检查扫码登录状态失败');
    }
  }

  /**
   * 处理微信扫码登录回调
   * @param {object} req - 请求对象
   * @param {object} res - 响应对象
   */
  async handleLoginCallback(req, res) {
    try {
      const { code, state } = req.query;

      // 参数校验
      if (!code || !state) {
        return this.fail(res, '缺少必要的参数');
      }

      // 验证state参数，防止CSRF攻击
      // 现在state参数就是场景值本身
      console.log('收到的state参数:', state);
      
      // 直接使用state作为场景值
      const sceneStr = state;
      
      // 检查场景值格式
      if (!sceneStr.startsWith('login_')) {
        console.error('无效的登录场景格式:', sceneStr);
        return this.fail(res, '无效的state参数');
      }
      
      console.log('解析出的场景值:', sceneStr);
      
      // 查找对应的登录场景
      if (!global.wechatLoginScenes || !global.wechatLoginScenes[sceneStr]) {
        console.error('未找到登录场景:', sceneStr);
        console.log('当前所有登录场景:', Object.keys(global.wechatLoginScenes || {}));
        return this.fail(res, '未找到对应的登录请求，请重新扫码');
      }
      
      // 检查场景是否过期
      const loginScene = global.wechatLoginScenes[sceneStr];
      if (Date.now() > loginScene.expireTime) {
        console.log('登录请求已过期:', sceneStr);
        loginScene.status = 'EXPIRED';
        return this.fail(res, '授权请求已过期，请重新扫码');
      }
      
      // 使用code获取用户的access_token和openid
      const accessTokenResult = await this.wechatAuthService.getAccessTokenByCode(code);
      
      if (!accessTokenResult || !accessTokenResult.access_token || !accessTokenResult.openid) {
        return this.fail(res, '获取微信用户授权失败');
      }
      
      // 获取用户信息
      const userInfo = await this.wechatAuthService.getUserInfo(
        accessTokenResult.access_token,
        accessTokenResult.openid
      );
      
      if (!userInfo || !userInfo.openid) {
        return this.fail(res, '获取微信用户信息失败');
      }
      
      // 根据openid查找用户
      const users = await this.prisma.$queryRaw`
        SELECT id, username, nickname, phone, email, status, avatar, wechat_openid
        FROM "base"."mall_user" 
        WHERE "wechat_openid" = ${userInfo.openid}
        AND "deleted_at" IS NULL
      `;
      
      let user;
      let token;
      
      if (!users || users.length === 0) {
        // 用户不存在，创建新用户
        console.log('微信用户不存在，创建新用户');
        
        // 生成随机用户名和密码
        const randomUsername = `wx_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        const randomPassword = Math.random().toString(36).slice(-8);
        
        // 加密密码
        const bcrypt = require('bcryptjs');
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(randomPassword, salt);
        
        // 生成用户ID
        const userId = await this.generateUserId();
        const now = Date.now();
        
        // 插入用户记录
        await this.prisma.$executeRaw`
          INSERT INTO "base"."mall_user" (
            "id", "username", "password", "nickname", "avatar", "wechat_openid",
            "status", "created_at", "updated_at"
          ) VALUES (
            ${userId}, ${randomUsername}, ${hashedPassword}, ${userInfo.nickname || '微信用户'}, 
            ${userInfo.headimgurl || ''}, ${userInfo.openid},
            1, ${now}, ${now}
          )
        `;
        
        // 查询新创建的用户
        const newUsers = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, status, avatar, wechat_openid
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
        `;
        
        if (!newUsers || newUsers.length === 0) {
          return this.fail(res, '创建微信用户失败');
        }
        
        user = newUsers[0];
      } else {
        // 用户已存在
        console.log('微信用户已存在，直接登录');
        user = users[0];
      }
      
      // 生成JWT令牌
      const jwt = require('jsonwebtoken');
      const JWT_SECRET = process.env.JWT_SECRET || 'mall_jwt_secret';
      const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
      
      token = jwt.sign(
        { id: user.id.toString(), username: user.username },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
      );
      
      // 计算令牌过期时间（秒）
      const expiresIn = parseInt(JWT_EXPIRES_IN.replace(/[^0-9]/g, '')) * (
        JWT_EXPIRES_IN.includes('h') ? 3600 : 
        JWT_EXPIRES_IN.includes('d') ? 86400 : 
        JWT_EXPIRES_IN.includes('m') ? 60 : 1
      );
      
      // 保存用户令牌（用于单点登录）
      const redisUtil = require('../../../core/utils/RedisUtil');
      await redisUtil.saveUserToken(user.id.toString(), token, expiresIn);
      
      // 更新用户登录信息
      const lastLoginIp = req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress;
      const lastLoginTime = Date.now();
      
      try {
        console.log('更新用户登录信息:', { userId: user.id, lastLoginIp, lastLoginTime });
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "last_login_ip" = ${lastLoginIp}, "last_login_time" = ${lastLoginTime}, 
              "login_count" = COALESCE("login_count", 0) + 1, "updated_at" = ${lastLoginTime}
          WHERE "id" = ${user.id}
        `;
      } catch (updateError) {
        console.warn('更新微信用户登录信息失败:', updateError);
        // 继续执行，不影响登录流程
      }
      
      // 准备返回的用户数据
      const userData = {
        id: user.id.toString(),
        username: user.username,
        nickname: user.nickname || user.username,
        phone: user.phone || '',
        email: user.email || '',
        avatar: user.avatar || userInfo.headimgurl || '',
        wechatOpenid: user.wechat_openid || userInfo.openid,
        last_login_time: lastLoginTime,
        last_login_ip: lastLoginIp
      };
      
      // 更新登录场景状态为已确认
      global.wechatLoginScenes[sceneStr] = {
        ...global.wechatLoginScenes[sceneStr],
        status: 'CONFIRMED',
        userInfo: userData,
        token
      };
      
      // 将用户重定向到前端登录成功页面，并带上令牌和用户信息
      const frontendUrl = this.baseUrl || 'https://v4.ioa.8080bl.com';
      const redirectUrl = `${frontendUrl}/mall/auth/wechat?token=${encodeURIComponent(token)}&userId=${userData.id}&nickname=${encodeURIComponent(userData.nickname || '')}&avatar=${encodeURIComponent(userData.avatar || '')}`;
      
      console.log('重定向到前端登录成功页面:', redirectUrl);
      
      // 重定向到前端页面
      res.redirect(redirectUrl);
    } catch (error) {
      console.error('处理微信登录回调失败:', error);
      return this.fail(res, '处理微信登录回调失败');
    }
  }
}

module.exports = WechatController;
