const express = require('express');
const bodyParser = require('body-parser');
const xml2js = require('xml2js');
const WechatController = require('../controllers/WechatController');
const UserWechatController = require('../controllers/UserWechatController');
const { PrismaClient } = require('@prisma/client');

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

/**
 * 微信公众号路由
 * 处理微信公众号相关的接口请求
 */
class WechatRoute {
  constructor() {
    this.router = express.Router();
    
    // 初始化Prisma客户端
    const prisma = new PrismaClient();
    
    // 初始化微信控制器
    this.wechatController = new WechatController();
    
    // 初始化用户微信绑定控制器
    this.userWechatController = new UserWechatController(prisma);
    
    // 初始化微信服务
    const WechatAuthService = require('../services/WechatService/WechatAuthService');
    const wechatAuthService = new WechatAuthService();
    
    // 将微信服务注入到控制器中
    this.userWechatController.initServices(wechatAuthService);
    
    // 将微信服务和用户微信控制器注入到微信控制器中
    this.wechatController.initServices(wechatAuthService, this, this.userWechatController);
    
    // 设置基础URL
    this.wechatController.baseUrl = process.env.BASE_URL || 'https://v4.ioa.8080bl.com';
    
    this.initRoutes();
  }

  /**
   * 初始化路由
   */
  initRoutes() {
    // 创建XML解析中间件
    const xmlParser = async (req, res, next) => {
      if (req.headers['content-type'] === 'text/xml' || req.headers['content-type'] === 'application/xml') {
        let xmlData = '';
        req.on('data', chunk => {
          xmlData += chunk;
        });
        req.on('end', () => {
          xml2js.parseString(xmlData, { explicitArray: false }, (err, result) => {
            if (err) {
              console.error('解析XML失败:', err);
              return next();
            }
            req.body = result.xml;
            next();
          });
        });
      } else {
        next();
      }
    };

    /**
     * @swagger
     * /api/v1/mall/wechat/verify:
     *   get:
     *     summary: 微信服务器验证接口
     *     description: 用于验证微信服务器的请求，微信服务器会发送签名、时间戳、随机数和回显字符串
     *     tags:
     *       - 微信公众号
     *     parameters:
     *       - name: signature
     *         in: query
     *         description: 微信服务器发送的签名
     *         required: true
     *         schema:
     *           type: string
     *       - name: timestamp
     *         in: query
     *         description: 时间戳
     *         required: true
     *         schema:
     *           type: string
     *       - name: nonce
     *         in: query
     *         description: 随机数
     *         required: true
     *         schema:
     *           type: string
     *       - name: echostr
     *         in: query
     *         description: 回显字符串
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: 验证成功，返回回显字符串
     */
    this.router.get('/verify', this.wechatController.verifyServer.bind(this.wechatController));

    /**
     * @swagger
     * /api/v1/mall/wechat/verify:
     *   post:
     *     summary: 微信消息处理接口
     *     description: 用于接收微信服务器推送的消息
     *     tags:
     *       - 微信公众号
     *     parameters:
     *       - name: signature
     *         in: query
     *         description: 微信服务器发送的签名
     *         required: true
     *         schema:
     *           type: string
     *       - name: timestamp
     *         in: query
     *         description: 时间戳
     *         required: true
     *         schema:
     *           type: string
     *       - name: nonce
     *         in: query
     *         description: 随机数
     *         required: true
     *         schema:
     *           type: string
     *       - name: encrypt_type
     *         in: query
     *         description: 加密类型
     *         required: false
     *         schema:
     *           type: string
     *       - name: msg_signature
     *         in: query
     *         description: 消息签名
     *         required: false
     *         schema:
     *           type: string
     *     requestBody:
     *       content:
     *         application/xml:
     *           schema:
     *             type: object
     *     responses:
     *       200:
     *         description: 处理成功
     */
    this.router.post('/verify', xmlParser, this.wechatController.handleMessage.bind(this.wechatController));

    /**
     * @swagger
     * /api/v1/mall/wechat/login/qrcode:
     *   get:
     *     summary: 获取微信扫码登录二维码
     *     description: 生成微信扫码登录的二维码图片
     *     tags:
     *       - 微信公众号
     *     responses:
     *       200:
     *         description: 成功获取二维码
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 0
     *                 message:
     *                   type: string
     *                   example: 操作成功
     *                 data:
     *                   type: object
     *                   properties:
     *                     ticket:
     *                       type: string
     *                       example: login_1623481234567_abcdef
     *                     qrcodeUrl:
     *                       type: string
     *                       example: https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQH47joAAAAAAAAAASxodHRwOi8vd2VpeGluLnFxLmNvbS9xL2taZ2Z3TVRtNzJXV1Brb3ZhYmJJAAIEjMBoVwMEAAAAAA%3D%3D
     *                     expiresIn:
     *                       type: integer
     *                       example: 1800
     */
    this.router.get('/login/qrcode', this.wechatController.getLoginQrCode.bind(this.wechatController));
    
    /**
     * @swagger
     * /api/v1/mall/wechat/login/check:
     *   get:
     *     summary: 检查微信扫码登录状态
     *     description: 查询微信扫码登录的状态，包括是否已扫码、是否已确认等
     *     tags:
     *       - 微信公众号
     *     parameters:
     *       - name: sceneStr
     *         in: query
     *         description: 场景值（票据）
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: 成功获取扫码状态
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 0
     *                 message:
     *                   type: string
     *                   example: 操作成功
     *                 data:
     *                   type: object
     *                   properties:
     *                     status:
     *                       type: string
     *                       example: WAITING
     *                     message:
     *                       type: string
     *                       example: 等待扫码
     */
    this.router.get('/login/check', this.wechatController.checkLoginStatus.bind(this.wechatController));

    /**
     * @swagger
     * /api/v1/mall/wechat/login/status:
     *   get:
     *     summary: 检查微信扫码登录状态
     *     description: 前端轮询检查微信扫码登录状态
     *     tags:
     *       - 微信公众号
     *     parameters:
     *       - name: sceneStr
     *         in: query
     *         description: 场景值，用于标识不同的登录请求
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: 成功返回登录状态
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 0
     *                 message:
     *                   type: string
     *                   example: 操作成功
     *                 data:
     *                   type: object
     *                   properties:
     *                     status:
     *                       type: string
     *                       example: WAITING
     *                     message:
     *                       type: string
     *                       example: 等待扫码
     *                     token:
     *                       type: string
     *                       example: wechat_login_token_1623481234567_openid
     *                     userInfo:
     *                       type: object
     *                       properties:
     *                         openid:
     *                           type: string
     *                           example: openid_1623481234567
     *                         nickname:
     *                           type: string
     *                           example: 微信用户
     *                         headimgurl:
     *                           type: string
     *                           example: https://thirdwx.qlogo.cn/mmopen/vi_32/sample/0
     */
    this.router.get('/login/status', this.wechatController.checkLoginStatus.bind(this.wechatController));

    /**
     * @swagger
     * /api/v1/mall/wechat/login/callback:
     *   get:
     *     summary: 微信扫码登录回调接口
     *     description: 处理微信扫码登录后的回调请求
     *     tags:
     *       - 微信公众号
     *     parameters:
     *       - name: code
     *         in: query
     *         description: 微信授权码
     *         required: true
     *         schema:
     *           type: string
     *       - name: state
     *         in: query
     *         description: 状态参数，用于防止CSRF攻击
     *         required: true
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: 登录成功
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 0
     *                 message:
     *                   type: string
     *                   example: 操作成功
     *                 data:
     *                   type: object
     *                   properties:
     *                     token:
     *                       type: string
     *                       example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
     *                     userInfo:
     *                       type: object
     *                       properties:
     *                         openid:
     *                           type: string
     *                           example: openid_1623481234567
     *                         nickname:
     *                           type: string
     *                           example: 微信用户
     *                         headimgurl:
     *                           type: string
     *                           example: https://thirdwx.qlogo.cn/mmopen/vi_32/sample/0
     */
    this.router.get('/login/callback', this.wechatController.handleLoginCallback.bind(this.wechatController));
    
    // 用户微信绑定二维码
    this.router.get('/user/bind/qrcode', authMiddleware, this.userWechatController.getBindQrcode.bind(this.userWechatController));
    
    // 检查微信绑定状态
    this.router.get('/user/bind/status', authMiddleware, this.userWechatController.checkBindStatus.bind(this.userWechatController));
    
    // 微信授权登录回调（用于处理用户在微信中点击确认登录）
    this.router.get('/auth/wechat', this.wechatController.handleLoginCallback.bind(this.wechatController));
  }

  /**
   * 获取路由实例
   * @returns {Object} Express路由实例
   */
  getRouter() {
    return this.router;
  }
}

module.exports = WechatRoute;
