/**
 * 商城商品路由
 * 定义商品相关的API路由
 */
const express = require('express');
const router = express.Router();

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');
// 导入可选认证中间件
const optionalAuthMiddleware = require('../../../core/middleware/OptionalAuthMiddleware');

// 导入商品控制器
const GoodsController = require('../controllers/GoodsController');

/**
 * 商品路由模块
 * @param {Object} prisma Prisma客户端实例
 * @returns {Object} Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const goodsController = new GoodsController(prisma);

  /**
   * @swagger
   * /api/v1/mall/goods/category/tree:
   *   get:
   *     summary: 获取商品分类树形结构
   *     tags: [Mall-Goods]
   *     responses:
   *       200:
   *         description: 成功获取商品分类树形结构
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取商品分类树形结构成功
   *                 data:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/GoodsCategoryTree'
   */
  router.get('/category/tree', goodsController.getCategoryTree.bind(goodsController));
  
  /**
   * @swagger
   * /api/v1/mall/goods/category/{categoryId}:
   *   get:
   *     summary: 获取商品分类详情
   *     tags: [Mall-Goods]
   *     parameters:
   *       - in: path
   *         name: categoryId
   *         required: true
   *         schema:
   *           type: integer
   *         description: 商品分类ID
   *     responses:
   *       200:
   *         description: 成功获取商品分类详情
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取商品分类信息成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       description: 分类ID
   *                     goodsParentCategoryId:
   *                       type: string
   *                       description: 父分类ID
   *                     name:
   *                       type: string
   *                       description: 分类名称
   *                     imageUrl:
   *                       type: string
   *                       description: 分类图片URL
   *                     description:
   *                       type: string
   *                       description: 分类描述
   *                     metaTitle:
   *                       type: string
   *                       description: SEO标题
   *                     metaKeywords:
   *                       type: string
   *                       description: SEO关键词
   *                     metaDescription:
   *                       type: string
   *                       description: SEO描述
   *                     sortOrder:
   *                       type: integer
   *                       description: 排序序号
   *                     isEnabled:
   *                       type: integer
   *                       description: 是否启用，1-启用，0-禁用
   *                     level:
   *                       type: integer
   *                       description: 分类层级，从1开始
   *       400:
   *         description: 请求参数错误
   *       404:
   *         description: 分类不存在
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/category/:categoryId', goodsController.getCategoryById.bind(goodsController));
  
  /**
   * @swagger
   * /api/v1/mall/goods/products:
   *   get:
   *     summary: 获取商品列表
   *     tags: [Mall-Goods]
   *     parameters:
   *       - in: query
   *         name: categoryId
   *         schema:
   *           type: integer
   *         description: 商品分类ID
   *       - in: query
   *         name: brandId
   *         schema:
   *           type: integer
   *         description: 品牌ID
   *       - in: query
   *         name: keyword
   *         schema:
   *           type: string
   *         description: 搜索关键词
   *       - in: query
   *         name: minPrice
   *         schema:
   *           type: number
   *         description: 最低价格
   *       - in: query
   *         name: maxPrice
   *         schema:
   *           type: number
   *         description: 最高价格
   *       - in: query
   *         name: tags
   *         schema:
   *           type: string
   *         description: 标签，多个标签用逗号分隔
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           enum: [created_at, sales, price]
   *           default: created_at
   *         description: 排序字段
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [asc, desc]
   *           default: desc
   *         description: 排序方式
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 成功获取商品列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取商品列表成功
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 data:
   *                   type: object
   *                   properties:
   *                     items:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             description: 商品ID
   *                           name:
   *                             type: string
   *                             description: 商品名称
   *                           imageUrl:
   *                             type: string
   *                             description: 商品主图
   *                           totalSales:
   *                             type: integer
   *                             description: 销量
   *                           price:
   *                             type: number
   *                             description: 价格
   *                     pageInfo:
   *                       type: object
   *                       properties:
   *                         total:
   *                           type: integer
   *                           description: 总记录数
   *                         currentPage:
   *                           type: integer
   *                           description: 当前页码
   *                         totalPage:
   *                           type: integer
   *                           description: 总页数
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/products', goodsController.getProducts.bind(goodsController));
  
  // 旧接口/api/v1/mall/goods/category/{categoryId}/products已移除，统一使用/api/v1/mall/goods/products
  
  /**
   * @swagger
   * /api/v1/mall/goods/tags:
   *   get:
   *     summary: 获取商品标签列表
   *     tags: [Mall-Goods]
   *     parameters:
   *       - in: query
   *         name: name
   *         schema:
   *           type: string
   *         description: 标签名称，支持模糊查询
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 成功获取商品标签列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取商品标签列表成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     items:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             description: 标签ID
   *                           name:
   *                             type: string
   *                             description: 标签名称
   *                           slug:
   *                             type: string
   *                             description: 标签别名（URL友好）
   *                           imageUrl:
   *                             type: string
   *                             description: 标签图片URL
   *                           description:
   *                             type: string
   *                             description: 标签描述
   *                           tagType:
   *                             type: string
   *                             description: 标签类型
   *                           sortOrder:
   *                             type: integer
   *                             description: 排序序号
   *                     pageInfo:
   *                       type: object
   *                       properties:
   *                         total:
   *                           type: integer
   *                           description: 总记录数
   *                         currentPage:
   *                           type: integer
   *                           description: 当前页码
   *                         totalPage:
   *                           type: integer
   *                           description: 总页数
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/tags', goodsController.getAllTags.bind(goodsController));
  
  /**
   * @swagger
   * /api/v1/mall/goods/brands:
   *   get:
   *     summary: 获取所有商品品牌（仅id和名称）
   *     tags: [Mall-Goods]
   *     responses:
   *       200:
   *         description: 成功获取品牌列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取品牌列表成功
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         description: 品牌 ID
   *                       name:
   *                         type: string
   *                         description: 品牌名称
   */
  router.get('/brands', goodsController.getAllBrands.bind(goodsController));
  
  /**
   * @swagger
   * /api/v1/mall/goods/services:
   *   get:
   *     summary: 获取所有商品服务（仅id和名称）
   *     tags: [Mall-Goods]
   *     responses:
   *       200:
   *         description: 成功获取商品服务列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取商品服务列表成功
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         description: 服务 ID
   *                       name:
   *                         type: string
   *                         description: 服务名称
   */
  router.get('/services', goodsController.getAllServices.bind(goodsController));
  
  /**
   * @swagger
   * /api/v1/mall/goods/product/{spuId}:
   *   get:
   *     summary: 获取商品详情
   *     tags: [Mall-Goods]
   *     parameters:
   *       - in: path
   *         name: spuId
   *         required: true
   *         schema:
   *           type: integer
   *         description: 商品SPU ID
   *     responses:
   *       200:
   *         description: 成功获取商品详情
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取商品详情成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       description: 商品ID
   *                     name:
   *                       type: string
   *                       description: 商品名称
   *                     subtitle:
   *                       type: string
   *                       description: 商品副标题
   *                     description:
   *                       type: string
   *                       description: 商品描述
   *                     spuCode:
   *                       type: string
   *                       description: 商品编码
   *                     status:
   *                       type: integer
   *                       description: 商品状态：0-草稿，1-上架，2-下架
   *                     totalSales:
   *                       type: integer
   *                       description: 总销量
   *                     skuType:
   *                       type: integer
   *                       description: 商品规格类型：1-单规格，2-多规格
   *                     brand:
   *                       type: object
   *                       description: 品牌信息
   *                     categories:
   *                       type: array
   *                       description: 分类信息
   *                     services:
   *                       type: array
   *                       description: 服务信息
   *                     tags:
   *                       type: array
   *                       description: 标签信息
   *                     attributes:
   *                       type: array
   *                       description: 属性信息
   *                     images:
   *                       type: array
   *                       description: 图片信息
   *                     videos:
   *                       type: array
   *                       description: 视频信息
   *                     skus:
   *                       type: array
   *                       description: SKU信息
   *       400:
   *         description: 请求参数错误
   *       404:
   *         description: 商品不存在或已下架
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/product/:spuId', optionalAuthMiddleware, goodsController.getProductDetail.bind(goodsController));

  /**
   * @swagger
   * /api/v1/mall/goods/featured-products:
   *   get:
   *     summary: 获取精选商品列表
   *     tags: [Mall-Goods]
   *     parameters:
   *       - in: query
   *         name: count
   *         schema:
   *           type: integer
   *           default: 6
   *         description: 需要获取的商品数量
   *       - in: query
   *         name: random
   *         schema:
   *           type: boolean
   *           default: false
   *         description: 是否随机获取商品
   *     responses:
   *       200:
   *         description: 成功获取商品列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取商品列表成功
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         description: 商品ID
   *                       name:
   *                         type: string
   *                         description: 商品名称
   *                       totalSales:
   *                         type: integer
   *                         description: 商品销量
   *                       totalStock:
   *                         type: integer
   *                         description: 商品库存
   *                       salePrice:
   *                         type: number
   *                         format: float
   *                         description: 销售价格
   *                       marketPrice:
   *                         type: number
   *                         format: float
   *                         description: 市场价格
   *                       imageUrl:
   *                         type: string
   *                         description: 商品图片URL
   */
  router.get('/featured-products', goodsController.getProductsWithImages.bind(goodsController));

  /**
   * @swagger
   * /api/v1/mall/goods/featured-brands:
   *   get:
   *     summary: 获取精选品牌列表
   *     tags: [Mall-Goods]
   *     parameters:
   *       - in: query
   *         name: count
   *         schema:
   *           type: integer
   *           default: 6
   *         description: 需要获取的品牌数量
   *       - in: query
   *         name: random
   *         schema:
   *           type: boolean
   *           default: false
   *         description: 是否随机获取品牌
   *     responses:
   *       200:
   *         description: 成功获取品牌列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取品牌列表成功
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         description: 品牌ID
   *                       name:
   *                         type: string
   *                         description: 品牌名称
   *                       imageUrl:
   *                         type: string
   *                         description: 品牌图片URL
   */
  router.get('/featured-brands', goodsController.getBrandsWithImages.bind(goodsController));
  
  return router;
};
