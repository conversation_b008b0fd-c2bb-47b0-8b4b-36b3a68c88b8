const express = require('express');
const router = express.Router();

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 导入订单控制器
const OrderController = require('../controllers/OrderController');

/**
 * 商城订单路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const orderController = new OrderController(prisma);

  /**
   * @swagger
   * /api/v1/mall/order/query-own-orders:
   *   post:
   *     tags: [商城订单管理]
   *     summary: 查询用户自己的订单
   *     description: 查询当前登录用户的订单列表，支持分页和筛选
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               page:
   *                 type: integer
   *                 description: 页码，默认为1
   *                 default: 1
   *               pageSize:
   *                 type: integer
   *                 description: 每页数量，默认为10
   *                 default: 10
   *               orderStatus:
   *                 type: integer
   *                 description: 订单状态（可选）
   *               orderSn:
   *                 type: string
   *                 description: 订单编号（可选）
   *               sortField:
   *                 type: string
   *                 description: 排序字段，默认为created_at
   *                 default: created_at
   *               sortOrder:
   *                 type: string
   *                 description: 排序方式，desc降序/asc升序，默认为desc
   *                 default: desc
   *     responses:
   *       200:
   *         description: 查询成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/query-own-orders', authMiddleware, orderController.queryOwnOrders.bind(orderController));

  /**
   * @swagger
   * /api/v1/mall/order/status-counts:
   *   get:
   *     tags: [商城订单管理]
   *     summary: 查询用户不同订单状态的数量
   *     description: 查询当前登录用户各个订单状态的数量统计
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 查询成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: object
   *                   properties:
   *                     0:
   *                       type: integer
   *                       description: 待付款订单数量
   *                       example: 2
   *                     1:
   *                       type: integer
   *                       description: 待发货订单数量
   *                       example: 3
   *                     2:
   *                       type: integer
   *                       description: 待收货订单数量
   *                       example: 1
   *                     3:
   *                       type: integer
   *                       description: 已完成订单数量
   *                       example: 5
   *                     4:
   *                       type: integer
   *                       description: 已取消订单数量
   *                       example: 2
   *                     5:
   *                       type: integer
   *                       description: 已关闭订单数量
   *                       example: 0
   *                 message:
   *                   type: string
   *                   example: 查询订单状态数量成功
   *       401:
   *         description: 未登录状态
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/status-counts', authMiddleware, orderController.getOrderStatusCounts.bind(orderController));

  /**
   * @swagger
   * /api/v1/mall/order/{orderId}:
   *   get:
   *     tags: [商城订单管理]
   *     summary: 查询订单详情
   *     description: 查询当前登录用户的指定订单详情
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: orderId
   *         required: true
   *         schema:
   *           type: string
   *         description: 订单ID
   *     responses:
   *       200:
   *         description: 查询成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未登录状态
   *       404:
   *         description: 订单不存在
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/:orderId', authMiddleware, orderController.getOrderDetail.bind(orderController));

  /**
   * @swagger
   * /api/v1/mall/order/{id}/cancel:
   *   post:
   *     tags: [商城订单管理]
   *     summary: 取消订单
   *     description: 用户取消自己的未支付订单
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: 订单ID
   *     requestBody:
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               cancelReason:
   *                 type: string
   *                 description: 取消原因
   *     responses:
   *       200:
   *         description: 取消成功
   *       400:
   *         description: 请求参数错误或订单状态不允许取消
   *       401:
   *         description: 未登录状态
   *       404:
   *         description: 订单不存在
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/:id/cancel', authMiddleware, orderController.cancelOrder.bind(orderController));

  return router;
};
