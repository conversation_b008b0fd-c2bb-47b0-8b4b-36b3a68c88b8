const express = require('express');
const router = express.Router();

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

/**
 * 用户微信绑定路由
 * @param {Object} prisma - Prisma客户端实例
 * @param {Object} services - 服务实例对象
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma, services) => {
  // 实例化控制器
  const UserWechatController = require('../controllers/UserWechatController');
  const userWechatController = new UserWechatController(prisma);
  
  // 初始化服务
  userWechatController.initServices(services.wechatAuthService);
  
  /**
   * @swagger
   * /api/mall/user/wechat/bind/qrcode:
   *   get:
   *     tags: [商城用户微信绑定]
   *     summary: 获取微信绑定二维码
   *     description: 获取用于绑定微信账号的二维码
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 操作成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     ticket:
   *                       type: string
   *                       description: 微信二维码票据
   *                     qrcodeUrl:
   *                       type: string
   *                       description: 二维码图片URL
   *                     sceneStr:
   *                       type: string
   *                       description: 场景值
   *                     expireIn:
   *                       type: integer
   *                       description: 过期时间（秒）
   *       401:
   *         description: 未授权
   *       500:
   *         description: 服务器错误
   */
  router.get('/wechat/bind/qrcode', authMiddleware, userWechatController.getBindQrcode);
  
  /**
   * @swagger
   * /api/mall/user/wechat/bind/status:
   *   get:
   *     tags: [商城用户微信绑定]
   *     summary: 查询微信绑定状态
   *     description: 查询微信扫码绑定的状态
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: sceneStr
   *         required: true
   *         schema:
   *           type: string
   *         description: 场景值
   *     responses:
   *       200:
   *         description: 查询成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 操作成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     status:
   *                       type: string
   *                       description: 绑定状态
   *                       enum: [CREATED, SCANNED, BOUND, EXPIRED]
   *                     userInfo:
   *                       type: object
   *                       description: 微信用户信息
   *       400:
   *         description: 参数错误
   *       401:
   *         description: 未授权
   *       500:
   *         description: 服务器错误
   */
  router.get('/wechat/bind/status', authMiddleware, userWechatController.checkBindStatus);
  
  return router;
};
