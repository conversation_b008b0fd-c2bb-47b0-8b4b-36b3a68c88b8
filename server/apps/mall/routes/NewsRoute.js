/**
 * 商城新闻路由 - 面向前端用户的公共接口
 */
const express = require('express');
const router = express.Router();
const NewsController = require('../controllers/NewsController');

/**
 * 商城新闻路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} Express路由实例
 */
module.exports = (prisma) => {
  const newsController = new NewsController(prisma);

  /**
   * @swagger
   * tags:
   *   name: 商城新闻
   *   description: 商城新闻公共接口
   */

  /**
   * @swagger
   * /api/v1/mall/news/categories:
   *   get:
   *     tags:
   *       - 商城新闻
   *     summary: 获取新闻分类列表
   *     description: 获取启用的新闻分类列表
   *     parameters:
   *       - name: limit
   *         in: query
   *         description: 返回数量限制
   *         required: false
   *         schema:
   *           type: integer
   *           default: 5
   *     responses:
   *       200:
   *         description: 成功获取分类列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "success"
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                       name:
   *                         type: string
   *                       description:
   *                         type: string
   *                       sort_order:
   *                         type: integer
   *                       created_at:
   *                         type: string
   */
  router.get('/categories', newsController.getCategories.bind(newsController));

  /**
   * @swagger
   * /api/v1/mall/news/articles:
   *   get:
   *     tags:
   *       - 商城新闻
   *     summary: 获取新闻文章列表
   *     description: 获取启用的新闻文章列表，支持分页和分类筛选
   *     parameters:
   *       - name: page
   *         in: query
   *         description: 页码
   *         required: false
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         description: 每页数量
   *         required: false
   *         schema:
   *           type: integer
   *           default: 10
   *       - name: category_id
   *         in: query
   *         description: 分类ID
   *         required: false
   *         schema:
   *           type: integer
   *       - name: limit
   *         in: query
   *         description: 限制返回数量（不分页）
   *         required: false
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 成功获取文章列表
   */
  router.get('/articles', newsController.getArticles.bind(newsController));

  /**
   * @swagger
   * /api/v1/mall/news/articles/latest:
   *   get:
   *     tags:
   *       - 商城新闻
   *     summary: 获取最新文章
   *     description: 获取最新的新闻文章列表
   *     parameters:
   *       - name: limit
   *         in: query
   *         description: 返回数量限制
   *         required: false
   *         schema:
   *           type: integer
   *           default: 10
   *       - name: category_id
   *         in: query
   *         description: 分类ID
   *         required: false
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 成功获取最新文章
   */
  router.get('/articles/latest', newsController.getLatestArticles.bind(newsController));

  /**
   * @swagger
   * /api/v1/mall/news/articles/hot:
   *   get:
   *     tags:
   *       - 商城新闻
   *     summary: 获取热门文章
   *     description: 获取热门的新闻文章列表
   *     parameters:
   *       - name: limit
   *         in: query
   *         description: 返回数量限制
   *         required: false
   *         schema:
   *           type: integer
   *           default: 10
   *     responses:
   *       200:
   *         description: 成功获取热门文章
   */
  router.get('/articles/hot', newsController.getHotArticles.bind(newsController));

  /**
   * @swagger
   * /api/v1/mall/news/articles/{id}:
   *   get:
   *     tags:
   *       - 商城新闻
   *     summary: 获取新闻文章详情
   *     description: 根据ID获取新闻文章的详细信息
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 文章ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 成功获取文章详情
   */
  router.get('/articles/:id', newsController.getArticleDetail.bind(newsController));

  /**
   * @swagger
   * /api/v1/mall/news/articles/{id}/related:
   *   get:
   *     tags:
   *       - 商城新闻
   *     summary: 获取相关文章推荐
   *     description: 根据文章ID获取相关文章推荐
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 文章ID
   *         required: true
   *         schema:
   *           type: integer
   *       - name: limit
   *         in: query
   *         description: 返回数量限制
   *         required: false
   *         schema:
   *           type: integer
   *           default: 5
   *     responses:
   *       200:
   *         description: 成功获取相关文章
   */
  router.get('/articles/:id/related', newsController.getRelatedArticles.bind(newsController));

  return router;
};
