/**
 * 运费计算路由
 * 定义运费计算相关的API路由
 */
const express = require('express');
const router = express.Router();

// 导入运费计算控制器
const FreightCalculationController = require('../controllers/FreightCalculationController');

/**
 * 运费计算路由模块
 * @param {Object} prisma Prisma客户端实例
 * @returns {Object} Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const freightCalculationController = new FreightCalculationController(prisma);

  /**
   * @swagger
   * /api/v1/mall/freight-calculation/single-product:
   *   post:
   *     summary: 单商品运费计算
   *     description: 根据商品SPU ID、SKU ID、数量和收货地址计算运费
   *     tags:
   *       - 运费计算
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required: [spuId, skuId, provinceCode]
   *             properties:
   *               spuId:
   *                 type: string
   *                 description: 商品SPU ID
   *                 example: "190791888209580032"
   *               skuId:
   *                 type: string
   *                 description: 商品SKU ID
   *                 example: "192159714207993856"
   *               quantity:
   *                 type: integer
   *                 description: 商品数量
   *                 minimum: 1
   *                 default: 1
   *                 example: 2
   *               provinceCode:
   *                 type: string
   *                 description: 省份编码（6位数字）
   *                 pattern: "^\\d{6}$"
   *                 example: "440000"
   *               cityCode:
   *                 type: string
   *                 description: 城市编码（6位数字，可选）
   *                 pattern: "^\\d{6}$"
   *                 example: "440100"
   *     responses:
   *       200:
   *         description: 计算成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "计算成功"
   *                 data:
   *                   type: object
   *                   description: 运费计算结果
   *       400:
   *         description: 参数错误
   *       404:
   *         description: 商品不存在或配置不存在
   *       500:
   *         description: 服务器错误
   */
  router.post('/single-product', (req, res) => {
    freightCalculationController.calculateSingleProductFreight(req, res);
  });

  /**
   * @swagger
   * /api/v1/mall/freight-calculation/batch:
   *   post:
   *     summary: 批量计算商品运费
   *     description: 批量计算多个商品的运费并提供汇总信息
   *     tags:
   *       - 运费计算
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required: [items, provinceCode]
   *             properties:
   *               items:
   *                 type: array
   *                 description: 商品列表（最多50个）
   *                 maxItems: 50
   *                 items:
   *                   type: object
   *                   required: [spuId, skuId, quantity]
   *                   properties:
   *                     spuId:
   *                       type: string
   *                       description: 商品SPU ID
   *                       example: "190791888209580032"
   *                     skuId:
   *                       type: string
   *                       description: 商品SKU ID
   *                       example: "192159714207993856"
   *                     quantity:
   *                       type: integer
   *                       description: 商品数量
   *                       minimum: 1
   *                       example: 2
   *               provinceCode:
   *                 type: string
   *                 description: 省份编码（6位数字）
   *                 pattern: "^\\d{6}$"
   *                 example: "440000"
   *               cityCode:
   *                 type: string
   *                 description: 城市编码（6位数字，可选）
   *                 pattern: "^\\d{6}$"
   *                 example: "440100"
   *           examples:
   *             batch_request:
   *               summary: 批量计算请求示例
   *               value:
   *                 items:
   *                   - spuId: "190791888209580032"
   *                     skuId: "192159714207993856"
   *                     quantity: 2
   *                   - spuId: "190791888209580033"
   *                     skuId: "192159714207993857"
   *                     quantity: 1
   *                 provinceCode: "440000"
   *                 cityCode: "440100"
   *     responses:
   *       200:
   *         description: 全部计算成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "批量计算成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     totalFreight:
   *                       type: number
   *                       description: 总运费
   *                       example: 150.00
   *                     itemCount:
   *                       type: integer
   *                       description: 商品总数
   *                       example: 2
   *                     successCount:
   *                       type: integer
   *                       description: 成功计算的商品数
   *                       example: 2
   *                     errorCount:
   *                       type: integer
   *                       description: 计算失败的商品数
   *                       example: 0
   *                     items:
   *                       type: array
   *                       description: 商品运费详情
   *                       items:
   *                         type: object
   *                         properties:
   *                           spuId:
   *                             type: string
   *                             description: 商品SPU ID
   *                             example: "190791888209580032"
   *                           skuId:
   *                             type: string
   *                             description: 商品SKU ID
   *                             example: "192159714207993856"
   *                           quantity:
   *                             type: integer
   *                             description: 商品数量
   *                             example: 2
   *                           freight:
   *                             type: number
   *                             description: 运费
   *                             example: 104.00
   *                           isFreeShipping:
   *                             type: boolean
   *                             description: 是否免运费
   *                             example: false
   *                           chargeType:
   *                             type: integer
   *                             description: 计费类型
   *                             example: 1
   *                           matchRegion:
   *                             type: string
   *                             description: 匹配的区域
   *                             example: "广东-广州市"
   *       206:
   *         description: 部分计算成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "批量计算部分成功，2个成功，1个失败"
   *                 data:
   *                   type: object
   *                   description: 批量运费计算结果
   *       400:
   *         description: 参数错误或全部计算失败
   *       500:
   *         description: 服务器错误
   */
  router.post('/batch', (req, res) => {
    freightCalculationController.calculateBatchFreight(req, res);
  });

  /**
   * @swagger
   * /api/v1/mall/freight-calculation/charge-types:
   *   get:
   *     summary: 获取支持的计费类型
   *     description: 获取系统支持的运费计费类型列表
   *     tags:
   *       - 运费计算
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取计费类型成功"
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: integer
   *                         description: 计费类型ID
   *                         example: 1
   *                       name:
   *                         type: string
   *                         description: 计费类型名称
   *                         example: "按件计费"
   *                       description:
   *                         type: string
   *                         description: 计费类型描述
   *                         example: "根据商品件数计算运费"
   *       500:
   *         description: 服务器错误
   */
  router.get('/charge-types', (req, res) => {
    freightCalculationController.getChargeTypes(req, res);
  });

  return router;
}; 