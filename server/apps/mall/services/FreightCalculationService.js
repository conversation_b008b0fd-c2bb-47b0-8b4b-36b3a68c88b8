/**
 * 运费计算服务类
 * 负责处理商品运费计算相关的业务逻辑
 */
const { recursiveSnakeToCamel } = require('../../../shared/utils/format');

class FreightCalculationService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 计算单商品运费
   * @param {Object} params 计算参数
   * @param {string} params.spuId 商品SPU ID
   * @param {string} params.skuId 商品SKU ID
   * @param {number} params.quantity 商品数量
   * @param {string} params.provinceCode 省份编码
   * @param {string} params.cityCode 城市编码
   * @returns {Promise<Object>} 计算结果
   */
  async calculateSingleProductFreight(params) {
    console.log('🚀 开始计算单商品运费，接收参数:', JSON.stringify(params, null, 2));
    
    try {
      // 参数验证
      this.validateParams(params);

      const { spuId, skuId, quantity, provinceCode, cityCode } = params;

      // 1. 查询商品SPU信息
      const spuInfo = await this.getSpuInfo(spuId);
      if (!spuInfo) {
        return {
          code: 404,
          message: '商品SPU不存在',
          data: null
        };
      }

      // 2. 检查是否免运费商品
      if (spuInfo.is_free_shipping === 1) {
        const responseData = {
          freight: 0,
          is_free_shipping: true,
          calculation_details: {
            charge_type: null,
            charge_type_name: '免运费商品',
            calculation_base: 0,
            calculation_unit: null,
            first_fee: 0,
            additional_cost: 0,
            additional_units: 0
          },
          template_info: {
            template_id: spuInfo.goods_freight_template_id?.toString() || null,
            template_name: null,
            config_id: null,
            match_region: '全国免运费'
          }
        };

        return {
          code: 200,
          message: '计算成功',
          data: recursiveSnakeToCamel(responseData)
        };
      }

      // 3. 查询商品SKU信息（获取重量和体积）
      const skuInfo = await this.getSkuInfo(skuId);
      if (!skuInfo) {
        return {
          code: 404,
          message: '商品SKU不存在',
          data: null
        };
      }

      // 4. 验证SKU是否属于该SPU
      if (skuInfo.goods_spu_id.toString() !== spuId.toString()) {
        return {
          code: 400,
          message: 'SKU与SPU不匹配',
          data: null
        };
      }

      // 5. 检查是否有运费模板
      if (!spuInfo.goods_freight_template_id) {
        return {
          code: 400,
          message: '商品未配置运费模板',
          data: null
        };
      }

      // 6. 查询运费模板信息
      const templateInfo = await this.getTemplateInfo(spuInfo.goods_freight_template_id);
      if (!templateInfo) {
        return {
          code: 404,
          message: '运费模板不存在',
          data: null
        };
      }

      // 7. 查找匹配的区域配置
      const { config, match_region, match_type } = await this.findMatchingConfig(
        spuInfo.goods_freight_template_id,
        provinceCode,
        cityCode
      );

      if (!config) {
        return {
          code: 404,
          message: '未找到匹配的运费配置',
          data: null
        };
      }

      // 8. 从SKU信息中获取重量和体积
      const weight = skuInfo.weight ? parseFloat(skuInfo.weight) : null;
      const volume = skuInfo.volume ? parseFloat(skuInfo.volume) : null;

      // 9. 计算运费
      const freightResult = await this.calculateFreight(
        templateInfo.charge_type,
        quantity,
        weight,
        volume,
        config
      );

      const responseData = {
        freight: freightResult.freight,
        is_free_shipping: false,
        calculation_details: {
          charge_type: templateInfo.charge_type,
          charge_type_name: this.getChargeTypeName(templateInfo.charge_type),
          calculation_base: freightResult.calculation_base,
          calculation_unit: freightResult.calculation_unit,
          first_fee: freightResult.first_fee,
          additional_cost: freightResult.additional_cost,
          additional_units: freightResult.additional_units
        },
        template_info: {
          template_id: spuInfo.goods_freight_template_id.toString(),
          template_name: templateInfo.name,
          config_id: config.id.toString(),
          match_region: match_region
        },
        sku_info: {
          weight: weight,
          volume: volume
        }
      };

      return {
        code: 200,
        message: '计算成功',
        data: recursiveSnakeToCamel(responseData)
      };

    } catch (error) {
      console.error('计算运费失败:', error);
      return {
        code: 500,
        message: error.message || '计算运费失败',
        data: null
      };
    }
  }

  /**
   * 验证计算参数
   * @param {Object} params 参数对象
   * @returns {void} 验证失败时抛出异常
   */
  validateParams(params) {
    if (!params.spuId) {
      throw new Error('商品SPU ID不能为空');
    }

    if (!params.skuId) {
      throw new Error('商品SKU ID不能为空');
    }

    if (!params.quantity || params.quantity <= 0) {
      throw new Error('商品数量必须大于0');
    }

    if (!params.provinceCode) {
      throw new Error('省份编码不能为空');
    }

    // cityCode可以为空，支持只按省份计算运费
  }

  /**
   * 查询商品SPU信息
   * @param {string} spuId SPU ID
   * @returns {Promise<Object|null>} SPU信息
   */
  async getSpuInfo(spuId) {
    console.log('🔍 查询SPU信息:', { spuId, spuIdType: typeof spuId });
    
    const result = await this.prisma.goodsSpu.findFirst({
      where: {
        id: BigInt(spuId),
        deleted_at: null
      },
      select: {
        id: true,
        name: true,
        goods_freight_template_id: true,
        is_free_shipping: true
      }
    });
    
    console.log('📦 SPU查询结果:', result ? {
      id: result.id.toString(),
      name: result.name,
      goods_freight_template_id: result.goods_freight_template_id?.toString(),
      is_free_shipping: result.is_free_shipping
    } : null);
    
    return result;
  }

  /**
   * 查询商品SKU信息
   * @param {string} skuId SKU ID
   * @returns {Promise<Object|null>} SKU信息
   */
  async getSkuInfo(skuId) {
    console.log('🔍 查询SKU信息:', { skuId, skuIdType: typeof skuId });
    
    const result = await this.prisma.goodsSku.findFirst({
      where: {
        id: BigInt(skuId),
        deleted_at: null
      },
      select: {
        id: true,
        goods_spu_id: true,
        weight: true,
        volume: true
      }
    });
    
    console.log('📦 SKU查询结果:', result ? {
      id: result.id.toString(),
      goods_spu_id: result.goods_spu_id.toString(),
      weight: result.weight?.toString(),
      volume: result.volume?.toString()
    } : null);
    
    return result;
  }

  /**
   * 查询运费模板信息
   * @param {string} templateId 模板ID
   * @returns {Promise<Object|null>} 模板信息
   */
  async getTemplateInfo(templateId) {
    return await this.prisma.goodsFreightTemplates.findFirst({
      where: {
        id: BigInt(templateId),
        deleted_at: null
      },
      select: {
        id: true,
        name: true,
        charge_type: true
      }
    });
  }

  /**
   * 查找匹配的区域配置
   * @param {string} templateId 模板ID
   * @param {string} provinceCode 省份编码
   * @param {string} cityCode 城市编码（可选）
   * @returns {Promise<Object>} 匹配结果
   */
  async findMatchingConfig(templateId, provinceCode, cityCode) {
    console.log('🔍 开始查找区域配置:', {
      templateId,
      provinceCode,
      cityCode: cityCode || '未提供'
    });

    // 使用3表架构：先查询配置，再通过关联查询区域
    const configs = await this.prisma.goodsFreightConfig.findMany({
      where: {
        freight_template_id: BigInt(templateId),
        deleted_at: null
      },
      include: {
        region_relations: {
          where: {
            deleted_at: null
          }
        }
      }
    });

    console.log('📋 查询到的配置数量:', configs.length);
    configs.forEach((config, index) => {
      console.log(`📦 配置${index + 1}:`, {
        id: config.id.toString(),
        is_default: config.is_default,
        first_item: config.first_item,
        first_fee: config.first_fee.toString(),
        additional_item: config.additional_item,
        additional_fee: config.additional_fee.toString(),
        region_relations_count: config.region_relations.length
      });
      
      config.region_relations.forEach((relation, relIndex) => {
        console.log(`  🌍 区域关系${relIndex + 1}:`, {
          region_code: relation.region_code,
          region_name: relation.region_name,
          parent_name: relation.parent_name
        });
      });
    });

    if (!configs.length) {
      console.log('❌ 未找到任何配置');
      return { config: null, match_region: null };
    }

    // 1. 优先匹配城市级配置（仅当提供了cityCode时）
    if (cityCode) {
      console.log('🏙️ 开始匹配城市级配置...');
      for (const config of configs) {
        const cityMatch = config.region_relations.find(relation => {
          // 处理region_code可能包含多个编码的情况（逗号分隔）
          const regionCodes = relation.region_code.split(',').map(code => code.trim());
          const isMatch = regionCodes.includes(cityCode);
          console.log(`  🔍 检查城市匹配: [${regionCodes.join(', ')}] 包含 ${cityCode} ? ${isMatch}`);
          return isMatch;
        });
        
        if (cityMatch) {
          console.log('✅ 找到城市级匹配:', {
            config_id: config.id.toString(),
            match_region: `${cityMatch.parent_name}-${cityMatch.region_name}`,
            first_fee: config.first_fee.toString()
          });
          return {
            config: {
              id: config.id,
              first_item: config.first_item,
              first_fee: config.first_fee,
              additional_item: config.additional_item,
              additional_fee: config.additional_fee
            },
            match_region: `${cityMatch.parent_name}-${cityMatch.region_name}`,
            match_type: 'CITY'
          };
        }
      }
    } else {
      console.log('🏙️ 跳过城市级匹配（未提供城市编码）');
    }

    // 2. 匹配省级配置
    console.log('🏞️ 开始匹配省级配置...');
    for (const config of configs) {
      const provinceMatch = config.region_relations.find(relation => {
        // 处理region_code可能包含多个编码的情况（逗号分隔）
        const regionCodes = relation.region_code.split(',').map(code => code.trim());
        const isMatch = regionCodes.includes(provinceCode);
        console.log(`  🔍 检查省份匹配: [${regionCodes.join(', ')}] 包含 ${provinceCode} ? ${isMatch}`);
        return isMatch;
      });
      
      if (provinceMatch) {
        console.log('✅ 找到省级匹配:', {
          config_id: config.id.toString(),
          match_region: provinceMatch.region_name,
          first_fee: config.first_fee.toString()
        });
        return {
          config: {
            id: config.id,
            first_item: config.first_item,
            first_fee: config.first_fee,
            additional_item: config.additional_item,
            additional_fee: config.additional_fee
          },
          match_region: provinceMatch.region_name,
          match_type: 'PROVINCE'
        };
      }
    }

    // 3. 使用默认配置（is_default为1的配置）
    console.log('🌐 查找默认配置...');
    const defaultConfig = configs.find(config => {
      const isDefault = config.is_default === 1;
      console.log(`  🔍 检查默认配置: config.id=${config.id.toString()}, is_default=${config.is_default} ? ${isDefault}`);
      return isDefault;
    });
    
    if (defaultConfig) {
      console.log('✅ 使用默认配置:', {
        config_id: defaultConfig.id.toString(),
        first_fee: defaultConfig.first_fee.toString()
      });
      return {
        config: {
          id: defaultConfig.id,
          first_item: defaultConfig.first_item,
          first_fee: defaultConfig.first_fee,
          additional_item: defaultConfig.additional_item,
          additional_fee: defaultConfig.additional_fee
        },
        match_region: '全国默认',
        match_type: 'DEFAULT'
      };
    }

    console.log('❌ 未找到任何匹配的配置');
    return { config: null, match_region: null };
  }

  /**
   * 计算运费
   * @param {number} chargeType 计费类型
   * @param {number} quantity 数量
   * @param {number} weight 重量
   * @param {number} volume 体积
   * @param {Object} config 配置信息
   * @returns {Promise<Object>} 计算结果
   */
  async calculateFreight(chargeType, quantity, weight, volume, config) {
    let calculationBase;
    let calculationUnit;

    // 根据计费类型确定计算基数
    switch (chargeType) {
      case 1: // 按件计费
        calculationBase = quantity;
        calculationUnit = '件';
        break;
      case 2: // 按重量计费
        if (!weight || weight <= 0) {
          throw new Error('按重量计费需要有效的商品重量');
        }
        calculationBase = parseFloat(weight) * quantity;
        calculationUnit = 'kg';
        break;
      case 3: // 按体积计费
        if (!volume || volume <= 0) {
          throw new Error('按体积计费需要有效的商品体积');
        }
        calculationBase = parseFloat(volume) * quantity;
        calculationUnit = 'm³';
        break;
      default:
        throw new Error('不支持的计费类型');
    }

    // 应用阶梯计费公式
    const firstFee = parseFloat(config.first_fee);
    let additionalCost = 0;
    let additionalUnits = 0;

    if (calculationBase > config.first_item) {
      const exceededAmount = calculationBase - config.first_item;
      additionalUnits = Math.ceil(exceededAmount / config.additional_item);
      additionalCost = additionalUnits * parseFloat(config.additional_fee);
    }

    const totalFreight = firstFee + additionalCost;

    return {
      freight: Math.round(totalFreight * 100) / 100, // 保留2位小数
      calculation_base: calculationBase,
      calculation_unit: calculationUnit,
      first_fee: firstFee,
      additional_cost: additionalCost,
      additional_units: additionalUnits
    };
  }

  /**
   * 获取计费类型名称
   * @param {number} chargeType 计费类型
   * @returns {string} 计费类型名称
   */
  getChargeTypeName(chargeType) {
    const typeNames = {
      1: '按件计费',
      2: '按重量计费',
      3: '按体积计费'
    };
    return typeNames[chargeType] || '未知计费类型';
  }

  /**
   * 批量计算商品运费
   * @param {Object} params 计算参数
   * @param {Array} params.items 商品列表
   * @param {string} params.provinceCode 省份编码
   * @param {string} params.cityCode 城市编码（可选）
   * @returns {Promise<Object>} 计算结果
   */
  async calculateBatchFreight(params) {
    console.log('🚀 开始批量计算运费，接收参数:', JSON.stringify(params, null, 2));
    
    try {
      // 参数验证
      this.validateBatchParams(params);

      const { items, provinceCode, cityCode } = params;
      
      // 存储每个商品的计算结果
      const itemResults = [];
      let totalFreight = 0;
      let hasError = false;
      const errors = [];

      // 逐个计算每个商品的运费
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        console.log(`📦 计算第${i + 1}个商品:`, item);

        try {
          // 调用单商品运费计算
          const singleResult = await this.calculateSingleProductFreight({
            spuId: item.spuId,
            skuId: item.skuId,
            quantity: item.quantity,
            provinceCode: provinceCode,
            cityCode: cityCode
          });

          if (singleResult.code === 200) {
            const itemResult = {
              ...item, // 保留原始商品信息
              freightResult: singleResult.data
            };
            itemResults.push(itemResult);
            totalFreight += singleResult.data.freight;
            
            console.log(`✅ 第${i + 1}个商品计算成功，运费: ${singleResult.data.freight}元`);
          } else {
            // 单个商品计算失败
            const errorResult = {
              ...item,
              error: {
                code: singleResult.code,
                message: singleResult.message
              }
            };
            itemResults.push(errorResult);
            errors.push(`商品${i + 1}(SPU:${item.spuId}): ${singleResult.message}`);
            hasError = true;
            
            console.log(`❌ 第${i + 1}个商品计算失败:`, singleResult.message);
          }
        } catch (error) {
          console.error(`第${i + 1}个商品计算异常:`, error);
          const errorResult = {
            ...item,
            error: {
              code: 500,
              message: error.message || '计算运费异常'
            }
          };
          itemResults.push(errorResult);
          errors.push(`商品${i + 1}(SPU:${item.spuId}): ${error.message || '计算运费异常'}`);
          hasError = true;
        }
      }

      // 构建响应数据
      const responseData = {
        total_freight: Math.round(totalFreight * 100) / 100, // 保留2位小数
        item_count: items.length,
        success_count: itemResults.filter(item => !item.error).length,
        error_count: itemResults.filter(item => item.error).length,
        items: itemResults.map(item => {
          if (item.error) {
            // 失败的商品只返回基本信息和错误
            return {
              spu_id: item.spuId,
              sku_id: item.skuId,
              quantity: item.quantity,
              freight: 0,
              error: item.error
            };
          } else {
            // 成功的商品返回精简信息
            return {
              spu_id: item.spuId,
              sku_id: item.skuId,
              quantity: item.quantity,
              freight: item.freightResult.freight,
              is_free_shipping: item.freightResult.isFreeShipping,
              charge_type: item.freightResult.calculationDetails?.chargeType || null,
              match_region: item.freightResult.templateInfo?.matchRegion || null
            };
          }
        })
      };

      console.log('📊 批量计算汇总:', {
        totalFreight: responseData.total_freight,
        successCount: responseData.success_count,
        errorCount: responseData.error_count
      });

      // 根据是否有错误返回不同的状态码
      if (hasError && responseData.success_count === 0) {
        // 全部失败
        return {
          code: 400,
          message: `批量计算失败: ${errors.join('; ')}`,
          data: recursiveSnakeToCamel(responseData)
        };
      } else if (hasError) {
        // 部分失败
        return {
          code: 206, // 206 Partial Content
          message: `批量计算部分成功，${responseData.success_count}个成功，${responseData.error_count}个失败`,
          data: recursiveSnakeToCamel(responseData)
        };
      } else {
        // 全部成功
        return {
          code: 200,
          message: '批量计算成功',
          data: recursiveSnakeToCamel(responseData)
        };
      }

    } catch (error) {
      console.error('批量计算运费失败:', error);
      return {
        code: 500,
        message: error.message || '批量计算运费失败',
        data: null
      };
    }
  }

  /**
   * 验证批量计算参数
   * @param {Object} params 参数对象
   * @returns {void} 验证失败时抛出异常
   */
  validateBatchParams(params) {
    if (!params.items || !Array.isArray(params.items)) {
      throw new Error('商品列表不能为空且必须是数组');
    }

    if (params.items.length === 0) {
      throw new Error('商品列表不能为空');
    }

    if (params.items.length > 50) {
      throw new Error('单次批量计算商品数量不能超过50个');
    }

    if (!params.provinceCode) {
      throw new Error('省份编码不能为空');
    }

    // 验证每个商品项
    params.items.forEach((item, index) => {
      if (!item.spuId) {
        throw new Error(`第${index + 1}个商品的SPU ID不能为空`);
      }
      if (!item.skuId) {
        throw new Error(`第${index + 1}个商品的SKU ID不能为空`);
      }
      if (!item.quantity || item.quantity <= 0) {
        throw new Error(`第${index + 1}个商品的数量必须大于0`);
      }
    });
  }
}

module.exports = FreightCalculationService; 