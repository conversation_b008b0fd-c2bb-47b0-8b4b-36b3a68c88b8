/**
 * 商城用户浏览记录服务类
 */
class BrowseHistoryService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 添加浏览记录
   * @param {Object} data - 浏览记录数据
   * @param {BigInt} data.userId - 用户ID
   * @param {BigInt} data.goodsSpuId - 商品SPU ID
   * @param {BigInt} data.goodsSkuId - 商品SKU ID（可选）
   * @returns {Promise<Object>} - 创建的浏览记录对象
   */
  async addBrowseHistory(data) {
    const { userId, goodsSpuId, goodsSkuId } = data;
    const now = Date.now();

    try {
      // 使用 UPSERT 操作，如果记录存在则更新浏览时间，否则创建新记录
      const result = await this.prisma.$executeRaw`
        INSERT INTO "base"."mall_user_browse_history"
        ("user_id", "goods_spu_id", "goods_sku_id", "browse_time", "created_at", "updated_at")
        VALUES (${userId}, ${goodsSpuId}, ${goodsSkuId || null}, ${now}, ${now}, ${now})
        ON CONFLICT ("user_id", "goods_spu_id")
        DO UPDATE SET
          "browse_time" = ${now},
          "updated_at" = ${now},
          "goods_sku_id" = ${goodsSkuId || null}
      `;

      return { success: true, message: '浏览记录添加成功' };
    } catch (error) {
      console.error('添加浏览记录失败:', error);
      throw new Error('添加浏览记录失败');
    }
  }

  /**
   * 获取用户浏览记录列表
   * @param {BigInt} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {Number} options.page - 页码，默认1
   * @param {Number} options.pageSize - 每页数量，默认20
   * @param {Number} options.limit - 限制数量（用于获取最近几条记录）
   * @returns {Promise<Object>} - 浏览记录列表和分页信息
   */
  async getBrowseHistory(userId, options = {}) {
    const { page = 1, pageSize = 20, limit } = options;
    
    try {
      let query = `
        SELECT 
          h.id,
          h.user_id,
          h.goods_spu_id,
          h.goods_sku_id,
          h.browse_time,
          h.created_at,
          h.updated_at,
          s.name as product_name,
          s.subtitle as product_subtitle,
          s.slug as product_slug,
          s.status as product_status,
          -- 获取商品主图
          (
            SELECT gi.image_url
            FROM "base"."goods_images" gi
            WHERE gi.goods_spu_id = s.id
              AND gi.is_default = true
            LIMIT 1
          ) as product_image,
          -- 获取商品价格（取最低价格的SKU）
          (
            SELECT MIN(gsku.sales_price)
            FROM "base"."goods_skus" gsku
            WHERE gsku.goods_spu_id = s.id
              AND gsku.deleted_at IS NULL
              AND gsku.is_enabled = 1
          ) as product_price,
          -- 获取商品原价（取最低原价的SKU）
          (
            SELECT MIN(gsku.market_price)
            FROM "base"."goods_skus" gsku
            WHERE gsku.goods_spu_id = s.id
              AND gsku.deleted_at IS NULL
              AND gsku.is_enabled = 1
          ) as product_original_price
        FROM "base"."mall_user_browse_history" h
        LEFT JOIN "base"."goods_spus" s ON h.goods_spu_id = s.id
        WHERE h.user_id = $1 
          AND s.deleted_at IS NULL
          AND s.status = 1
        ORDER BY h.browse_time DESC
      `;

      let queryParams = [userId];
      
      if (limit) {
        query += ` LIMIT $2`;
        queryParams.push(limit);
      } else {
        const offset = (page - 1) * pageSize;
        query += ` LIMIT $2 OFFSET $3`;
        queryParams.push(pageSize, offset);
      }

      const records = await this.prisma.$queryRawUnsafe(query, ...queryParams);

      // 如果不是限制查询，还需要获取总数
      let total = 0;
      if (!limit) {
        const countResult = await this.prisma.$queryRawUnsafe(`
          SELECT COUNT(*) as count
          FROM "base"."mall_user_browse_history" h
          LEFT JOIN "base"."goods_spus" s ON h.goods_spu_id = s.id
          WHERE h.user_id = $1 
            AND s.deleted_at IS NULL
            AND s.status = 1
        `, userId);
        total = parseInt(countResult[0]?.count || 0);
      }

      // 格式化返回数据
      const formattedRecords = records.map(record => ({
        id: record.goods_spu_id.toString(),
        productId: record.goods_spu_id.toString(),
        name: record.product_name,
        subtitle: record.product_subtitle,
        slug: record.product_slug,
        image: record.product_image || '',
        price: parseFloat(record.product_price || 0),
        originalPrice: parseFloat(record.product_original_price || 0),
        viewTime: record.browse_time,
        createdAt: record.created_at,
        updatedAt: record.updated_at,
        status: record.product_status
      }));

      return {
        success: true,
        data: formattedRecords,
        pagination: limit ? null : {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } catch (error) {
      console.error('获取浏览记录失败:', error);
      throw new Error('获取浏览记录失败');
    }
  }

  /**
   * 删除单个浏览记录
   * @param {BigInt} userId - 用户ID
   * @param {BigInt} goodsSpuId - 商品SPU ID
   * @returns {Promise<Object>} - 删除结果
   */
  async removeBrowseHistory(userId, goodsSpuId) {
    try {
      const result = await this.prisma.$executeRaw`
        DELETE FROM "base"."mall_user_browse_history"
        WHERE "user_id" = ${userId} AND "goods_spu_id" = ${goodsSpuId}
      `;

      return { success: true, message: '浏览记录删除成功' };
    } catch (error) {
      console.error('删除浏览记录失败:', error);
      throw new Error('删除浏览记录失败');
    }
  }

  /**
   * 批量删除浏览记录
   * @param {BigInt} userId - 用户ID
   * @param {Array<BigInt>} goodsSpuIds - 商品SPU ID数组
   * @returns {Promise<Object>} - 删除结果
   */
  async removeBrowseHistoryBatch(userId, goodsSpuIds) {
    try {
      if (!goodsSpuIds || goodsSpuIds.length === 0) {
        return { success: false, message: '请选择要删除的记录' };
      }

      // 确保所有ID都是BigInt类型
      const validGoodsSpuIds = goodsSpuIds.filter(id => id !== null && id !== undefined);
      
      if (validGoodsSpuIds.length === 0) {
        return { success: false, message: '无有效记录可删除' };
      }

      // 构建删除条件
      const placeholders = validGoodsSpuIds.map((_, index) => `$${index + 2}`).join(',');
      const query = `
        DELETE FROM "base"."mall_user_browse_history"
        WHERE "user_id" = $1 AND "goods_spu_id" IN (${placeholders})
      `;

      const result = await this.prisma.$executeRawUnsafe(query, userId, ...validGoodsSpuIds);

      return { success: true, message: `成功删除${validGoodsSpuIds.length}条浏览记录` };
    } catch (error) {
      console.error('批量删除浏览记录失败:', error);
      throw new Error('批量删除浏览记录失败');
    }
  }

  /**
   * 清空用户所有浏览记录
   * @param {BigInt} userId - 用户ID
   * @returns {Promise<Object>} - 删除结果
   */
  async clearBrowseHistory(userId) {
    try {
      const result = await this.prisma.$executeRaw`
        DELETE FROM "base"."mall_user_browse_history"
        WHERE "user_id" = ${userId}
      `;

      return { success: true, message: '浏览记录清空成功' };
    } catch (error) {
      console.error('清空浏览记录失败:', error);
      throw new Error('清空浏览记录失败');
    }
  }

  /**
   * 获取用户浏览记录统计
   * @param {BigInt} userId - 用户ID
   * @returns {Promise<Object>} - 统计信息
   */
  async getBrowseHistoryStats(userId) {
    try {
      const result = await this.prisma.$queryRawUnsafe(`
        SELECT 
          COUNT(*) as total_count,
          COUNT(DISTINCT DATE(to_timestamp(browse_time / 1000))) as browse_days,
          MAX(browse_time) as last_browse_time
        FROM "base"."mall_user_browse_history" h
        LEFT JOIN "base"."goods_spus" s ON h.goods_spu_id = s.id
        WHERE h.user_id = $1 
          AND s.deleted_at IS NULL
          AND s.status = 1
      `, userId);

      const stats = result[0] || {};
      
      return {
        success: true,
        data: {
          totalCount: parseInt(stats.total_count || 0),
          browseDays: parseInt(stats.browse_days || 0),
          lastBrowseTime: stats.last_browse_time || null
        }
      };
    } catch (error) {
      console.error('获取浏览记录统计失败:', error);
      throw new Error('获取浏览记录统计失败');
    }
  }
}

module.exports = BrowseHistoryService;
