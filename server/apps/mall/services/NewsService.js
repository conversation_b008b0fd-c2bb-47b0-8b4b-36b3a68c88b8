/**
 * 商城新闻服务类 - 面向前端用户的公共接口
 */
const { Prisma } = require('@prisma/client');

class NewsService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取启用的新闻分类列表
   * @param {Object} params 查询参数
   * @returns {Promise<Array>} 分类列表
   */
  async getEnabledCategories(params = {}) {
    try {
      const { limit = 5 } = params;

      const sql = `
        SELECT id, name, description, sort_order, created_at
        FROM base.mall_news_categories 
        WHERE is_enabled = 1 AND deleted_at IS NULL
        ORDER BY sort_order DESC, created_at DESC
        LIMIT ${limit}
      `;
      
      const categories = await this.prisma.$queryRaw(Prisma.raw(sql));

      return categories.map(item => ({
        id: item.id.toString(),
        name: item.name,
        description: item.description,
        sort_order: item.sort_order,
        created_at: item.created_at.toString()
      }));
    } catch (error) {
      console.error('获取新闻分类失败:', error);
      throw new Error(`获取新闻分类失败: ${error.message}`);
    }
  }

  /**
   * 获取启用的新闻文章列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getEnabledArticles(params = {}) {
    try {
      const {
        page = 1,
        pageSize = 10,
        category_id,
        limit
      } = params;

      // 构建WHERE条件
      let whereClause = 'WHERE a.is_enabled = 1 AND a.status = 1 AND a.deleted_at IS NULL';
      
      if (category_id) {
        whereClause += ` AND a.category_id = ${BigInt(category_id)}`;
      }

      // 如果指定了limit，则不使用分页
      if (limit) {
        const sql = `
          SELECT a.id, a.title, a.summary, a.image_url, a.view_count, a.category_id,
                 a.created_at, a.updated_at, c.name as category_name
          FROM base.mall_news_articles a
          LEFT JOIN base.mall_news_categories c ON a.category_id = c.id
          ${whereClause}
          ORDER BY a.updated_at DESC, a.sort_order DESC, a.created_at DESC
          LIMIT ${limit}
        `;

        const articles = await this.prisma.$queryRaw(Prisma.raw(sql));

        return articles.map(item => ({
          id: item.id.toString(),
          title: item.title,
          summary: item.summary,
          image_url: item.image_url,
          view_count: item.view_count || 0,
          category_id: item.category_id.toString(),
          category_name: item.category_name,
          created_at: item.created_at.toString(),
          updated_at: item.updated_at.toString()
        }));
      }

      // 分页查询
      const countSql = `
        SELECT COUNT(*) as total 
        FROM base.mall_news_articles a
        ${whereClause}
      `;
      
      const countResult = await this.prisma.$queryRaw(Prisma.raw(countSql));
      const total = Number(countResult[0]?.total || 0);

      const offset = (page - 1) * pageSize;
      const dataSql = `
        SELECT a.id, a.title, a.summary, a.image_url, a.view_count, a.category_id,
               a.created_at, a.updated_at, c.name as category_name
        FROM base.mall_news_articles a
        LEFT JOIN base.mall_news_categories c ON a.category_id = c.id
        ${whereClause}
        ORDER BY a.updated_at DESC, a.sort_order DESC, a.created_at DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `;

      const articles = await this.prisma.$queryRaw(Prisma.raw(dataSql));

      const totalPages = Math.ceil(total / pageSize);

      return {
        items: articles.map(item => ({
          id: item.id.toString(),
          title: item.title,
          summary: item.summary,
          image_url: item.image_url,
          view_count: item.view_count || 0,
          category_id: item.category_id.toString(),
          category_name: item.category_name,
          created_at: item.created_at.toString(),
          updated_at: item.updated_at.toString()
        })),
        pageInfo: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          totalPages,
          current: parseInt(page)
        }
      };
    } catch (error) {
      console.error('获取新闻文章列表失败:', error);
      throw new Error(`获取新闻文章列表失败: ${error.message}`);
    }
  }

  /**
   * 获取新闻文章详情
   * @param {string|number} id 文章ID
   * @returns {Promise<Object>} 文章详情
   */
  async getArticleDetail(id) {
    try {
      const sql = `
        SELECT a.*, c.name as category_name 
        FROM base.mall_news_articles a
        LEFT JOIN base.mall_news_categories c ON a.category_id = c.id
        WHERE a.id = ${BigInt(id)} AND a.is_enabled = 1 AND a.status = 1 AND a.deleted_at IS NULL
        LIMIT 1
      `;
      
      const result = await this.prisma.$queryRaw(Prisma.raw(sql));
      
      if (!result || result.length === 0) {
        throw new Error('新闻文章不存在或已下线');
      }

      const article = result[0];
      
      // 增加浏览次数
      await this.incrementViewCount(id);

      return {
        id: article.id.toString(),
        title: article.title,
        summary: article.summary,
        content: article.content,
        image_url: article.image_url,
        category_id: article.category_id.toString(),
        category_name: article.category_name,
        view_count: article.view_count || 0,
        sort_order: article.sort_order,
        seo_title: article.seo_title,
        seo_keywords: article.seo_keywords,
        seo_description: article.seo_description,
        created_at: article.created_at.toString(),
        updated_at: article.updated_at.toString()
      };
    } catch (error) {
      console.error('获取新闻文章详情失败:', error);
      throw new Error(`获取新闻文章详情失败: ${error.message}`);
    }
  }

  /**
   * 增加文章浏览次数
   * @param {string|number} id 文章ID
   * @returns {Promise<void>}
   */
  async incrementViewCount(id) {
    try {
      await this.prisma.$queryRaw`
        UPDATE base.mall_news_articles 
        SET view_count = view_count + 1
        WHERE id = ${BigInt(id)} AND deleted_at IS NULL
      `;
    } catch (error) {
      console.error('增加浏览次数失败:', error);
      // 不抛出错误，避免影响主要功能
    }
  }

  /**
   * 获取相关文章推荐
   * @param {string|number} articleId 当前文章ID
   * @param {string|number} categoryId 分类ID
   * @param {number} limit 推荐数量
   * @returns {Promise<Array>} 相关文章列表
   */
  async getRelatedArticles(articleId, categoryId, limit = 5) {
    try {
      const sql = `
        SELECT a.id, a.title, a.summary, a.image_url, a.view_count, 
               a.created_at, c.name as category_name
        FROM base.mall_news_articles a
        LEFT JOIN base.mall_news_categories c ON a.category_id = c.id
        WHERE a.category_id = ${BigInt(categoryId)} 
          AND a.id != ${BigInt(articleId)}
          AND a.is_enabled = 1 
          AND a.status = 1 
          AND a.deleted_at IS NULL
        ORDER BY a.view_count DESC, a.updated_at DESC
        LIMIT ${limit}
      `;
      
      const articles = await this.prisma.$queryRaw(Prisma.raw(sql));

      return articles.map(item => ({
        id: item.id.toString(),
        title: item.title,
        summary: item.summary,
        image_url: item.image_url,
        view_count: item.view_count || 0,
        category_name: item.category_name,
        created_at: item.created_at.toString()
      }));
    } catch (error) {
      console.error('获取相关文章失败:', error);
      return [];
    }
  }
}

module.exports = NewsService;
