/**
 * 库存管理服务
 * 负责处理商品库存和销量的增减操作
 * 
 * 主要功能：
 * 1. 扣减库存（下单时）
 * 2. 恢复库存（取消订单时）
 * 3. 更新销量（完成订单时）
 * 4. 批量库存操作
 */

const DecimalUtils = require('../../../core/utils/DecimalUtils');

class InventoryService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 扣减商品库存（下单时调用）
   * @param {Array} orderItems - 订单项数组
   * @param {Object} prisma - Prisma 事务实例（可选）
   * @returns {Promise<Object>} - 操作结果
   */
  async decreaseInventory(orderItems, prisma = null) {
    const client = prisma || this.prisma;
    const currentTimestamp = BigInt(Date.now());
    const processedItems = [];

    try {
      for (const orderItem of orderItems) {
        const { goods_spu_id, goods_sku_id, quantity } = orderItem;

        // 1. 检查并扣减 SKU 库存
        if (goods_sku_id) {
          const sku = await client.goods_skus.findUnique({
            where: { id: BigInt(goods_sku_id) },
            select: { id: true, stock: true, sales_volume: true }
          });

          if (!sku) {
            throw new Error(`SKU ${goods_sku_id} 不存在`);
          }

          if (sku.stock < quantity) {
            throw new Error(`SKU ${goods_sku_id} 库存不足，当前库存: ${sku.stock}, 需要: ${quantity}`);
          }

          // 扣减 SKU 库存，增加销量
          await client.goods_skus.update({
            where: { id: BigInt(goods_sku_id) },
            data: {
              stock: { decrement: quantity },
              sales_volume: { increment: quantity },
              updated_at: currentTimestamp
            }
          });


        }

        // 2. 检查并扣减 SPU 总库存
        if (goods_spu_id) {
          const spu = await client.goods_spus.findUnique({
            where: { id: BigInt(goods_spu_id) },
            select: { id: true, total_stock: true, total_sales: true }
          });

          if (!spu) {
            throw new Error(`SPU ${goods_spu_id} 不存在`);
          }

          if (spu.total_stock < quantity) {
            throw new Error(`SPU ${goods_spu_id} 总库存不足，当前库存: ${spu.total_stock}, 需要: ${quantity}`);
          }

          // 扣减 SPU 总库存，增加总销量
          await client.goods_spus.update({
            where: { id: BigInt(goods_spu_id) },
            data: {
              total_stock: { decrement: quantity },
              total_sales: { increment: quantity },
              updated_at: currentTimestamp
            }
          });


        }

        processedItems.push({
          goods_spu_id,
          goods_sku_id,
          quantity,
          status: 'success'
        });
      }



      return {
        success: true,
        message: '库存扣减成功',
        data: {
          processedItems: processedItems.length,
          details: processedItems
        }
      };

    } catch (error) {
      console.error('库存扣减失败:', error);
      throw new Error(`库存扣减失败: ${error.message}`);
    }
  }

  /**
   * 恢复商品库存（取消订单时调用）
   * @param {Array} orderItems - 订单项数组
   * @param {Object} prisma - Prisma 事务实例（可选）
   * @returns {Promise<Object>} - 操作结果
   */
  async restoreInventory(orderItems, prisma = null) {
    const client = prisma || this.prisma;
    const currentTimestamp = BigInt(Date.now());
    const processedItems = [];

    try {
      for (const orderItem of orderItems) {
        const { goods_spu_id, goods_sku_id, quantity } = orderItem;

        // 1. 恢复 SKU 库存和销量
        if (goods_sku_id) {
          try {
            await client.goods_skus.update({
              where: { id: BigInt(goods_sku_id) },
              data: {
                stock: { increment: quantity },           // 增加库存
                sales_volume: { decrement: quantity },    // 减少销量（但不能小于0）
                updated_at: currentTimestamp
              }
            });

          } catch (skuError) {
            console.error(`SKU恢复失败 ID: ${goods_sku_id}`, skuError);
            // SKU 更新失败不应该阻止整个流程，记录错误继续执行
          }
        }

        // 2. 恢复 SPU 总库存和总销量
        if (goods_spu_id) {
          try {
            await client.goods_spus.update({
              where: { id: BigInt(goods_spu_id) },
              data: {
                total_stock: { increment: quantity },     // 增加总库存
                total_sales: { decrement: quantity },     // 减少总销量（但不能小于0）
                updated_at: currentTimestamp
              }
            });

          } catch (spuError) {
            console.error(`SPU恢复失败 ID: ${goods_spu_id}`, spuError);
            // SPU 更新失败不应该阻止整个流程，记录错误继续执行
          }
        }

        processedItems.push({
          goods_spu_id,
          goods_sku_id,
          quantity,
          status: 'success'
        });
      }



      return {
        success: true,
        message: '库存恢复成功',
        data: {
          processedItems: processedItems.length,
          details: processedItems
        }
      };

    } catch (error) {
      console.error('库存恢复失败:', error);
      throw new Error(`库存恢复失败: ${error.message}`);
    }
  }

  /**
   * 检查商品库存是否充足
   * @param {Array} orderItems - 订单项数组
   * @returns {Promise<Object>} - 检查结果
   */
  async checkInventory(orderItems) {
    try {

      const checkResults = [];
      let allAvailable = true;

      for (const orderItem of orderItems) {
        const { goods_spu_id, goods_sku_id, quantity } = orderItem;
        
        let skuAvailable = true;
        let spuAvailable = true;
        let skuStock = 0;
        let spuStock = 0;

        // 检查 SKU 库存
        if (goods_sku_id) {
          const sku = await this.prisma.goods_skus.findUnique({
            where: { id: BigInt(goods_sku_id) },
            select: { id: true, stock: true, sales_volume: true }
          });

          if (!sku) {
            skuAvailable = false;
            allAvailable = false;
          } else {
            skuStock = sku.stock;
            if (sku.stock < quantity) {
              skuAvailable = false;
              allAvailable = false;
            }
          }
        }

        // 检查 SPU 库存
        if (goods_spu_id) {
          const spu = await this.prisma.goods_spus.findUnique({
            where: { id: BigInt(goods_spu_id) },
            select: { id: true, total_stock: true, total_sales: true }
          });

          if (!spu) {
            spuAvailable = false;
            allAvailable = false;
          } else {
            spuStock = spu.total_stock;
            if (spu.total_stock < quantity) {
              spuAvailable = false;
              allAvailable = false;
            }
          }
        }

        checkResults.push({
          goods_spu_id,
          goods_sku_id,
          quantity,
          skuAvailable,
          spuAvailable,
          skuStock,
          spuStock,
          sufficient: skuAvailable && spuAvailable
        });
      }



      return {
        success: true,
        message: allAvailable ? '库存检查通过' : '部分商品库存不足',
        data: {
          allAvailable,
          checkResults
        }
      };

    } catch (error) {
      console.error('库存检查失败:', error);
      throw new Error(`库存检查失败: ${error.message}`);
    }
  }

  /**
   * 获取商品库存信息
   * @param {BigInt} spuId - SPU ID
   * @param {BigInt} skuId - SKU ID（可选）
   * @returns {Promise<Object>} - 库存信息
   */
  async getInventoryInfo(spuId, skuId = null) {
    try {
      const result = {
        spu: null,
        sku: null
      };

      // 获取 SPU 信息
      if (spuId) {
        const spu = await this.prisma.goods_spus.findUnique({
          where: { id: BigInt(spuId) },
          select: {
            id: true,
            name: true,
            total_stock: true,
            total_sales: true,
            status: true
          }
        });

        if (spu) {
          result.spu = {
            id: DecimalUtils.handleBigInt(spu.id),
            name: spu.name,
            totalStock: spu.total_stock,
            totalSales: spu.total_sales,
            status: spu.status
          };
        }
      }

      // 获取 SKU 信息
      if (skuId) {
        const sku = await this.prisma.goods_skus.findUnique({
          where: { id: BigInt(skuId) },
          select: {
            id: true,
            sku_name: true,
            stock: true,
            sales_volume: true,
            is_enabled: true
          }
        });

        if (sku) {
          result.sku = {
            id: DecimalUtils.handleBigInt(sku.id),
            name: sku.sku_name,
            stock: sku.stock,
            salesVolume: sku.sales_volume,
            isEnabled: sku.is_enabled
          };
        }
      }

      return {
        success: true,
        message: '获取库存信息成功',
        data: result
      };

    } catch (error) {
      console.error('获取库存信息失败:', error);
      throw new Error(`获取库存信息失败: ${error.message}`);
    }
  }
}

module.exports = InventoryService; 