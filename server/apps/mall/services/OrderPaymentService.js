const PaymentRecordService = require('../../master/services/PaymentRecordService');
const WechatPayService = require('../../master/services/WechatPayService');
const OrderService = require('./OrderService');

/**
 * 商城订单支付服务
 */
class OrderPaymentService {
  constructor(prisma) {
    this.prisma = prisma;
    this.wechatPayService = new WechatPayService();
    this.orderService = new OrderService(prisma);
  }

  /**
   * 创建支付
   * @param {string} orderId - 订单ID
   * @param {number} paymentMethodId - 支付方式ID
   * @param {string} userId - 用户ID
   * @param {Object} options - 支付选项
   * @param {boolean} options.useBalance - 是否使用余额
   * @param {number} options.balanceAmount - 使用的余额金额
   * @returns {Object} 支付结果
   */
  async createPayment(orderId, paymentMethodId, userId, options = {}) {
    try {
      const { useBalance = false, balanceAmount = 0 } = options;

      // 验证订单是否属于当前用户
      const order = await this.orderService.getOrderDetail(BigInt(orderId), userId);
      if (!order) {
        throw new Error('订单不存在或无权限访问');
      }

      // 检查订单状态
      if (order.order_status !== 0) {
        throw new Error('订单状态不允许支付');
      }

      if (order.payment_status === 1) {
        throw new Error('订单已支付，无需重复支付');
      }

      const totalAmount = parseFloat(order.total_amount);
      let onlinePaymentAmount = totalAmount;

      // 如果使用余额支付，验证余额并计算在线支付金额
      if (useBalance && balanceAmount > 0) {
        // 验证用户余额
        const userBalance = await this.getUserBalance(userId);
        if (userBalance < balanceAmount) {
          throw new Error('用户余额不足');
        }

        if (balanceAmount > totalAmount) {
          throw new Error('余额使用金额不能超过订单总金额');
        }

        // 计算在线支付金额
        onlinePaymentAmount = totalAmount - balanceAmount;
      }

      // 创建在线支付记录（如果需要在线支付）
      let paymentRecord = null;
      if (onlinePaymentAmount > 0) {
        const paymentRecordResult = await PaymentRecordService.createPaymentRecord({
          orderId,
          userId,
          paymentMethodId,
          paymentAmount: onlinePaymentAmount,
          description: `订单支付-${order.id}${useBalance ? '(组合支付-在线部分)' : ''}`
        });

        if (!paymentRecordResult.success) {
          throw new Error(paymentRecordResult.message);
        }

        paymentRecord = paymentRecordResult.data;
      }

      let paymentResult = null;

      // 如果需要在线支付，根据支付方式处理
      if (onlinePaymentAmount > 0 && paymentRecord) {
        switch (paymentMethodId) {
          case 1: // 微信支付
            // 确保微信支付配置已加载
            await this.wechatPayService.initConfig();
            if (!this.wechatPayService.config) {
              throw new Error('微信支付配置加载失败');
            }

            paymentResult = await this.wechatPayService.createNativeOrder({
              outTradeNo: paymentRecord.payment_sn,
              description: `订单支付-${order.id}${useBalance ? '(组合支付)' : ''}`,
              amount: Math.round(onlinePaymentAmount * 100), // 转换为分
              notifyUrl: `${process.env.API_BASE_URL || 'http://localhost:4000'}/api/v1/master/wechat-pay/notify`
            });
            break;
          case 2: // 支付宝
            throw new Error('支付宝支付暂未开放');
          case 3: // 银行卡
            throw new Error('银行卡支付暂未开放');
          case 4: // 货到付款
            // 货到付款不需要在线支付
            break;
          case 5: // 其他
            throw new Error('其他支付方式暂未开放');
          default:
            throw new Error('不支持的支付方式');
        }
      }

      // 如果只使用余额支付（无需在线支付）
      if (useBalance && onlinePaymentAmount === 0) {
        // 直接处理余额支付
        await this.processBalancePayment(orderId, userId, balanceAmount, order.id.toString());

        return {
          paymentRecord: null,
          paymentResult: null,
          balancePaymentCompleted: true,
          message: '余额支付完成'
        };
      }

      return {
        paymentRecord,
        paymentResult,
        useBalance,
        balanceAmount,
        onlinePaymentAmount
      };
    } catch (error) {
      console.error('创建支付失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户余额
   * @param {string} userId - 用户ID
   * @returns {number} 用户余额
   */
  async getUserBalance(userId) {
    try {
      const user = await this.prisma.$queryRaw`
        SELECT balance
        FROM "base"."mall_user"
        WHERE "id" = ${BigInt(userId)}
        AND "deleted_at" IS NULL
      `;

      if (!user || user.length === 0) {
        throw new Error('用户不存在');
      }

      return parseFloat(user[0].balance || 0);
    } catch (error) {
      console.error('获取用户余额失败:', error);
      throw error;
    }
  }

  /**
   * 处理余额支付
   * @param {string} orderId - 订单ID
   * @param {string} userId - 用户ID
   * @param {number} balanceAmount - 余额支付金额
   * @param {string} orderSn - 订单编号
   * @returns {Object} 处理结果
   */
  async processBalancePayment(orderId, userId, balanceAmount, orderSn) {
    try {
      // 开始事务
      return await this.prisma.$transaction(async (prisma) => {
        // 1. 扣除用户余额
        const updateResult = await prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET balance = balance - ${balanceAmount}
          WHERE "id" = ${BigInt(userId)}
          AND balance >= ${balanceAmount}
          AND "deleted_at" IS NULL
        `;

        if (updateResult === 0) {
          throw new Error('余额不足或用户不存在');
        }

        // 2. 创建余额支付记录
        const balancePaymentSn = this.generatePaymentSn();
        const balancePaymentRecord = await prisma.payment_records.create({
          data: {
            payment_sn: balancePaymentSn,
            order_id: BigInt(orderId),
            user_id: BigInt(userId),
            payment_method_id: 6, // 6: 余额支付
            payment_method_name: '余额支付',
            payment_amount: balanceAmount,
            payment_status: 2, // 2: 支付成功
            payment_time: BigInt(Date.now()),
            description: `订单余额支付-${orderSn}`,
            created_by: BigInt(userId)
          }
        });

        // 3. 更新订单状态
        await prisma.orders.update({
          where: { id: BigInt(orderId) },
          data: {
            order_status: 1, // 1: 待发货
            payment_status: 1, // 1: 已支付
            payment_method_id: 6, // 余额支付
            payment_sn: balancePaymentSn,
            paid_amount: balanceAmount,
            paid_at: BigInt(Date.now())
          }
        });

        return balancePaymentRecord;
      });
    } catch (error) {
      console.error('处理余额支付失败:', error);
      throw error;
    }
  }

  /**
   * 处理组合支付完成
   * @param {string} onlinePaymentSn - 在线支付流水号
   * @returns {Object} 处理结果
   */
  async processComboPaymentCompletion(onlinePaymentSn) {
    try {
      // 获取在线支付记录
      const paymentRecordResult = await PaymentRecordService.getPaymentRecord(onlinePaymentSn);
      if (!paymentRecordResult.success) {
        throw new Error('支付记录不存在');
      }

      const onlinePaymentRecord = paymentRecordResult.data;

      // 检查是否是组合支付（通过描述判断）
      if (!onlinePaymentRecord.description.includes('组合支付')) {
        return { isComboPayment: false };
      }

      // 获取订单信息
      const order = await this.orderService.getOrderDetail(
        onlinePaymentRecord.order_id,
        onlinePaymentRecord.user_id.toString()
      );

      if (!order) {
        throw new Error('订单不存在');
      }

      // 计算余额支付金额（订单总金额 - 在线支付金额）
      const totalAmount = parseFloat(order.total_amount);
      const onlineAmount = parseFloat(onlinePaymentRecord.payment_amount);
      const balanceAmount = totalAmount - onlineAmount;

      if (balanceAmount > 0) {
        // 处理余额支付
        await this.processBalancePayment(
          onlinePaymentRecord.order_id.toString(),
          onlinePaymentRecord.user_id.toString(),
          balanceAmount,
          order.id.toString()
        );
      }

      return {
        isComboPayment: true,
        balanceAmount,
        message: '组合支付完成'
      };
    } catch (error) {
      console.error('处理组合支付完成失败:', error);
      throw error;
    }
  }

  /**
   * 生成支付流水号
   * @returns {string} 支付流水号
   */
  generatePaymentSn() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `PAY${timestamp}${random}`;
  }

  /**
   * 查询支付状态
   * @param {string} paymentSn - 支付流水号
   * @param {string} userId - 用户ID
   * @returns {Object} 支付状态
   */
  async queryPaymentStatus(paymentSn, userId) {
    try {
      // 获取支付记录
      const paymentRecordResult = await PaymentRecordService.getPaymentRecord(paymentSn);
      if (!paymentRecordResult.success) {
        throw new Error(paymentRecordResult.message);
      }

      const paymentRecord = paymentRecordResult.data;

      // 验证用户权限
      if (paymentRecord.user_id.toString() !== userId.toString()) {
        throw new Error('无权限查询此支付记录');
      }

      // 如果支付已完成，直接返回状态
      if (paymentRecord.payment_status === 2) {
        return {
          paymentStatus: paymentRecord.payment_status,
          paymentSn: paymentRecord.payment_sn,
          isExpired: false
        };
      }

      // 检查支付是否过期（30分钟）
      const now = Date.now();
      const createdAt = parseInt(paymentRecord.created_at.toString());
      const isExpired = (now - createdAt) > 30 * 60 * 1000;

      if (isExpired && paymentRecord.payment_status === 0) {
        // 更新支付记录为过期
        await PaymentRecordService.updatePaymentStatus(paymentSn, {
          status: 4 // 4: 已过期
        });
        return {
          paymentStatus: 4,
          paymentSn: paymentRecord.payment_sn,
          isExpired: true
        };
      }

      // 查询第三方支付状态
      if (paymentRecord.payment_method_id === 1) { // 微信支付
        try {
          // 确保微信支付配置已加载
          await this.wechatPayService.initConfig();
          if (!this.wechatPayService.config) {
            throw new Error('微信支付配置加载失败');
          }

          const wechatStatus = await this.wechatPayService.queryOrder(paymentSn);
          if (wechatStatus.trade_state === 'SUCCESS') {
            // 更新支付状态
            await PaymentRecordService.updatePaymentStatus(paymentSn, {
              status: 2 // 2: 已支付
            });

            // 检查是否是组合支付，如果是则处理余额支付
            try {
              const comboResult = await this.processComboPaymentCompletion(paymentSn);
              if (comboResult.isComboPayment) {
                console.log('组合支付处理完成:', comboResult);
              }
            } catch (error) {
              console.error('处理组合支付失败:', error);
              // 不影响主支付流程，只记录错误
            }

            return {
              paymentStatus: 2,
              paymentSn: paymentRecord.payment_sn,
              isExpired: false
            };
          }
        } catch (error) {
          console.error('查询微信支付状态失败:', error);
        }
      }

      return {
        paymentStatus: paymentRecord.payment_status,
        paymentSn: paymentRecord.payment_sn,
        isExpired
      };
    } catch (error) {
      console.error('查询支付状态失败:', error);
      throw error;
    }
  }

  /**
   * 取消支付
   * @param {string} paymentSn - 支付流水号
   * @param {string} userId - 用户ID
   * @returns {Object} 取消结果
   */
  async cancelPayment(paymentSn, userId) {
    try {
      // 获取支付记录
      const paymentRecordResult = await PaymentRecordService.getPaymentRecord(paymentSn);
      if (!paymentRecordResult.success) {
        throw new Error(paymentRecordResult.message);
      }

      const paymentRecord = paymentRecordResult.data;

      // 验证用户权限
      if (paymentRecord.user_id.toString() !== userId.toString()) {
        throw new Error('无权限操作此支付记录');
      }

      // 检查支付状态
      if (paymentRecord.payment_status !== 0) {
        throw new Error('当前支付状态不允许取消');
      }

      // 更新支付状态为已取消
      await PaymentRecordService.updatePaymentStatus(paymentSn, {
        status: 3 // 3: 已取消
      });

      return {
        paymentSn,
        status: 'cancelled'
      };
    } catch (error) {
      console.error('取消支付失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单支付记录
   * @param {string} orderId - 订单ID
   * @param {string} userId - 用户ID
   * @returns {Array} 支付记录列表
   */
  async getOrderPaymentRecords(orderId, userId) {
    try {
      // 验证订单权限
      const order = await this.orderService.getOrderDetail(BigInt(orderId), userId);
      if (!order) {
        throw new Error('订单不存在或无权限访问');
      }

      // 获取支付记录
      const recordsResult = await PaymentRecordService.getOrderPaymentRecords(orderId);
      if (!recordsResult.success) {
        throw new Error(recordsResult.message);
      }

      const records = recordsResult.data;

      return records.map(record => ({
        id: record.id.toString(),
        paymentSn: record.payment_sn,
        orderId: record.order_id.toString(),
        paymentMethodId: record.payment_method_id,
        amount: record.amount.toString(),
        paymentStatus: record.payment_status,
        createdAt: record.created_at.toString(),
        updatedAt: record.updated_at.toString(),
        paidAt: record.paid_at ? record.paid_at.toString() : null
      }));
    } catch (error) {
      console.error('获取订单支付记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取支付统计信息
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Object} 统计信息
   */
  async getPaymentStatistics(userId, options = {}) {
    try {
      const { startTime, endTime } = options;

      // 构建查询条件
      const where = {
        user_id: BigInt(userId),
        deleted_at: null
      };

      if (startTime && endTime) {
        where.created_at = {
          gte: BigInt(startTime),
          lte: BigInt(endTime)
        };
      }

      // 查询统计数据
      const [totalCount, successCount, failedCount, totalAmount, successAmount] = await Promise.all([
        this.prisma.paymentRecords.count({ where }),
        this.prisma.paymentRecords.count({ where: { ...where, payment_status: 2 } }),
        this.prisma.paymentRecords.count({ where: { ...where, payment_status: 3 } }),
        this.prisma.paymentRecords.aggregate({
          where,
          _sum: { amount: true }
        }),
        this.prisma.paymentRecords.aggregate({
          where: { ...where, payment_status: 2 },
          _sum: { amount: true }
        })
      ]);

      return {
        totalCount,
        successCount,
        failedCount,
        totalAmount: totalAmount._sum.amount ? parseFloat(totalAmount._sum.amount.toString()) : 0,
        successAmount: successAmount._sum.amount ? parseFloat(successAmount._sum.amount.toString()) : 0
      };
    } catch (error) {
      console.error('获取支付统计失败:', error);
      throw error;
    }
  }
}

module.exports = OrderPaymentService;
