/**
 * 订单评价服务类
 */
class OrderReviewService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取订单可评价的商品列表
   * @param {BigInt} orderId - 订单ID
   * @param {BigInt} userId - 用户ID
   * @returns {Promise<Object>} - 可评价商品列表
   */
  async getReviewableItems(orderId, userId) {
    try {
      // 验证订单是否存在且属于当前用户
      const order = await this.prisma.orders.findFirst({
        where: {
          id: BigInt(orderId),
          user_id: BigInt(userId),
          order_status: 3, // 只有交易成功的订单才能评价
          deleted_at: null
        },
        include: {
          order_items: {
            include: {
              order_item_reviews: {
                where: {
                  deleted_at: null
                }
              }
            }
          }
        }
      });

      if (!order) {
        throw new Error('订单不存在或不可评价');
      }

      // 处理订单商品，标记评价状态
      const reviewableItems = order.order_items.map(item => ({
        id: item.id,
        orderId: order.id,
        goodsSpuId: item.goods_spu_id,
        goodsSkuId: item.goods_sku_id,
        productName: item.product_name,
        productImage: item.product_image,
        skuSpecifications: item.sku_specifications,
        unitPrice: item.unit_price,
        quantity: item.quantity,
        totalPrice: item.total_price,
        isEvaluated: item.order_item_reviews.length > 0,
        reviewId: item.order_item_reviews.length > 0 ? item.order_item_reviews[0].id : null,
        existingReview: item.order_item_reviews.length > 0 ? {
          qualityRating: item.order_item_reviews[0].quality_rating,
          serviceRating: item.order_item_reviews[0].service_rating,
          logisticsRating: item.order_item_reviews[0].logistics_rating,
          overallRating: item.order_item_reviews[0].overall_rating,
          reviewContent: item.order_item_reviews[0].review_content,
          isAnonymous: item.order_item_reviews[0].is_anonymous,
          createdAt: Number(item.order_item_reviews[0].created_at)
        } : null
      }));

      return {
        order: {
          id: order.id,
          orderSn: order.id.toString(), // 使用订单ID作为订单编号
          createdAt: Number(order.created_at),
          totalAmount: order.total_amount
        },
        items: reviewableItems
      };
    } catch (error) {
      console.error('获取可评价商品列表失败:', error);
      throw error;
    }
  }

  /**
   * 提交商品评价
   * @param {Object} reviewData - 评价数据
   * @returns {Promise<Object>} - 评价结果
   */
  async submitReview(reviewData) {
    const {
      orderId,
      orderItemId,
      userId,
      qualityRating = 5,
      serviceRating = 5,
      logisticsRating = 5,
      reviewContent = '',
      isAnonymous = false,
      images = []
    } = reviewData;

    try {
      return await this.prisma.$transaction(async (tx) => {
        // 验证订单商品是否存在且属于当前用户
        const orderItem = await tx.order_items.findFirst({
          where: {
            id: BigInt(orderItemId),
            order_id: BigInt(orderId),
            orders: {
              user_id: BigInt(userId),
              order_status: 3, // 交易成功
              deleted_at: null
            }
          },
          include: {
            orders: true,
            order_item_reviews: {
              where: {
                deleted_at: null
              }
            }
          }
        });

        if (!orderItem) {
          throw new Error('订单商品不存在或不可评价');
        }

        // 检查是否已经评价过
        if (orderItem.order_item_reviews.length > 0) {
          throw new Error('该商品已经评价过了');
        }

        // 过滤敏感词
        const filteredContent = await this.filterSensitiveWords(reviewContent);

        // 计算综合评分
        const overallRating = Math.round((qualityRating + serviceRating + logisticsRating) / 3);

        // 处理图片URL列表
        const imageUrls = images && images.length > 0
          ? images.map(image => image.fileUrl)
          : [];

        // 创建评价记录
        const review = await tx.order_item_reviews.create({
          data: {
            order_id: BigInt(orderId),
            order_item_id: BigInt(orderItemId),
            user_id: BigInt(userId),
            goods_spu_id: orderItem.goods_spu_id,
            goods_sku_id: orderItem.goods_sku_id,
            quality_rating: qualityRating,
            service_rating: serviceRating,
            logistics_rating: logisticsRating,
            overall_rating: overallRating,
            review_content: reviewContent,
            review_content_filtered: filteredContent,
            is_anonymous: isAnonymous,
            product_name_snapshot: orderItem.product_name,
            product_image_snapshot: orderItem.product_image,
            sku_specifications_snapshot: orderItem.sku_specifications,
            image_urls: imageUrls
          }
        });

        return {
          reviewId: review.id,
          message: '评价提交成功'
        };
      });
    } catch (error) {
      console.error('提交评价失败:', error);
      throw error;
    }
  }

  /**
   * 获取商品的评价列表（用于商品详情页）
   * @param {BigInt} goodsSpuId - 商品SPU ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 评价列表
   */
  async getProductReviews(goodsSpuId, options = {}) {
    const {
      page = 1,
      pageSize = 10,
      rating = null, // 评分筛选：1-5星
      hasImages = null, // 是否有图片
      sortBy = 'created_at', // 排序字段
      sortOrder = 'desc' // 排序方向
    } = options;

    try {
      const where = {
        goods_spu_id: BigInt(goodsSpuId),
        status: 1, // 正常显示
        audit_status: 1, // 已审核
        deleted_at: null
      };

      // 评分筛选
      if (rating) {
        where.overall_rating = parseInt(rating);
      }

      // 有图片筛选
      if (hasImages === true) {
        where.image_urls = {
          not: []
        };
      }

      // 查询总数
      const total = await this.prisma.order_item_reviews.count({ where });

      // 查询评价列表
      const reviews = await this.prisma.order_item_reviews.findMany({
        where,
        orderBy: {
          [sortBy]: sortOrder
        },
        skip: (page - 1) * pageSize,
        take: pageSize
      });

      // 格式化评价数据
      const formattedReviews = reviews.map(review => ({
        id: review.id,
        userId: review.is_anonymous ? null : review.user_id,
        userName: review.is_anonymous ? '匿名用户' : `用户${review.user_id.toString().slice(-4)}`,
        qualityRating: review.quality_rating,
        serviceRating: review.service_rating,
        logisticsRating: review.logistics_rating,
        overallRating: review.overall_rating,
        reviewContent: review.review_content_filtered || review.review_content,
        productName: review.product_name_snapshot,
        skuSpecifications: review.sku_specifications_snapshot,
        images: review.image_urls || [],
        isAnonymous: review.is_anonymous,
        helpfulCount: review.helpful_count,
        createdAt: Number(review.created_at),
        adminReply: review.admin_reply,
        adminReplyAt: review.admin_reply_at
      }));

      return {
        total,
        list: formattedReviews,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize)
      };
    } catch (error) {
      console.error('获取商品评价列表失败:', error);
      throw error;
    }
  }

  /**
   * 过滤敏感词
   * @param {String} content - 原始内容
   * @returns {Promise<String>} - 过滤后的内容
   */
  async filterSensitiveWords(content) {
    if (!content) return content;

    try {
      // 获取启用的敏感词列表
      const sensitiveWords = await this.prisma.sensitive_words.findMany({
        where: {
          status: 1,
          deleted_at: null
        }
      });

      let filteredContent = content;

      // 替换敏感词
      sensitiveWords.forEach(word => {
        const regex = new RegExp(word.word, 'gi');
        const replacement = word.replacement || '*'.repeat(word.word.length);
        filteredContent = filteredContent.replace(regex, replacement);
      });

      return filteredContent;
    } catch (error) {
      console.error('过滤敏感词失败:', error);
      return content; // 如果过滤失败，返回原内容
    }
  }

  /**
   * 获取商品评价统计
   * @param {BigInt} goodsSpuId - 商品SPU ID
   * @returns {Promise<Object>} - 评价统计
   */
  async getProductReviewStats(goodsSpuId) {
    try {
      const stats = await this.prisma.order_item_reviews.groupBy({
        by: ['overall_rating'],
        where: {
          goods_spu_id: BigInt(goodsSpuId),
          status: 1,
          audit_status: 1,
          deleted_at: null
        },
        _count: {
          overall_rating: true
        }
      });

      const total = stats.reduce((sum, item) => sum + item._count.overall_rating, 0);
      const ratingDistribution = {
        1: 0, 2: 0, 3: 0, 4: 0, 5: 0
      };

      stats.forEach(item => {
        ratingDistribution[item.overall_rating] = item._count.overall_rating;
      });

      // 计算平均评分
      const totalScore = stats.reduce((sum, item) => 
        sum + (item.overall_rating * item._count.overall_rating), 0
      );
      const averageRating = total > 0 ? (totalScore / total).toFixed(1) : 0;

      return {
        total,
        averageRating: parseFloat(averageRating),
        ratingDistribution,
        goodRatePercentage: total > 0 ? 
          Math.round(((ratingDistribution[4] + ratingDistribution[5]) / total) * 100) : 0
      };
    } catch (error) {
      console.error('获取商品评价统计失败:', error);
      throw error;
    }
  }
}

module.exports = OrderReviewService;
