const bcrypt = require('bcryptjs');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 商城前端用户服务
 */
class UserService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} - 创建的用户对象
   */
  async create(userData) {
    // 生成雪花ID
    const id = generateSnowflakeId();
    
    // 密码加密
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(userData.password, salt);

    // 准备用户数据
    const userDataToCreate = {
      id,
      username: userData.username,
      password: hashedPassword,
      nickname: userData.nickname,
      phone: userData.phone,
      avatar: userData.avatar,
      status: userData.status || 1,
      created_at: userData.created_at || Date.now(),
      updated_at: userData.updated_at || Date.now(),
      created_by: userData.created_by,
      updated_by: userData.updated_by,
      remark: userData.remark
    };

    // 只有当email不为空字符串时，才添加email字段
    if (userData.email && userData.email !== '') {
      userDataToCreate.email = userData.email;
    }

    console.log('创建用户数据:', userDataToCreate);

    // 创建用户
    return this.prisma.masterMallUser.create({
      data: userDataToCreate
    });
  }
  
  /**
   * 在事务中创建用户
   * @param {PrismaClient} prisma - Prisma 事务实例
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} - 创建的用户对象
   */
  async createWithTransaction(prisma, userData) {
    // 生成雪花ID
    const id = generateSnowflakeId();
    
    // 准备用户数据
    const userDataToCreate = {
      id,
      username: userData.username,
      password: userData.password, // 已经加密的密码
      nickname: userData.nickname || userData.username,
      phone: userData.phone,
      status: userData.status || 1,
      created_at: userData.created_at || Date.now(),
      updated_at: userData.updated_at || Date.now(),
      created_by: userData.created_by,
      updated_by: userData.updated_by,
      remark: userData.remark
    };

    // 只有当email不为空字符串时，才添加email字段
    if (userData.email && userData.email !== '') {
      userDataToCreate.email = userData.email;
    }
    
    // 只有当avatar不为空时，才添加avatar字段
    if (userData.avatar) {
      userDataToCreate.avatar = userData.avatar;
    }

    console.log('在事务中创建用户数据:', userDataToCreate);

    // 在事务中创建用户
    return prisma.masterMallUser.create({
      data: userDataToCreate
    });
  }

  /**
   * 根据用户名查找用户
   * @param {String} username - 用户名
   * @returns {Promise<Object|null>} - 用户对象或null
   */
  async findByUsername(username) {
    return this.prisma.masterMallUser.findFirst({
      where: {
        username,
        deleted_at: null
      }
    });
  }

  /**
   * 根据手机号查找用户
   * @param {String} phone - 手机号
   * @returns {Promise<Object|null>} - 用户对象或null
   */
  async findByPhone(phone) {
    console.log('根据手机号查找用户:', phone);
    
    try {
      // 使用原始SQL查询
      const results = await this.prisma.$queryRaw`
        SELECT * FROM "base"."mall_user" 
        WHERE "phone" = ${phone} 
        AND "deleted_at" IS NULL
      `;
      
      console.log('原始SQL查询结果:', results);
      
      if (results && results.length > 0) {
        const user = results[0];
        console.log('找到用户:', user.id);
        return user;
      }
      
      console.log('未找到用户');
      return null;
    } catch (error) {
      console.error('查询手机号用户失败:', error);
      return null;
    }
  }

  /**
   * 根据ID查找用户
   * @param {BigInt} id - 用户ID
   * @returns {Promise<Object|null>} - 用户对象或null
   */
  async findById(id) {
    return this.prisma.masterMallUser.findFirst({
      where: {
        id,
        deleted_at: null
      }
    });
  }

  /**
   * 验证用户登录
   * @param {String} username - 用户名或手机号
   * @param {String} password - 密码
   * @returns {Promise<Object|null>} - 验证成功返回用户对象，失败返回null
   */
  async verifyUser(username, password) {
    console.log('验证用户登录，输入:', { username });
    
    try {
      // 尝试直接查询数据库，同时匹配用户名或手机号
      const users = await this.prisma.$queryRaw`
        SELECT * FROM "base"."mall_user" 
        WHERE ("username" = ${username} OR "phone" = ${username})
        AND "deleted_at" IS NULL
      `;
      
      console.log('查询结果:', users);
      
      if (!users || users.length === 0) {
        console.log('未找到用户');
        return null;
      }
      
      const user = users[0];
      console.log('找到用户:', user.id);
      
      // 验证密码
      const isMatch = await bcrypt.compare(password, user.password);
      console.log('密码验证结果:', isMatch ? '密码正确' : '密码错误');
      
      if (!isMatch) {
        return null;
      }
      
      return user;
    } catch (error) {
      console.error('验证用户登录失败:', error);
      return null;
    }
  }

  /**
   * 更新用户登录信息
   * @param {BigInt} id - 用户ID
   * @param {Object} loginInfo - 登录信息
   * @returns {Promise<Object>} - 更新后的用户对象
   */
  async updateLoginInfo(id, loginInfo) {
    return this.prisma.masterMallUser.update({
      where: { id },
      data: loginInfo
    });
  }

  /**
   * 更新用户信息
   * @param {BigInt} id - 用户ID
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} - 更新后的用户对象
   */
  async update(id, userData) {
    // 如果包含密码字段，需要加密
    if (userData.password) {
      const salt = await bcrypt.genSalt(10);
      userData.password = await bcrypt.hash(userData.password, salt);
    }

    return this.prisma.masterMallUser.update({
      where: { id },
      data: userData
    });
  }
}

module.exports = UserService;
