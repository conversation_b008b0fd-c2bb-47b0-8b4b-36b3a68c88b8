/**
 * 用户消息模型定义
 */
module.exports = {
  schemas: {
    // 用户消息模型
    UserMessage: {
      type: "object",
      properties: {
        id: {
          type: "string",
          description: "消息ID",
          example: "1001"
        },
        userId: {
          type: "string",
          description: "用户ID",
          example: "1"
        },
        title: {
          type: "string",
          description: "消息标题",
          example: "欢迎使用商城"
        },
        content: {
          type: "string",
          description: "消息内容",
          example: "欢迎您注册成为我们的用户！"
        },
        messageType: {
          type: "string",
          description: "消息类型",
          enum: ["system", "order", "promotion", "service"],
          example: "system"
        },
        priority: {
          type: "integer",
          description: "优先级：1-普通，2-重要，3-紧急",
          enum: [1, 2, 3],
          example: 1
        },
        isRead: {
          type: "boolean",
          description: "是否已读",
          example: false
        },
        actionUrl: {
          type: "string",
          description: "操作链接",
          example: "/mall/user/center"
        },
        actionText: {
          type: "string",
          description: "操作按钮文本",
          example: "查看详情"
        },
        icon: {
          type: "string",
          description: "消息图标",
          example: "https://placehold.co/40x40/4f46e5/ffffff?text=系统"
        },
        createdAt: {
          type: "integer",
          description: "创建时间（时间戳）",
          example: 1619712000000
        },
        updatedAt: {
          type: "integer",
          description: "更新时间（时间戳）",
          example: 1619712000000
        },
        remark: {
          type: "string",
          description: "备注信息",
          example: "系统自动生成"
        }
      }
    },

    // 分页信息模型
    Pagination: {
      type: "object",
      properties: {
        page: {
          type: "integer",
          description: "当前页码",
          example: 1
        },
        pageSize: {
          type: "integer",
          description: "每页数量",
          example: 10
        },
        total: {
          type: "integer",
          description: "总记录数",
          example: 100
        },
        totalPages: {
          type: "integer",
          description: "总页数",
          example: 10
        }
      }
    },

    // 创建消息请求
    CreateMessageRequest: {
      type: "object",
      required: ["userId", "title"],
      properties: {
        userId: {
          type: "string",
          description: "用户ID",
          example: "1"
        },
        title: {
          type: "string",
          description: "消息标题",
          example: "系统通知"
        },
        content: {
          type: "string",
          description: "消息内容",
          example: "这是一条系统通知消息"
        },
        messageType: {
          type: "string",
          description: "消息类型",
          enum: ["system", "order", "promotion", "service"],
          default: "system",
          example: "system"
        },
        priority: {
          type: "integer",
          description: "优先级：1-普通，2-重要，3-紧急",
          enum: [1, 2, 3],
          default: 1,
          example: 1
        },
        actionUrl: {
          type: "string",
          description: "操作链接",
          example: "/mall/user/center"
        },
        actionText: {
          type: "string",
          description: "操作按钮文本",
          example: "查看详情"
        },
        icon: {
          type: "string",
          description: "消息图标",
          example: "https://placehold.co/40x40/4f46e5/ffffff?text=系统"
        }
      }
    },

    // 批量标记已读请求
    BatchMarkReadRequest: {
      type: "object",
      required: ["messageIds"],
      properties: {
        messageIds: {
          type: "array",
          items: {
            type: "string"
          },
          description: "消息ID列表",
          example: ["1001", "1002", "1003"]
        }
      }
    },

    // 消息列表响应
    MessageListResponse: {
      type: "object",
      properties: {
        code: {
          type: "integer",
          example: 200
        },
        message: {
          type: "string",
          example: "获取消息列表成功"
        },
        data: {
          type: "object",
          properties: {
            messages: {
              type: "array",
              items: {
                $ref: "#/components/schemas/UserMessage"
              }
            },
            pagination: {
              $ref: "#/components/schemas/Pagination"
            }
          }
        }
      }
    },

    // 未读消息数量响应
    UnreadCountResponse: {
      type: "object",
      properties: {
        code: {
          type: "integer",
          example: 200
        },
        message: {
          type: "string",
          example: "获取未读消息数量成功"
        },
        data: {
          type: "object",
          properties: {
            count: {
              type: "integer",
              description: "未读消息数量",
              example: 5
            }
          }
        }
      }
    },

    // 通用成功响应
    SuccessResponse: {
      type: "object",
      properties: {
        code: {
          type: "integer",
          example: 200
        },
        message: {
          type: "string",
          example: "操作成功"
        },
        data: {
          type: "object",
          nullable: true
        }
      }
    },

    // 错误响应
    ErrorResponse: {
      type: "object",
      properties: {
        code: {
          type: "integer",
          example: 400
        },
        message: {
          type: "string",
          example: "请求参数错误"
        },
        data: {
          type: "object",
          nullable: true
        }
      }
    }
  }
};
