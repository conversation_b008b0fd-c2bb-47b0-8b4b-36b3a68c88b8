{"openapi": "3.0.0", "info": {"title": "聚灵云平台商城API文档", "version": "1.0.0", "description": "聚灵云平台商城前端API接口文档"}, "servers": [{"url": "http://localhost:4000", "description": "开发服务器"}], "tags": [{"name": "商城/用户/认证", "description": "商城用户认证相关接口"}], "paths": {"/api/v1/mall/auth/login": {"post": {"tags": ["商城/用户/认证"], "summary": "用户登录", "description": "商城前端用户登录接口，支持用户名或手机号登录", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallUserLoginRequest"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallUserLoginResponse"}}}}, "400": {"description": "用户名或密码错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "账号已被禁用", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/mall/auth/register": {"post": {"tags": ["商城/用户/认证"], "summary": "用户注册", "description": "商城前端用户注册接口", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallUserRegisterRequest"}}}}, "responses": {"200": {"description": "注册成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallUserRegisterResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/mall/auth/captcha": {"get": {"tags": ["商城/用户/认证"], "summary": "获取验证码", "description": "获取短信验证码", "parameters": [{"in": "query", "name": "phone", "required": true, "schema": {"type": "string"}, "description": "手机号", "example": "13800138000"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallCaptchaResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"MallUser": {"type": "object", "properties": {"id": {"type": "string", "description": "用户ID", "example": "1001"}, "username": {"type": "string", "description": "用户名", "example": "user123"}, "nickname": {"type": "string", "description": "昵称", "example": "用户昵称"}, "phone": {"type": "string", "description": "手机号", "example": "13800138000"}, "email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}, "avatar": {"type": "string", "description": "头像", "example": "https://example.com/avatar.jpg"}, "status": {"type": "integer", "description": "用户状态：1-正常，0-禁用", "enum": [0, 1], "example": 1}, "last_login_time": {"type": "integer", "description": "最后登录时间（时间戳）", "example": 1619712000000}, "last_login_ip": {"type": "string", "description": "最后登录IP", "example": "***********"}, "login_count": {"type": "integer", "description": "登录次数", "example": 10}, "created_at": {"type": "integer", "description": "创建时间（时间戳）", "example": 1619712000000}, "updated_at": {"type": "integer", "description": "更新时间（时间戳）", "example": 1619712000000}}}, "MallUserLoginRequest": {"type": "object", "required": ["username", "password"], "properties": {"username": {"type": "string", "description": "用户名或手机号", "example": "user123"}, "password": {"type": "string", "description": "密码", "example": "password123"}, "encrypted": {"type": "boolean", "description": "密码是否已经过RSA加密", "default": false, "example": false}}}, "MallUserLoginResponse": {"type": "object", "properties": {"success": {"type": "boolean", "default": true}, "message": {"type": "string", "example": "登录成功"}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/MallUser"}, "token": {"type": "string", "description": "登录令牌", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}}}}, "MallUserRegisterRequest": {"type": "object", "required": ["username", "password", "phone", "<PERSON><PERSON>a"], "properties": {"username": {"type": "string", "description": "用户名", "example": "user123"}, "password": {"type": "string", "description": "密码", "example": "password123"}, "nickname": {"type": "string", "description": "昵称", "example": "用户昵称"}, "phone": {"type": "string", "description": "手机号", "example": "13800138000"}, "email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}, "captcha": {"type": "string", "description": "验证码", "example": "123456"}, "encrypted": {"type": "boolean", "description": "密码是否已经过RSA加密", "default": false, "example": false}}}, "MallUserRegisterResponse": {"type": "object", "properties": {"success": {"type": "boolean", "default": true}, "message": {"type": "string", "example": "注册成功"}, "data": {"$ref": "#/components/schemas/MallUser"}}}, "MallCaptchaResponse": {"type": "object", "properties": {"success": {"type": "boolean", "default": true}, "message": {"type": "string", "example": "获取验证码成功"}, "data": {"type": "object", "properties": {"captcha": {"type": "string", "description": "验证码（仅在非生产环境下返回）", "example": "123456"}}}}}, "Error": {"type": "object", "properties": {"success": {"type": "boolean", "default": false}, "message": {"type": "string", "description": "错误信息", "example": "用户名/手机号或密码错误"}, "code": {"type": "integer", "description": "错误码", "example": 40001, "enum": [40001, 40002, 40003, 40004, 40101, 40102, 40301, 40302, 40401, 40402, 50001, 50002, 50003]}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证，在请求头中添加 'Authorization: Bearer {token}'"}}}}