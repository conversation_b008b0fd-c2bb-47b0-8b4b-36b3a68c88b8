const { z } = require('zod');

/**
 * 商品品牌 DTO
 */
class GoodsBrandDto {
  /**
   * 创建商品品牌的验证规则
   */
  static createSchema = z.object({
    name: z.string({
      required_error: '品牌名称不能为空'
    }).min(1, '品牌名称不能为空'),
    logo_url: z.string().url('Logo URL 格式不正确').optional().nullable(),
    description: z.string().optional().nullable()
  });

  /**
   * 更新商品品牌的验证规则
   */
  static updateSchema = z.object({
    name: z.string({
      required_error: '品牌名称不能为空'
    }).min(1, '品牌名称不能为空'),
    logo_url: z.string().url('Logo URL 格式不正确').optional().nullable(),
    description: z.string().optional().nullable()
  });

  /**
   * 验证创建商品品牌的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证后的数据
   */
  static validateCreate(data) {
    return this.createSchema.parse(data);
  }

  /**
   * 验证更新商品品牌的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证后的数据
   */
  static validateUpdate(data) {
    return this.updateSchema.parse(data);
  }
}

module.exports = GoodsBrandDto;
