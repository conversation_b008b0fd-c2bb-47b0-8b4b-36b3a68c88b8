const { z } = require('zod');

/**
 * 商品草稿DTO - 用于创建草稿
 */
const GoodsDraftDto = z.object({
  content: z.string({
    required_error: '草稿内容不能为空',
    invalid_type_error: '草稿内容必须是字符串'
  }).min(1, '草稿内容不能为空')
});

/**
 * 商品草稿更新DTO - 用于更新草稿
 */
const GoodsDraftUpdateDto = z.object({
  content: z.string({
    required_error: '草稿内容不能为空',
    invalid_type_error: '草稿内容必须是字符串'
  }).min(1, '草稿内容不能为空')
});

module.exports = {
  GoodsDraftDto,
  GoodsDraftUpdateDto
};
