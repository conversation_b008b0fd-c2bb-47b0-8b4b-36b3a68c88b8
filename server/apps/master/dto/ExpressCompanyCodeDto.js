/**
 * 快递公司编码DTO
 */
class ExpressCompanyCodeDto {
  /**
   * 验证查询参数
   * @param {Object} params - 查询参数
   * @returns {Object} - 验证结果
   */
  static validateQueryParams(params) {
    const errors = [];
    
    // 验证ID格式（如果提供）
    if (params.id && isNaN(Number(params.id))) {
      errors.push('ID必须是数字');
    }
    
    // 验证时间范围（如果提供）
    if (params.startTime && isNaN(Number(params.startTime))) {
      errors.push('开始时间必须是时间戳');
    }
    
    if (params.endTime && isNaN(Number(params.endTime))) {
      errors.push('结束时间必须是时间戳');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = ExpressCompanyCodeDto;
