/**
 * 运费区域关联数据传输对象
 */
class GoodsFreightRegionRelationDto {
  /**
   * 创建运费区域关联DTO
   * @param {Object} data - 运费区域关联数据
   */
  constructor(data = {}) {
    this.id = data.id || null;
    this.freight_config_id = data.freight_config_id || null;
    this.region_code = data.region_code || '';
    this.region_name = data.region_name || '';
    this.parent_name = data.parent_name || '';
    this.created_at = data.created_at || Date.now();
    this.updated_at = data.updated_at || Date.now();
    this.deleted_at = data.deleted_at || null;
  }

  /**
   * 验证DTO数据
   * @returns {Object} 包含验证结果和错误信息
   */
  validate() {
    const errors = [];

    // 验证关联的配置ID
    if (!this.freight_config_id) {
      errors.push('运费配置ID不能为空');
    }

    // 验证区域代码
    if (!this.region_code) {
      errors.push('区域代码不能为空');
    }

    // 验证区域名称
    if (!this.region_name) {
      errors.push('区域名称不能为空');
    }

    // 验证父级区域名称
    if (!this.parent_name) {
      errors.push('父级区域名称不能为空');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 转换为数据库对象
   * @returns {Object} 数据库对象
   */
  toDbObject() {
    return {
      freight_config_id: this.freight_config_id,
      region_code: this.region_code,
      region_name: this.region_name,
      parent_name: this.parent_name,
      created_at: this.created_at,
      updated_at: this.updated_at,
      deleted_at: this.deleted_at
    };
  }

  /**
   * 从数据库对象创建DTO
   * @param {Object} dbObject - 数据库对象
   * @returns {GoodsFreightRegionRelationDto} DTO实例
   */
  static fromDbObject(dbObject) {
    return new GoodsFreightRegionRelationDto(dbObject);
  }
}

module.exports = GoodsFreightRegionRelationDto;
