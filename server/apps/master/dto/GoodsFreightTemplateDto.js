const ChargeTypeEnum = require('../constants/ChargeTypeEnum');

/**
 * 运费模板数据传输对象
 */
class GoodsFreightTemplateDto {
  /**
   * 创建运费模板DTO
   * @param {Object} data - 运费模板数据
   */
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || '';
    this.charge_type = data.charge_type || ChargeTypeEnum.CHARGE_BY_PIECE; // 默认按件数
    this.created_at = data.created_at || Date.now(); // 使用毫秒级时间戳
    this.updated_at = data.updated_at || Date.now(); // 使用毫秒级时间戳
    this.deleted_at = data.deleted_at || null;
    this.created_by = data.created_by || null;
    this.updated_by = data.updated_by || null;
    this.freight_configs = data.freight_configs || []; // 运费配置列表
  }

  /**
   * 获取驼峰格式的计费类型
   */
  get chargeType() {
    return this.charge_type;
  }

  /**
   * 设置驼峰格式的计费类型
   */
  set chargeType(value) {
    this.charge_type = value;
  }

  /**
   * 获取驼峰格式的运费配置
   */
  get freightConfigs() {
    return this.freight_configs;
  }

  /**
   * 设置驼峰格式的运费配置
   */
  set freightConfigs(value) {
    this.freight_configs = value;
  }

  /**
   * 验证DTO数据
   * @returns {Object} 包含验证结果和错误信息
   */
  validate() {
    const errors = [];

    // 验证名称
    if (!this.name) {
      errors.push('运费模板名称不能为空');
    } else if (this.name.length > 100) {
      errors.push('运费模板名称不能超过100个字符');
    }

    // 验证计价方式
    if (!ChargeTypeEnum.isValidChargeType(this.charge_type)) {
      errors.push(`计价方式必须是以下之一：${ChargeTypeEnum.getAllOptions().map(opt => `${opt.value}-${opt.label}`).join('，')}`);
    }

    // 验证运费配置
    if (!this.freight_configs || this.freight_configs.length === 0) {
      errors.push('至少需要一个运费配置');
    } else {
      // 检查是否有全国默认配置
      const hasDefaultConfig = this.freight_configs.some(config => config.is_default === 1);
      
      // 验证每个运费配置
      this.freight_configs.forEach((config, index) => {
        // 首件/首重验证
        if (config.first_item <= 0) {
          errors.push(`第${index + 1}个运费配置的首件/首重/首体积数量必须大于0`);
        }
        
        // 首费验证
        if (config.first_fee < 0) {
          errors.push(`第${index + 1}个运费配置的首件/首重/首体积费用不能为负数`);
        }
        
        // 续件/续重验证
        if (config.additional_item <= 0) {
          errors.push(`第${index + 1}个运费配置的续件/续重/续体积数量必须大于0`);
        }
        
        // 续费验证
        if (config.additional_fee < 0) {
          errors.push(`第${index + 1}个运费配置的续件/续重/续体积费用不能为负数`);
        }
        
        // 非默认配置必须有区域关联
        if (config.is_default !== 1 && (!config.region_relations || config.region_relations.length === 0)) {
          errors.push(`第${index + 1}个非默认运费配置必须至少关联一个区域`);
        }
      });
      
      // 提示应有全国默认配置
      if (!hasDefaultConfig) {
        errors.push('运费模板应至少包含一个全国默认配置');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 转换为数据库对象
   * @returns {Object} 数据库对象
   */
  toDbObject() {
    return {
      name: this.name,
      charge_type: this.charge_type,
      created_at: this.created_at,
      updated_at: this.updated_at,
      deleted_at: this.deleted_at,
      created_by: this.created_by,
      updated_by: this.updated_by
    };
  }

  /**
   * 从数据库对象创建DTO
   * @param {Object} dbObject - 数据库对象
   * @returns {GoodsFreightTemplateDto} DTO实例
   */
  static fromDbObject(dbObject) {
    return new GoodsFreightTemplateDto(dbObject);
  }
}

module.exports = GoodsFreightTemplateDto;
