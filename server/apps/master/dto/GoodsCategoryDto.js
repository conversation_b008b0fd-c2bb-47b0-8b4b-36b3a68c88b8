/**
 * 商品分类请求DTO
 */
class GoodsCategoryRequest {
  constructor(data) {
    // 将前端传入的字段转换为与数据库字段名称一致的字段
    // 只使用驼峰命名法
    this.goods_parent_category_id = data.goodsParentCategoryId !== undefined ? (Number(data.goodsParentCategoryId) || null) : null;
    this.name = data.name;
    this.sort_order = data.sortOrder !== undefined ? Number(data.sortOrder) : 100;
    this.is_enabled = data.isEnabled !== undefined ? (data.isEnabled ? 1 : 0) : 1;
    this.description = data.description || '';
    this.meta_title = data.metaTitle || '';
    this.meta_keywords = data.metaKeywords || '';
    this.meta_description = data.metaDescription || '';
    // 只接受单个图片链接
    this.image_url = typeof data.imageUrl === 'string' ? data.imageUrl : null;
    
    // 新增字段
    this.level = data.level || 1;
    
    // 关联属性模板
    this.attributeSetIds = data.attributeSetIds || [];
    // 如果传入的是字符串，尝试解析为数组
    if (typeof this.attributeSetIds === 'string') {
      try {
        this.attributeSetIds = JSON.parse(this.attributeSetIds);
      } catch (e) {
        this.attributeSetIds = [];
      }
    }
    // 确保是数字数组
    this.attributeSetIds = Array.isArray(this.attributeSetIds) ? 
      this.attributeSetIds.map(id => Number(id)).filter(id => !isNaN(id)) : [];
  }
  
  /**
   * 验证请求数据
   * @throws {Error} 验证失败时抛出错误
   * @returns {boolean} 验证成功返回true
   */
  validate() {
    if (!this.name) {
      throw new Error('分类名称不能为空');
    }
    
    if (this.goods_parent_category_id !== null && typeof this.goods_parent_category_id !== 'number') {
      throw new Error('父级ID必须是数字');
    }
    
    if (typeof this.sort_order !== 'number' || this.sort_order < 0) {
      throw new Error('排序值必须是非负数字');
    }
    
    if (![0, 1].includes(this.is_enabled)) {
      throw new Error('启用状态必须是0或1，0表示禁用，1表示启用');
    }
    
    return true;
  }
}

module.exports = {
  GoodsCategoryRequest
};
