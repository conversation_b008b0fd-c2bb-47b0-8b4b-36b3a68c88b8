/**
 * 运费配置数据传输对象
 */
class GoodsFreightConfigDto {
  /**
   * 创建运费配置DTO
   * @param {Object} data - 运费配置数据
   */
  constructor(data = {}) {
    this.id = data.id || null;
    this.freight_template_id = data.freight_template_id || null;
    this.first_item = data.first_item || 1;
    this.first_fee = typeof data.first_fee === 'number' ? data.first_fee : (data.first_fee || 0);
    this.additional_item = data.additional_item || 1;
    this.additional_fee = typeof data.additional_fee === 'number' ? data.additional_fee : (data.additional_fee || 0);
    this.is_default = data.is_default === 1 || data.is_default === true ? 1 : 0;
    this.created_at = data.created_at || Date.now();
    this.updated_at = data.updated_at || Date.now();
    this.deleted_at = data.deleted_at || null;
    this.created_by = data.created_by || null;
    this.updated_by = data.updated_by || null;
    this.region_relations = data.region_relations || []; // 区域关联
  }

  /**
   * 验证DTO数据
   * @returns {Object} 包含验证结果和错误信息
   */
  validate() {
    const errors = [];

    // 验证运费模板ID
    if (!this.freight_template_id) {
      errors.push('运费模板ID不能为空');
    }

    // 验证首件/首重/首体积
    if (this.first_item <= 0) {
      errors.push('首件/首重/首体积数量必须大于0');
    }
    
    // 验证首费
    if (this.first_fee < 0) {
      errors.push('首件/首重/首体积费用不能为负数');
    }
    
    // 验证续件/续重/续体积
    if (this.additional_item <= 0) {
      errors.push('续件/续重/续体积数量必须大于0');
    }
    
    // 验证续费
    if (this.additional_fee < 0) {
      errors.push('续件/续重/续体积费用不能为负数');
    }

    // 验证区域关系（非默认配置必须有区域关联）
    if (this.is_default !== 1 && (!this.region_relations || this.region_relations.length === 0)) {
      errors.push('非默认配置必须至少关联一个区域');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 转换为数据库对象
   * @returns {Object} 数据库对象
   */
  toDbObject() {
    return {
      freight_template_id: this.freight_template_id,
      first_item: this.first_item,
      first_fee: this.first_fee,
      additional_item: this.additional_item,
      additional_fee: this.additional_fee,
      is_default: this.is_default,
      created_at: this.created_at,
      updated_at: this.updated_at,
      deleted_at: this.deleted_at,
      created_by: this.created_by,
      updated_by: this.updated_by
    };
  }

  /**
   * 从数据库对象创建DTO
   * @param {Object} dbObject - 数据库对象
   * @returns {GoodsFreightConfigDto} DTO实例
   */
  static fromDbObject(dbObject) {
    return new GoodsFreightConfigDto(dbObject);
  }
}

module.exports = GoodsFreightConfigDto;
