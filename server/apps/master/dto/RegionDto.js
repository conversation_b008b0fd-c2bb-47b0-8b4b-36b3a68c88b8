/**
 * 区域数据传输对象
 * 用于规范区域相关API的请求和响应数据格式
 */
class RegionDto {
  /**
   * 格式化区域树结构为前端所需的简化格式
   * @param {Array} regionTree - 区域树数据
   * @returns {Array} - 格式化后的区域树数据，只包含code和name字段
   */
  static formatRegionTreeSimple(regionTree) {
    if (!regionTree || !Array.isArray(regionTree)) return [];
    
    return regionTree.map(region => {
      return {
        code: region.citycode ? region.citycode.toString() : '',
        name: region.cityname || '',
        children: region.children ? this.formatRegionTreeSimple(region.children) : []
      };
    });
  }
}

module.exports = RegionDto;
