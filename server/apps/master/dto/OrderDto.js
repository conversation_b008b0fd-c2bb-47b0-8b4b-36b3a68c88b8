/**
 * 订单数据传输对象
 * 负责订单相关数据的验证
 * 
 * 第三方创建订单接口必填项说明（/api/master/orders/third-party）：
 * ----------------------------------------
 * 1. 基础信息必填字段：
 * - customerName: 客户姓名（必填）
 * - customerPhone: 客户手机号（必填，格式为1开头的11位数字）
 * - channelId: 渠道ID（必填，雪花ID格式）
 * - items: 订单商品项列表（必填，至少包含一项）
 * 
 * 2. 商品项必填字段（每个商品项）：
 * - productName: 商品名称（必填）
 * - unitPrice: 商品单价（必填）
 * - quantity: 购买数量（必填，必须大于0）
 * 
 * 3. 可选字段：
 * - customerId: 客户ID
 * - remark: 订单备注
 * - goodsSpuId: 系统内商品SPU ID
 * - goodsSkuId: 系统内商品SKU ID
 * - productImage: 商品图片URL
 * - thirdPartySpuId: 第三方系统中的SPU标识（可选）
 * - thirdPartySkuId: 第三方系统中的SKU标识（可选）
 * - thirdPartyProductCode: 第三方系统中的商品编码
 *
 * 手动录单接口必填项说明（/api/master/orders/manual）：
 * ----------------------------------------
 * 1. 基础信息必填字段：
 * - customerName: 客户姓名（必填）
 * - customerPhone: 客户手机号（必填）
 * - channelId: 渠道ID（必填）
 * - items: 订单商品项列表（必填，至少包含一项）
 * - shippingInfo: 收货信息（必填）
 * 
 * 2. 收货信息必填字段：
 * - recipientName: 收货人姓名（必填）
 * - recipientPhone: 收货人电话（必填）
 * - regionProvinceId: 省份ID（必填）
 * - regionCityId: 城市ID（必填）
 * - regionPathName: 区域路径名称（必填）
 * - streetAddress: 街道地址（必填）
 */
const Joi = require('joi');
const OrderSourceEnum = require('../constants/OrderSourceEnum');
const OrderTypeEnum = require('../constants/OrderTypeEnum');
const PaymentMethodEnum = require('../constants/PaymentMethodEnum');
const InvoiceTypeEnum = require('../constants/InvoiceTypeEnum');

/**
 * 订单DTO
 */
class OrderDto {
  /**
   * 验证手动录单数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateManualCreate(data) {
    const schema = Joi.object({
      // 客户信息
      customerId: Joi.string().allow(null, ''),
      customerName: Joi.string().required().max(50).messages({
        'string.empty': '客户姓名不能为空',
        'string.max': '客户姓名不能超过50个字符',
        'any.required': '客户姓名是必填项'
      }),
      customerPhone: Joi.string().required().messages({
        'string.empty': '客户手机号不能为空',
        'any.required': '客户手机号是必填项'
      }),

      // 订单基本信息
      orderStatus: Joi.string().valid('pending', 'processing', 'shipped', 'delivered', 'completed', 'returned', 'cancelled').default('pending'),
      orderType: Joi.number().integer().valid(
          OrderTypeEnum.MALL,    // 1-商城订单
          OrderTypeEnum.SYSTEM   // 2-系统订单
      ).default(OrderTypeEnum.MALL),
      orderSource: Joi.number().integer().valid(
          OrderSourceEnum.SYSTEM,  // 0-系统创建
          OrderSourceEnum.ADMIN,   // 1-后台创建
          OrderSourceEnum.MALL,     // 2-商城下单
          OrderSourceEnum.MANUAL,  // 3-手动录单
        ).default(OrderSourceEnum.MANUAL),  // 默认为手动录单

      // 第三方订单信息
      thirdPartyOrderSn: Joi.string().allow(null, '').description('第三方订单号'),

      // 备注
      remark: Joi.string().allow(null, '').max(500),

      // 价格信息
      discountAmount: Joi.number().min(0).allow(null).default(0).messages({
        'number.base': '折扣金额必须是数字',
        'number.min': '折扣金额不能小于0'
      }),
      totalAmount: Joi.number().min(0).allow(null).messages({
        'number.base': '订单总金额必须是数字',
        'number.min': '订单总金额不能小于0'
      }),

      // 渠道信息
      channelId: Joi.string().required().messages({
        'string.empty': '渠道ID不能为空',
        'any.required': '渠道ID是必填项'
      }),
      
      // 跟单员信息
      followerId: Joi.string().allow(null, ''),
      
      // 支付方式信息
      paymentMethod: Joi.string().allow(null, ''), // 支付方式名称
      paymentMethodsId: Joi.string().allow(null, ''), // 支付方式ID，关联到payment_methods表
      paymentStatus: Joi.string().valid('unpaid', 'paid', 'partial_paid', 'refunded', 'partial_refunded').default('unpaid'),
      paidAmount: Joi.number().min(0).default(0),
      transactionId: Joi.string().allow(null, ''),
      paymentTime: Joi.number().allow(null),

      // 订单项 - 主要接收 skuId、price 和 quantity，允许前端传递其他字段但不使用
      items: Joi.array().items(
          Joi.object({
            // 商品ID - 必填
            skuId: Joi.string().required().messages({
              'string.empty': 'SKU ID不能为空',
              'any.required': 'SKU ID是必填项'
            }),
            // 单价 - 必填
            price: Joi.number().required().messages({
              'number.base': '商品单价必须是数字',
              'any.required': '商品单价是必填项'
            }),
            // 数量 - 必填
            quantity: Joi.number().required().integer().min(1).messages({
              'number.base': '购买数量必须是数字',
              'number.integer': '购买数量必须是整数',
              'number.min': '购买数量不能小于1',
              'any.required': '购买数量是必填项'
            }),
            // 允许前端传递但不使用的字段
            productName: Joi.any().strip(), // 允许传递但会被删除
            productImage: Joi.any().strip(),
            skuSpecifications: Joi.any().strip(),
            unitPrice: Joi.any().strip() // 兼容旧版前端传递 unitPrice
          }).unknown(true) // 允许其他字段但会被删除
      ).min(1).required().messages({
        'array.min': '订单项不能为空',
        'any.required': '订单项是必填项'
      }),

      // 配送信息 - 特别注意：这里使用shippingInfo而不是shipping
      shippingInfo: Joi.object({
        // 不再需要收货人姓名和电话，将使用客户信息
        // 收货人姓名和电话将在服务层自动从客户信息中获取
        regionProvinceId: Joi.number().integer().required().messages({
          'number.base': '省份ID必须是数字',
          'number.integer': '省份ID必须是整数',
          'any.required': '省份ID是必填项'
        }),
        regionCityId: Joi.number().integer().required().messages({
          'number.base': '城市ID必须是数字',
          'number.integer': '城市ID必须是整数',
          'any.required': '城市ID是必填项'
        }),
        regionDistrictId: Joi.number().integer().allow(null).messages({
          'number.base': '区/县ID必须是数字',
          'number.integer': '区/县ID必须是整数'
        }),
        regionPathName: Joi.string().allow(null, ''),
        streetAddress: Joi.string().required().messages({
          'string.empty': '详细地址不能为空',
          'any.required': '详细地址是必填项'
        }),
        postalCode: Joi.string().allow(null, ''),
        shippingCompanyCode: Joi.string().allow(null, ''),
        shippingCompanyName: Joi.string().allow(null, ''),
        trackingNumber: Joi.string().allow(null, '')
      }).required().messages({
        'any.required': '收货信息是必填项'
      }),
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证第三方创建订单的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateThirdPartyCreate(data) {
    const schema = Joi.object({
      // 客户信息
      customerId: Joi.string().allow(null, ''),
      customerName: Joi.string().required().max(50).messages({
        'string.empty': '客户姓名不能为空',
        'string.max': '客户姓名不能超过50个字符',
        'any.required': '客户姓名是必填项'
      }),
      customerPhone: Joi.string().required().messages({
        'string.empty': '客户手机号不能为空',
        'any.required': '客户手机号是必填项'
      }),

      // 订单基本信息
      orderStatus: Joi.string().valid('pending', 'processing', 'shipped', 'delivered', 'completed', 'returned', 'cancelled').default('pending'),
      orderType: Joi.number().integer().valid(
          OrderTypeEnum.MALL,    // 1-商城订单
          OrderTypeEnum.SYSTEM   // 2-系统订单
      ).default(OrderTypeEnum.MALL),
      orderSource: Joi.number().integer().valid(
          OrderSourceEnum.SYSTEM,  // 0-系统创建
          OrderSourceEnum.ADMIN,   // 1-后台创建
          OrderSourceEnum.MALL,    // 2-商城下单
          OrderSourceEnum.MANUAL,  // 3-手动录单
        ).default(OrderSourceEnum.SYSTEM),

      // 金额信息
      discountAmount: Joi.number().min(0).default(0),
      couponAmount: Joi.number().min(0).default(0),
      shippingFee: Joi.number().min(0).default(0),
      paidAmount: Joi.number().min(0).default(0),

      // 支付信息
      paymentStatus: Joi.string().valid('unpaid', 'partial_paid', 'paid', 'refunding', 'refunded').default('unpaid'),
      paymentMethod: Joi.string().allow(null, ''),
      paymentMethodId: Joi.number().integer().valid(
          PaymentMethodEnum.WECHAT,    // 1-微信支付
          PaymentMethodEnum.ALIPAY,    // 2-支付宝
          PaymentMethodEnum.UNIONPAY,  // 3-银联
          PaymentMethodEnum.COD,       // 4-货到付款
          PaymentMethodEnum.OTHER      // 5-其他
      ).allow(null).default(null),
      transactionId: Joi.string().allow(null, ''),
      paymentTime: Joi.number().allow(null),

      // 配送状态
      shippingStatus: Joi.string().valid('unshipped', 'shipped', 'delivered', 'returned').default('unshipped'),

      // 备注
      remark: Joi.string().allow(null, '').max(500),

      // 第三方平台信息
      thirdPartyOrderSn: Joi.string().required().messages({
        'string.empty': '第三方订单号不能为空',
        'any.required': '第三方订单号是必填项'
      }),

      // 渠道信息
      //channelId: Joi.string().required().messages({
      //  'string.empty': '渠道ID不能为空',
      //  'any.required': '渠道ID是必填项'
      //}),

      // 平台和店铺信息（可选）
      //platformId: Joi.string().allow(null, '').messages({
      //  'string.base': '平台ID必须是字符串'
      //}),
      storeId: Joi.string().allow(null, '').messages({
        'string.base': '店铺ID必须是字符串'
      }),

      // 跟单员信息
      followerId: Joi.string().allow(null, ''),

      // 订单项 - 第三方订单中不需要goodsSpuId和goodsSkuId
      items: Joi.array().items(
          Joi.object({
            goodsSpuId: Joi.string().allow(null, ''),
            goodsSkuId: Joi.string().allow(null, ''),
            spuCodeSnapshot: Joi.string().allow(null, ''),
            spuNameSnapshot: Joi.string().allow(null, ''),
            productName: Joi.string().required(),
            skuCode: Joi.string().allow(null, ''),
            skuSpecifications: Joi.string().allow(null, ''),
            productImage: Joi.string().allow(null, ''),
            unitPrice: Joi.number().required().min(0),
            marketPriceSnapshot: Joi.number().allow(null),
            costPriceSnapshot: Joi.number().allow(null),
            weightSnapshot: Joi.number().allow(null),
            volumeSnapshot: Joi.number().allow(null),
            thirdPartySpuId: Joi.string().allow(null, ''),
            thirdPartySkuId: Joi.string().allow(null, ''),
            thirdPartyProductCode: Joi.string().allow(null, ''),
            thirdPartyItemSnapshot: Joi.alternatives().try(
                Joi.string().allow(null, ''),
                Joi.object().allow(null)
            ),
            quantity: Joi.number().required().integer().min(1),
            itemPaidAmount: Joi.number().min(0).default(0)
          })
      ).min(1).required().messages({
        'array.min': '订单项不能为空',
        'any.required': '订单项是必填项'
      }),

      // 配送信息（可选，收货人信息直接使用客户信息）
      address: Joi.string().allow(null, '').description('收货地址，可以是完整地址如"北京市东城区王府井大街1号"'),
      postalCode: Joi.string().allow(null, '').description('邮政编码'),

      // 销售人员信息
      salespeople: Joi.object({
        salespersonId: Joi.string().allow(null, ''),
        salespersonName: Joi.string().allow(null, ''),
        salespersonPhone: Joi.string().allow(null, ''),
        commissionRate: Joi.number().min(0).max(100).allow(null),
        commissionAmount: Joi.number().min(0).allow(null)
      }).allow(null)
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证创建订单的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateCreate(data) {
    const schema = Joi.object({
      // 客户信息
      customerId: Joi.string().allow(null, ''),
      customerName: Joi.string().required().max(50).messages({
        'string.empty': '客户姓名不能为空',
        'string.max': '客户姓名不能超过50个字符',
        'any.required': '客户姓名是必填项'
      }),
      customerPhone: Joi.string().required().messages({
        'string.empty': '客户手机号不能为空',
        'any.required': '客户手机号是必填项'
      }),

      // 订单基本信息
      orderStatus: Joi.string().valid('pending', 'processing', 'shipped', 'delivered', 'completed', 'returned', 'cancelled').default('pending'),
      orderType: Joi.number().integer().valid(
          OrderTypeEnum.MALL,    // 1-商城订单
          OrderTypeEnum.SYSTEM   // 2-系统订单
      ).default(OrderTypeEnum.MALL),
      orderSource: Joi.number().integer().valid(
          OrderSourceEnum.SYSTEM,  // 0-系统创建
          OrderSourceEnum.ADMIN,   // 1-后台创建
          OrderSourceEnum.MALL     // 2-商城下单
      ).default(OrderSourceEnum.SYSTEM),

      // 金额信息
      discountAmount: Joi.number().min(0).default(0),
      couponAmount: Joi.number().min(0).default(0),
      shippingFee: Joi.number().min(0).default(0),
      paidAmount: Joi.number().min(0).default(0),

      // 支付信息
      paymentStatus: Joi.string().valid('unpaid', 'partial_paid', 'paid', 'refunding', 'refunded').default('unpaid'),
      paymentMethod: Joi.string().allow(null, ''),
      paymentMethodId: Joi.number().integer().valid(
          PaymentMethodEnum.WECHAT,    // 1-微信支付
          PaymentMethodEnum.ALIPAY,    // 2-支付宝
          PaymentMethodEnum.UNIONPAY,  // 3-银联
          PaymentMethodEnum.COD,       // 4-货到付款
          PaymentMethodEnum.OTHER      // 5-其他
      ).allow(null).default(null).messages({
        'number.base': '支付方式ID必须是数字',
        'number.integer': '支付方式ID必须是整数',
        'any.only': '无效的支付方式ID'
      }),
      transactionId: Joi.string().allow(null, ''),
      paymentTime: Joi.number().allow(null),

      // 配送状态
      shippingStatus: Joi.string().valid('unshipped', 'shipped', 'delivered', 'returned').default('unshipped'),

      // 备注
      remark: Joi.string().allow(null, '').max(500),

      // 第三方平台信息
      thirdPartyOrderSn: Joi.string().allow(null, ''),

      // 渠道信息
      channelId: Joi.string().required().messages({
        'string.empty': '渠道ID不能为空',
        'any.required': '渠道ID是必填项'
      }),
      
      // 跟单员信息
      followerId: Joi.string().allow(null, ''),

      // 订单项
      items: Joi.array().items(
          Joi.object({
            goodsSpuId: Joi.string().required().messages({
              'string.empty': '商品SPU ID不能为空',
              'any.required': '商品SPU ID是必填项'
            }),
            goodsSkuId: Joi.string().required().messages({
              'string.empty': '商品SKU ID不能为空',
              'any.required': '商品SKU ID是必填项'
            }),
            spuCodeSnapshot: Joi.string().allow(null, ''),
            spuNameSnapshot: Joi.string().allow(null, ''),
            productName: Joi.string().required(),
            skuCode: Joi.string().allow(null, ''),
            skuSpecifications: Joi.string().allow(null, ''),
            productImage: Joi.string().allow(null, ''),
            unitPrice: Joi.number().required().min(0),
            marketPriceSnapshot: Joi.number().allow(null),
            costPriceSnapshot: Joi.number().allow(null),
            weightSnapshot: Joi.number().allow(null),
            volumeSnapshot: Joi.number().allow(null),
            thirdPartySpuId: Joi.string().allow(null, ''),
            thirdPartySkuId: Joi.string().allow(null, ''),
            thirdPartyProductCode: Joi.string().allow(null, ''),
            thirdPartyItemSnapshot: Joi.alternatives().try(
                Joi.string().allow(null, ''),
                Joi.object().allow(null)
            ),
            quantity: Joi.number().required().integer().min(1),
            itemPaidAmount: Joi.number().min(0).default(0)
          })
      ).min(1).required().messages({
        'array.min': '订单项不能为空',
        'any.required': '订单项是必填项'
      }),

      // 配送信息
      shipping: Joi.object({
        recipientName: Joi.string().required(),
        recipientPhone: Joi.string().required(),
        regionProvinceId: Joi.number().integer().required(),
        regionCityId: Joi.number().integer().required(),
        regionDistrictId: Joi.number().integer().required(),
        regionPathName: Joi.string().required(),
        streetAddress: Joi.string().required(),
        postalCode: Joi.string().allow(null, ''),
        shippingCompanyCode: Joi.string().allow(null, ''),
        shippingCompanyName: Joi.string().allow(null, ''),
        trackingNumber: Joi.string().allow(null, '')
      }).allow(null),

      // 销售人员信息
      salespeople: Joi.object({
        salespersonId: Joi.string().allow(null, ''),
        salespersonName: Joi.string().allow(null, ''),
        salespersonPhone: Joi.string().allow(null, ''),
        commissionRate: Joi.number().min(0).max(100).allow(null),
        commissionAmount: Joi.number().min(0).allow(null)
      }).allow(null),

      // 发票信息
      invoice: Joi.object({
        headerId: Joi.string().allow(null, ''),
        invoiceType: Joi.number().integer().valid(
          InvoiceTypeEnum.NONE,     // 0-不开发票
          InvoiceTypeEnum.PERSONAL, // 1-个人发票
          InvoiceTypeEnum.COMPANY   // 2-企业发票
        ).default(InvoiceTypeEnum.NONE).messages({
          'number.base': '发票类型必须是数字',
          'number.integer': '发票类型必须是整数',
          'any.only': '无效的发票类型'
        }),
        invoiceAmount: Joi.number().min(0).allow(null).messages({
          'number.base': '发票金额必须是数字',
          'number.min': '发票金额不能小于0'
        }),
        remark: Joi.string().allow(null, '').max(500).messages({
          'string.max': '发票备注不能超过500个字符'
        })
      }).allow(null)
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证更新订单状态的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateStatusUpdate(data) {
    const schema = Joi.object({
      status: Joi.string().valid('pending', 'processing', 'shipped', 'delivered', 'completed', 'returned', 'cancelled').required().messages({
        'string.empty': '订单状态不能为空',
        'any.only': '无效的订单状态',
        'any.required': '订单状态是必填项'
      }),
      remark: Joi.string().allow(null, '').max(500)
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证更新订单支付信息的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validatePaymentUpdate(data) {
    const schema = Joi.object({
      paymentMethod: Joi.string().required().messages({
        'string.empty': '支付方式不能为空',
        'any.required': '支付方式是必填项'
      }),
      paymentMethodId: Joi.number().integer().valid(
          PaymentMethodEnum.WECHAT,    // 1-微信支付
          PaymentMethodEnum.ALIPAY,    // 2-支付宝
          PaymentMethodEnum.UNIONPAY,  // 3-银联
          PaymentMethodEnum.COD,       // 4-货到付款
          PaymentMethodEnum.OTHER      // 5-其他
      ).required().messages({
        'number.base': '支付方式ID必须是数字',
        'number.integer': '支付方式ID必须是整数',
        'any.only': '无效的支付方式ID',
        'any.required': '支付方式ID是必填项'
      }),
      transactionId: Joi.string().allow(null, ''),
      paidAmount: Joi.number().required().min(0.01).messages({
        'number.base': '支付金额必须是数字',
        'number.min': '支付金额必须大于0',
        'any.required': '支付金额是必填项'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证取消订单的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateCancelOrder(data) {
    const schema = Joi.object({
      cancelReason: Joi.string().required().max(200).messages({
        'string.empty': '取消原因不能为空',
        'string.max': '取消原因不能超过200个字符',
        'any.required': '取消原因是必填项'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证更新订单物流信息的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateShippingUpdate(data) {
    const schema = Joi.object({
      // 配送方式：
      // 1-快递物流（必填快递物流单号、必填快递物流公司、选填附件）
      // 2-自定义物流（必填物流单号、必填物流公司、选填附件）
      // 3-商家自送（选填附件）
      // 4-线下自取（选填附件）
      // 5-无需物流（选填附件）
      shippingMethod: Joi.number().integer().required().valid(1, 2, 3, 4, 5).messages({
        'number.base': '配送方式必须是数字',
        'number.integer': '配送方式必须是整数',
        'any.required': '配送方式是必填项',
        'any.only': '配送方式必须是有效值：1-快递物流，2-自定义物流，3-商家自送，4-线下自取，5-无需物流'
      }),

      // 物流信息 - 根据配送方式设置验证规则
      shippingCompanyCode: Joi.string().when('shippingMethod', {
        is: Joi.valid(1, 2), // 快递物流或自定义物流时必填
        then: Joi.string().required().messages({
          'string.empty': '物流公司不能为空',
          'any.required': '当配送方式为快递物流或自定义物流时，物流公司是必填项'
        }),
        otherwise: Joi.string().allow(null, '')
      }),
      shippingCompanyName: Joi.string().when('shippingMethod', {
        is: Joi.valid(1, 2), // 快递物流或自定义物流时必填
        then: Joi.string().required().messages({
          'string.empty': '物流公司名称不能为空',
          'any.required': '当配送方式为快递物流或自定义物流时，物流公司名称是必填项'
        }),
        otherwise: Joi.string().allow(null, '')
      }),
      trackingNumber: Joi.string().when('shippingMethod', {
        is: Joi.valid(1, 2), // 快递物流或自定义物流时必填
        then: Joi.string().required().messages({
          'string.empty': '物流单号不能为空',
          'any.required': '当配送方式为快递物流或自定义物流时，物流单号是必填项'
        }),
        otherwise: Joi.string().allow(null, '')
      }),

      // 附件图片URL - 所有配送方式都可选填
      imagesUrl: Joi.string().allow(null, '').description('附件图片URL地址')
    }).unknown(true);

    return schema.validate(data, { abortEarly: false });
  }
  
  /**
   * 验证绑定跟单员的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} - 验证结果
   */
  static validateFollowerUpdate(data) {
    const schema = Joi.object({
      followerId: Joi.string().required().messages({
        'string.empty': '跟单员ID不能为空',
        'any.required': '跟单员ID是必填项'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }
}

module.exports = OrderDto;
