/**
 * 溯源码查询相关DTO
 * 注意：我们不再使用Joi进行验证，而是直接在控制器中处理参数
 */

// 溯源码查询参数说明
const querySourceCodeParams = {
  sourceCode: '溯源码（必填）'
};

// 溯源码查询情况列表参数说明
const sourceCodeQueryListParams = {
  page: '页码（默认1）',
  limit: '每页数量（默认10）',
  sourceCode: '溯源码（可选）',
  brandName: '品牌名称（可选）',
  startTime: '创建开始时间戳（毫秒，可选）',
  endTime: '创建结束时间戳（毫秒，可选）'
};

// 溯源码查询日志列表参数说明
const sourceCodeQueryLogListParams = {
  sourceCode: '溯源码（必填）',
  page: '页码（默认1）',
  limit: '每页数量（默认10）'
};

module.exports = {
  querySourceCodeParams,
  sourceCodeQueryListParams,
  sourceCodeQueryLogListParams
};
