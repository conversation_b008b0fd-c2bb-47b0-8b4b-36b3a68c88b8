/**
 * 自主品牌溯源码DTO
 */
const Joi = require('joi');

class SelfSourceCodeDto {
  /**
   * 获取列表验证
   * @returns {Joi.ObjectSchema}
   */
  static getListValidate() {
    return Joi.object({
      page: Joi.number().integer().min(1).default(1),
      pageSize: Joi.number().integer().min(1).max(100).default(20),
      limit: Joi.number().integer().min(1).max(100),
      sourceCode: Joi.string().allow(''),
      brandName: Joi.string().allow(''),
      exportStatus: Joi.number().valid(0, 1),
      startTime: Joi.number(),
      endTime: Joi.number(),
      // 添加对brandId的支持，允许字符串和数字类型
      brandId: Joi.alternatives().try(
        Joi.number(),
        Joi.string()
      ),
      // 支持下划线形式的brand_id
      brand_id: Joi.alternatives().try(
        Joi.number(),
        Joi.string()
      ),
    });
  }

  /**
   * 获取溯源码详情的验证规则
   * @returns {Object} 验证规则
   */
  static getDetailValidate() {
    return Joi.object({
      id: Joi.alternatives()
        .try(
          Joi.number().integer().positive(),
          Joi.string().pattern(/^\d+$/)
        )
        .required()
        .description('溯源码ID')
    });
  }

  /**
   * 根据自主品牌ID查询溯源码列表的验证规则
   * @returns {Object} 验证规则
   */
  static getByBrandIdValidate() {
    return Joi.object({
      page: Joi.number().integer().min(1).default(1).description('页码'),
      limit: Joi.number().integer().min(1).max(100).default(20).description('每页数量'),
      pageSize: Joi.number().integer().min(1).max(100).default(20).description('每页数量（与limit同义）'),
      // 使用更宽松的brandId验证，允许空值，在控制器中进行额外检查
      // 支持雪花ID（大数字），不限制数字长度
      brandId: Joi.alternatives()
        .try(
          Joi.number(),
          Joi.string()
        )
        .description('自主品牌ID'),
      // 兼容下划线命名风格
      brand_id: Joi.alternatives()
        .try(
          Joi.number(),
          Joi.string()
        )
        .description('自主品牌ID（下划线格式）'),
      sourceCode: Joi.string().description('溯源码'),
      source_code: Joi.string().description('溯源码（下划线格式）'),
      brandName: Joi.string().description('自主品牌名称'),
      brand_name: Joi.string().description('自主品牌名称（下划线格式）'),
      exportStatus: Joi.number().valid(0, 1).description('导出状态：0-未导出，1-已导出'),
      export_status: Joi.number().valid(0, 1).description('导出状态（下划线格式）：0-未导出，1-已导出'),
      startTime: Joi.number().integer().description('创建开始时间戳（毫秒）'),
      start_time: Joi.number().integer().description('创建开始时间戳（毫秒，下划线格式）'),
      endTime: Joi.number().integer().description('创建结束时间戳（毫秒）'),
      end_time: Joi.number().integer().description('创建结束时间戳（毫秒，下划线格式）')
    }).or('brandId', 'brand_id'); // 至少需要提供一种形式的brandId
  }

  /**
   * 生成溯源码的验证规则
   * @returns {Object} 验证规则
   */
  static generateValidate() {
    return Joi.object({
      // 支持下划线和驼峰两种命名风格
      brand_id: Joi.alternatives()
        .try(
          Joi.number().integer().positive(),
          Joi.string().pattern(/^\d+$/)
        )
        .description('自主品牌ID'),
      brandId: Joi.alternatives()
        .try(
          Joi.number().integer().positive(),
          Joi.string().pattern(/^\d+$/)
        )
        .description('自主品牌ID'),
      count: Joi.number().integer().min(1).max(1000).default(1).description('生成数量')
    }).or('brand_id', 'brandId'); // 至少需要提供一种形式的brandId
  }

  /**
   * 批量更新溯源码状态的验证规则
   * @returns {Object} 验证规则
   */
  static updateStatusValidate() {
    return Joi.object({
      ids: Joi.array().items(
        Joi.alternatives()
          .try(
            Joi.number().integer().positive(),
            Joi.string().pattern(/^\d+$/)
          )
      ).required().description('溯源码ID数组'),
      status: Joi.number().valid(0, 1).required().description('状态：0-未启用，1-已启用')
    });
  }
}

module.exports = SelfSourceCodeDto;
