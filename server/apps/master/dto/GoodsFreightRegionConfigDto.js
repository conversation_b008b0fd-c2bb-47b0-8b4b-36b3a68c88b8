/**
 * 运费模板区域配置数据传输对象
 */
class GoodsFreightRegionConfigDto {
  /**
   * 创建运费模板区域配置DTO
   * @param {Object} data - 区域配置数据
   */
  constructor(data = {}) {
    this.id = data.id || null;
    this.freight_template_id = data.freight_template_id || null;
    this.region_codes = data.region_codes || null; // 多个区域编码，用逗号分隔，null表示全国
    this.region_names = data.region_names || ''; // 区域名称会自动生成，此处保留以兼容数据库结构
    this.first_item = data.first_item || 1; // 首件/首重/首体积数量
    this.first_fee = data.first_fee || 0; // 首件/首重/首体积费用
    this.additional_item = data.additional_item || 1; // 续件/续重/续体积数量
    this.additional_fee = data.additional_fee || 0; // 续件/续重/续体积费用
    this.created_at = data.created_at || Math.floor(Date.now() / 1000);
    this.updated_at = data.updated_at || Math.floor(Date.now() / 1000);
    this.deleted_at = data.deleted_at || null; // 软删除时间戳，null表示未删除
    this.created_by = data.created_by || null; // 创建人ID
    this.updated_by = data.updated_by || null; // 最后更新人ID
  }

  /**
   * 验证DTO数据
   * @returns {Object} 包含验证结果和错误信息
   */
  validate() {
    const errors = [];

    // 验证区域编码
    if (!this.region_codes) {
      errors.push('区域编码不能为空');
    }

    // 验证首件/首重/首体积数量
    if (this.first_item <= 0) {
      errors.push('首件/首重/首体积数量必须大于0');
    }

    // 验证首件/首重/首体积费用
    if (this.first_fee < 0) {
      errors.push('首件/首重/首体积费用不能为负数');
    }

    // 验证续件/续重/续体积数量
    if (this.additional_item <= 0) {
      errors.push('续件/续重/续体积数量必须大于0');
    }

    // 验证续件/续重/续体积费用
    if (this.additional_fee < 0) {
      errors.push('续件/续重/续体积费用不能为负数');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 转换为数据库对象
   * @returns {Object} 数据库对象
   */
  toDbObject() {
    return {
      freight_template_id: this.freight_template_id ? BigInt(this.freight_template_id) : null,
      region_codes: this.region_codes,
      region_names: this.region_names, // 区域名称会自动生成，此处保留以兼容数据库结构
      first_item: this.first_item,
      first_fee: this.first_fee,
      additional_item: this.additional_item,
      additional_fee: this.additional_fee,
      created_at: this.created_at,
      updated_at: this.updated_at,
      deleted_at: this.deleted_at,
      created_by: this.created_by ? BigInt(this.created_by) : null,
      updated_by: this.updated_by ? BigInt(this.updated_by) : null
    };
  }

  /**
   * 从数据库对象创建DTO
   * @param {Object} dbObject - 数据库对象
   * @returns {GoodsFreightRegionConfigDto} DTO实例
   */
  static fromDbObject(dbObject) {
    return new GoodsFreightRegionConfigDto(dbObject);
  }
}

module.exports = GoodsFreightRegionConfigDto;
