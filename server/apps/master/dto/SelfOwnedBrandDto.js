const Joi = require('joi');

// 自主品牌DTO验证类
class SelfOwnedBrandDto {
  /**
   * 查询自主品牌列表的验证规则
   * @returns {Object} 验证规则
   */
  static getListValidate() {
    return Joi.object({
      page: Joi.number().integer().min(1).default(1).description('页码'),
      limit: Joi.number().integer().min(1).max(100).default(20).description('每页数量'),
      pageSize: Joi.number().integer().min(1).max(100).default(20).description('每页数量（与limit同义）'),
      // 前端传参要求的字段
      name: Joi.string().allow('').description('自主品牌名称'),
      registerId: Joi.alternatives().try(
        Joi.string().pattern(/^\d+$/),
        Joi.number().integer()
      ).description('商标号'),
      ownerName: Joi.string().allow('').description('持有人'),
      status: Joi.number().valid(0, 1).description('状态：0-禁用，1-启用'),
      // 兼容旧的参数名称 - 下划线命名
      trademark_code: Joi.string().allow('').description('商标代码'),
      registration_type: Joi.string().allow('').description('商标类型'),
      owner_name: Joi.string().allow('').description('商标申请人'),
      last_procedure_status: Joi.string().allow('').description('经营状态'),
      start_time: Joi.number().integer().allow(null).description('创建开始时间戳（毫秒）'),
      end_time: Joi.number().integer().allow(null).description('创建结束时间戳（毫秒）'),
      // 兼容旧的参数名称 - 驼峰命名
      trademarkCode: Joi.string().allow('').description('商标代码'),
      registrationType: Joi.string().allow('').description('商标类型'),
      lastProcedureStatus: Joi.string().allow('').description('经营状态'),
      startTime: Joi.number().integer().allow(null).description('创建开始时间戳（毫秒）'),
      endTime: Joi.number().integer().allow(null).description('创建结束时间戳（毫秒）')
    });
  }

  /**
   * 获取自主品牌详情的验证规则
   * @returns {Object} 验证规则
   */
  static getDetailValidate() {
    return Joi.object({
      id: Joi.alternatives()
        .try(
          Joi.number().integer().positive(),
          Joi.string().pattern(/^\d+$/)
        )
        .required()
        .description('自主品牌ID，可以是数字或字符串形式的雪花ID')
    });
  }

  /**
   * 创建自主品牌的验证规则
   * @returns {Object} 验证规则
   */
  static createValidate() {
    return Joi.object({
      // 基本信息 - 同时支持下划线和驼峰命名
      // 下划线命名字段
      name: Joi.string().max(100).description('商标名称'),
      image: Joi.string().max(255).description('logo图片url'),
      owner_name: Joi.string().max(255).description('申请人'),
      owner_address: Joi.string().max(255).description('申请人地址'),
      agency: Joi.string().max(255).description('代理组织机构'),
      register_id: Joi.number().integer().max(9999999999999).allow(null).description('注册id，最大长度13位'),
      registration_type: Joi.string().max(30).description('商标类型'),
      pre_ann_date: Joi.date().allow(null, '').description('初审公告日期'),
      apply_date: Joi.date().allow(null, '').description('申请日期'),
      reg_ann_date: Joi.date().allow(null, '').description('注册公告日期'),
      last_procedure_status: Joi.string().max(30).description('当前状态'),
      product_desc: Joi.alternatives().try(
        Joi.string().allow('', null),
        Joi.array().items(Joi.object({
          goodsCode: Joi.string().required().description('商品编码'),
          goodsName: Joi.string().required().description('商品名称'),
          beDeleted: Joi.string().valid('true', 'false').default('false').description('是否删除')
        }))
      ).description('商品服务列表，可以是字符串或JSON数组'),
      exclusive_date_limit: Joi.string().max(60).description('专用权期限'),
      brand_images: Joi.array().items(Joi.string().max(255)).description('品牌附件列表（多个）'),
      sourceCodeRule: Joi.string().max(100).allow('', null).description('溯源码规则'),
      brandLocation: Joi.string().max(100).allow('', null).description('品牌位置'),
      issues: Joi.alternatives().try(
        Joi.string().allow('', null),
        Joi.array().items(Joi.object({
          app_name: Joi.string().description('申请人名称'),
          issueImg: Joi.string().description('公告图片ID'),
          issueImgUrl: Joi.string().description('公告图片URL'),
          issueName: Joi.string().description('公告名称'),
          issueNo: Joi.number().description('公告期号'),
          paaDate: Joi.string().description('公告日期'),
          tm_name: Joi.string().description('商标名称')
        }))
      ).description('商标公告，可以是字符串或JSON数组'),
      status: Joi.number().valid(0, 1).default(1).description('状态：0-禁用，1-启用'),
      class_id: Joi.number().integer().description('商标类型id'),
      class_name: Joi.string().max(60).description('商标类名'),
      trademark_code: Joi.string().max(100).description('商标代码'),
      
      // 驼峰命名字段
      ownerName: Joi.string().max(255).description('申请人'),
      ownerAddress: Joi.string().max(255).description('申请人地址'),
      registerId: Joi.number().integer().max(9999999999999).allow(null).description('注册id，最大长度13位'),
      registrationType: Joi.string().max(30).description('商标类型'),
      preAnnDate: Joi.date().allow(null, '').description('初审公告日期'),
      applyDate: Joi.date().allow(null, '').description('申请日期'),
      regAnnDate: Joi.date().allow(null, '').description('注册公告日期'),
      lastProcedureStatus: Joi.string().max(30).description('当前状态'),
      productDesc: Joi.alternatives().try(
        Joi.string().allow('', null),
        Joi.array().items(Joi.object({
          goodsCode: Joi.string().required().description('商品编码'),
          goodsName: Joi.string().required().description('商品名称'),
          beDeleted: Joi.string().valid('true', 'false').default('false').description('是否删除')
        }))
      ).description('商品服务列表，可以是字符串或JSON数组'),
      exclusiveDateLimit: Joi.string().max(60).description('专用权期限'),
      classId: Joi.number().integer().description('商标类型id'),
      className: Joi.string().max(60).description('商标类名'),
      trademarkCode: Joi.string().max(100).description('商标代码'),
      brandImages: Joi.array().items(Joi.string().max(255)).description('品牌附件列表（多个）'),
      // sourceCodeRule: Joi.string().max(100).allow('', null).description('溯源码规则'),
      // brandLocation: Joi.string().max(100).allow('', null).description('品牌位置')
    }).or(
      'name',
      'ownerName',
      'image',
      'owner_name',
      'ownerName',
      'owner_address',
      'ownerAddress',
      'agency',
      'registration_type',
      'registrationType',
      'last_procedure_status',
      'lastProcedureStatus',
      'exclusive_date_limit',
      'exclusiveDateLimit',
      'class_id',
      'classId',
      'class_name', 
      'className',
      'trademark_code',
      'trademarkCode'
    );
  }

  /**
   * 更新自主品牌的验证规则
   * @returns {Object} 验证规则
   */
  static updateValidate() {
    return Joi.object({
      // 基本信息字段 - 只支持驼峰命名
      id: Joi.alternatives()
        .try(
          Joi.number().integer().positive(),
          Joi.string().pattern(/^\d+$/)
        )
        .optional() // ID已经在URL参数中提供
        .description('自主品牌ID，可以是数字或字符串形式的雪花ID'),
      // 驼峰命名字段
      name: Joi.string().max(100).description('商标名称'),
      image: Joi.string().max(255).description('logo图片url'),
      ownerName: Joi.string().max(255).description('申请人'),
      ownerAddress: Joi.string().max(255).description('申请人地址'),
      agency: Joi.string().max(255).description('代理组织机构'),
      registerId: Joi.number().integer().max(9999999999999).allow(null).description('注册id，最大长度13位'),
      registrationType: Joi.string().max(30).description('商标类型'),
      preAnnDate: Joi.date().allow(null, '').description('初审公告日期'),
      applyDate: Joi.date().allow(null, '').description('申请日期'),
      regAnnDate: Joi.date().allow(null, '').description('注册公告日期'),
      lastProcedureStatus: Joi.string().max(30).description('当前状态'),
      productDesc: Joi.alternatives().try(
        Joi.string().allow('', null),
        Joi.array().items(Joi.object({
          goodsCode: Joi.string().required().description('商品编码'),
          goodsName: Joi.string().required().description('商品名称'),
          beDeleted: Joi.string().valid('true', 'false').default('false').description('是否删除')
        }))
      ).description('商品服务列表，可以是字符串或JSON数组'),
      exclusiveDateLimit: Joi.string().max(60).description('专用权期限'),
      issues: Joi.alternatives().try(
        Joi.string().allow('', null),
        Joi.array().items(Joi.object({
          app_name: Joi.string().description('申请人名称'),
          issueImg: Joi.string().description('公告图片ID'),
          issueImgUrl: Joi.string().description('公告图片URL'),
          issueName: Joi.string().description('公告名称'),
          issueNo: Joi.number().description('公告期号'),
          paaDate: Joi.string().description('公告日期'),
          tm_name: Joi.string().description('商标名称')
        }))
      ).description('商标公告，可以是字符串或JSON数组'),
      status: Joi.number().valid(0, 1).description('状态：0-禁用，1-启用'),
      classId: Joi.number().integer().description('商标类型id'),
      className: Joi.string().max(60).description('商标类名'),
      trademarkCode: Joi.string().max(100).description('商标代码'),
      brandImages: Joi.array().items(
        Joi.string().description('品牌附件URL')
      ).optional().description('品牌附件列表（多个），可选'),
      sourceCodeRule: Joi.string().max(100).allow('', null).description('溯源码规则'),
      brandLocation: Joi.string().max(100).allow('', null).description('品牌位置'),
      productDesc: Joi.alternatives().try(
        Joi.string().allow('', null),
        Joi.array().items(Joi.object({
          goodsCode: Joi.string().required().description('商品编码'),
          goodsName: Joi.string().required().description('商品名称'),
          beDeleted: Joi.string().valid('true', 'false').default('false').description('是否删除')
        }))
      ).description('商品服务列表，可以是字符串或JSON数组'),
      exclusiveDateLimit: Joi.string().max(60).description('专用权期限'),
      classId: Joi.number().integer().description('商标类型id'),
      className: Joi.string().max(60).description('商标类名'),
      trademarkCode: Joi.string().max(100).description('商标代码'),
      brandImages: Joi.array().items(
        Joi.string().description('品牌附件URL')
      ).optional().description('品牌附件列表（多个），可选'),
      sourceCodeRule: Joi.string().max(100).allow('', null).description('溯源码规则'),
      brandLocation: Joi.string().max(100).allow('', null).description('品牌位置'),
    });
  }

  /**
   * 删除自主品牌的验证规则
   * @returns {Object} 验证规则
   */
  static deleteValidate() {
    return Joi.object({
      id: Joi.alternatives()
        .try(
          Joi.number().integer().positive(),
          Joi.string().pattern(/^\d+$/)
        )
        .required()
        .description('自主品牌ID，可以是数字或字符串形式的雪花ID')
    });
  }
}

module.exports = SelfOwnedBrandDto;
