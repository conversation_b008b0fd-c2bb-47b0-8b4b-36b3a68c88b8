/**
 * 订单包裹DTO验证
 */
const Joi = require('joi');

// 创建包裹的验证规则
const createOrderPackageSchema = Joi.object({
  orderId: Joi.number().positive().required().messages({
    'number.base': '订单ID必须是数字',
    'number.positive': '订单ID必须是正数',
    'any.required': '订单ID是必填项'
  }),
  
  recipientName: Joi.string().max(100).required().messages({
    'string.base': '收件人姓名必须是字符串',
    'string.max': '收件人姓名不能超过100个字符',
    'any.required': '收件人姓名是必填项'
  }),
  
  recipientPhone: Joi.string().max(20).required().messages({
    'string.base': '收件人电话必须是字符串',
    'string.max': '收件人电话不能超过20个字符',
    'any.required': '收件人电话是必填项'
  }),
  
  regionPathName: Joi.string().allow(null, '').messages({
    'string.base': '地区路径名称必须是字符串'
  }),
  
  streetAddress: Joi.string().required().messages({
    'string.base': '详细地址必须是字符串',
    'any.required': '详细地址是必填项'
  }),
  
  postalCode: Joi.string().max(20).allow(null, '').messages({
    'string.base': '邮政编码必须是字符串',
    'string.max': '邮政编码不能超过20个字符'
  }),
  
  shippingMethod: Joi.number().integer().min(1).max(5).allow(null).messages({
    'number.base': '配送方式必须是数字',
    'number.integer': '配送方式必须是整数',
    'number.min': '配送方式值最小为1',
    'number.max': '配送方式值最大为5'
  }),
  
  items: Joi.array().min(1).items(
    Joi.object({
      orderItemId: Joi.number().positive().required().messages({
        'number.base': '订单项ID必须是数字',
        'number.positive': '订单项ID必须是正数',
        'any.required': '订单项ID是必填项'
      }),
      quantity: Joi.number().integer().positive().required().messages({
        'number.base': '数量必须是数字',
        'number.integer': '数量必须是整数',
        'number.positive': '数量必须是正数',
        'any.required': '数量是必填项'
      })
    })
  ).required().messages({
    'array.base': '包裹商品必须是数组',
    'array.min': '包裹必须至少包含一个商品',
    'any.required': '包裹商品是必填项'
  }),
  
  remark: Joi.string().allow(null, '').messages({
    'string.base': '备注必须是字符串'
  })
});

// 更新包裹的验证规则
const updateOrderPackageSchema = Joi.object({
  recipientName: Joi.string().max(100).messages({
    'string.base': '收件人姓名必须是字符串',
    'string.max': '收件人姓名不能超过100个字符'
  }),
  
  recipientPhone: Joi.string().max(20).messages({
    'string.base': '收件人电话必须是字符串',
    'string.max': '收件人电话不能超过20个字符'
  }),
  
  regionPathName: Joi.string().allow(null, '').messages({
    'string.base': '地区路径名称必须是字符串'
  }),
  
  streetAddress: Joi.string().messages({
    'string.base': '详细地址必须是字符串'
  }),
  
  postalCode: Joi.string().max(20).allow(null, '').messages({
    'string.base': '邮政编码必须是字符串',
    'string.max': '邮政编码不能超过20个字符'
  }),
  
  shippingMethod: Joi.number().integer().min(1).max(5).allow(null).messages({
    'number.base': '配送方式必须是数字',
    'number.integer': '配送方式必须是整数',
    'number.min': '配送方式值最小为1',
    'number.max': '配送方式值最大为5'
  }),
  
  remark: Joi.string().allow(null, '').messages({
    'string.base': '备注必须是字符串'
  })
});

// 包裹发货的验证规则
const shipOrderPackageSchema = Joi.object({
  shippingCompanyCode: Joi.string().max(50).required().messages({
    'string.base': '物流公司编码必须是字符串',
    'string.max': '物流公司编码不能超过50个字符',
    'any.required': '物流公司编码是必填项'
  }),
  
  shippingCompanyName: Joi.string().max(100).required().messages({
    'string.base': '物流公司名称必须是字符串',
    'string.max': '物流公司名称不能超过100个字符',
    'any.required': '物流公司名称是必填项'
  }),
  
  trackingNumber: Joi.string().max(100).required().messages({
    'string.base': '物流单号必须是字符串',
    'string.max': '物流单号不能超过100个字符',
    'any.required': '物流单号是必填项'
  }),
  
  imagesUrl: Joi.string().allow(null, '').messages({
    'string.base': '附件图片URL必须是字符串'
  }),
  
  shippingOrigin: Joi.string().max(255).allow(null, '').messages({
    'string.base': '发货地必须是字符串',
    'string.max': '发货地不能超过255个字符'
  })
});

// 查询包裹列表的验证规则
const getOrderPackagesSchema = Joi.object({
  orderId: Joi.number().positive().messages({
    'number.base': '订单ID必须是数字',
    'number.positive': '订单ID必须是正数'
  }),
  
  packageSn: Joi.string().messages({
    'string.base': '包裹编号必须是字符串'
  }),
  
  trackingNumber: Joi.string().messages({
    'string.base': '物流单号必须是字符串'
  }),
  
  shippingStatus: Joi.number().integer().min(0).max(2).messages({
    'number.base': '包裹状态必须是数字',
    'number.integer': '包裹状态必须是整数',
    'number.min': '包裹状态值最小为0',
    'number.max': '包裹状态值最大为2'
  }),
  
  startTime: Joi.number().messages({
    'number.base': '开始时间必须是时间戳数字'
  }),
  
  endTime: Joi.number().messages({
    'number.base': '结束时间必须是时间戳数字'
  }),
  
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': '页码必须是数字',
    'number.integer': '页码必须是整数',
    'number.min': '页码最小为1'
  }),
  
  pageSize: Joi.number().integer().min(1).max(100).default(20).messages({
    'number.base': '每页条数必须是数字',
    'number.integer': '每页条数必须是整数',
    'number.min': '每页条数最小为1',
    'number.max': '每页条数最大为100'
  })
});

module.exports = {
  createOrderPackageSchema,
  updateOrderPackageSchema,
  shipOrderPackageSchema,
  getOrderPackagesSchema
};
