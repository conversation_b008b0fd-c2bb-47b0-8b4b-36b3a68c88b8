/**
 * 支付配置数据传输对象
 * 用于验证支付配置相关的请求数据
 */
const Joi = require('joi');

class PaymentConfigDto {
  /**
   * 微信支付配置验证规则
   */
  static validateWechatConfig(data) {
    const schema = Joi.object({
      enabled: Joi.boolean().required().messages({
        'boolean.base': '启用状态必须是布尔值',
        'any.required': '启用状态不能为空'
      }),
      
      mch_id: Joi.when('enabled', {
        is: true,
        then: Joi.string().required().min(1).max(50).messages({
          'string.base': '商户号必须是字符串',
          'string.empty': '商户号不能为空',
          'string.min': '商户号长度至少1位',
          'string.max': '商户号长度不能超过50位',
          'any.required': '启用时商户号不能为空'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }).required(),
      
      api_v3_key: Joi.when('enabled', {
        is: true,
        then: Joi.string().required().min(32).max(32).messages({
          'string.base': 'APIv3密钥必须是字符串',
          'string.empty': 'APIv3密钥不能为空',
          'string.min': 'APIv3密钥长度必须是32位',
          'string.max': 'APIv3密钥长度必须是32位',
          'any.required': '启用时APIv3密钥不能为空'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }),
      
      mch_private_key: Joi.when('enabled', {
        is: true,
        then: Joi.string().required().messages({
          'string.base': '商户私钥必须是字符串',
          'string.empty': '商户私钥不能为空',
          'any.required': '启用时商户私钥不能为空'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }),
      
      mch_public_key: Joi.string().allow('', null).optional(),
      
      wechat_pay_cert: Joi.when('enabled', {
        is: true,
        then: Joi.string().required().messages({
          'string.base': '微信支付公钥必须是字符串',
          'string.empty': '微信支付公钥不能为空',
          'any.required': '启用时微信支付公钥不能为空'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }),
      
      wechat_pay_public_key_id: Joi.string().allow('', null).optional(),
      
      native_callback_url: Joi.when('enabled', {
        is: true,
        then: Joi.string().uri().required().messages({
          'string.base': '回调链接必须是字符串',
          'string.uri': '回调链接格式不正确',
          'any.required': '启用时回调链接不能为空'
        }),
        otherwise: Joi.string().uri().allow('', null).optional()
      }),
      
      config_name: Joi.string().min(1).max(100).default('默认微信支付配置').messages({
        'string.base': '配置名称必须是字符串',
        'string.min': '配置名称长度至少1位',
        'string.max': '配置名称长度不能超过100位'
      }),
      
      environment: Joi.string().valid('sandbox', 'production').default('sandbox').messages({
        'string.base': '环境必须是字符串',
        'any.only': '环境只能是sandbox或production'
      }),
      
      remark: Joi.string().max(255).allow('', null).optional().messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过255位'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 支付宝配置验证规则
   */
  static validateAlipayConfig(data) {
    const schema = Joi.object({
      enabled: Joi.boolean().required().messages({
        'boolean.base': '启用状态必须是布尔值',
        'any.required': '启用状态不能为空'
      }),
      
      app_id: Joi.when('enabled', {
        is: true,
        then: Joi.string().required().min(1).max(50).messages({
          'string.base': '支付宝应用ID必须是字符串',
          'string.empty': '支付宝应用ID不能为空',
          'string.min': '支付宝应用ID长度至少1位',
          'string.max': '支付宝应用ID长度不能超过50位',
          'any.required': '启用时支付宝应用ID不能为空'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }),
      
      private_key: Joi.when('enabled', {
        is: true,
        then: Joi.string().required().messages({
          'string.base': '应用私钥必须是字符串',
          'string.empty': '应用私钥不能为空',
          'any.required': '启用时应用私钥不能为空'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }),
      
      public_key: Joi.string().allow('', null).optional(),
      
      alipay_public_key: Joi.when('enabled', {
        is: true,
        then: Joi.string().required().messages({
          'string.base': '支付宝公钥必须是字符串',
          'string.empty': '支付宝公钥不能为空',
          'any.required': '启用时支付宝公钥不能为空'
        }),
        otherwise: Joi.string().allow('', null).optional()
      }),
      
      alipay_cert: Joi.string().allow('', null).optional(),
      
      config_name: Joi.string().min(1).max(100).default('默认支付宝配置').messages({
        'string.base': '配置名称必须是字符串',
        'string.min': '配置名称长度至少1位',
        'string.max': '配置名称长度不能超过100位'
      }),
      
      environment: Joi.string().valid('sandbox', 'production').default('sandbox').messages({
        'string.base': '环境必须是字符串',
        'any.only': '环境只能是sandbox或production'
      }),
      
      remark: Joi.string().max(255).allow('', null).optional().messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过255位'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 更新配置验证规则
   */
  static validateUpdateConfig(data) {
    const schema = Joi.object({
      config_name: Joi.string().min(1).max(100).optional().messages({
        'string.base': '配置名称必须是字符串',
        'string.min': '配置名称长度至少1位',
        'string.max': '配置名称长度不能超过100位'
      }),
      
      enabled: Joi.boolean().optional().messages({
        'boolean.base': '启用状态必须是布尔值'
      }),
      
      environment: Joi.string().valid('sandbox', 'production').optional().messages({
        'string.base': '环境必须是字符串',
        'any.only': '环境只能是sandbox或production'
      }),
      
      remark: Joi.string().max(255).allow('', null).optional().messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过255位'
      }),
      
      // 微信支付字段
      mch_id: Joi.string().min(1).max(50).optional().messages({
        'string.base': '商户号必须是字符串',
        'string.min': '商户号长度至少1位',
        'string.max': '商户号长度不能超过50位'
      }),
      
      api_v3_key: Joi.string().length(32).optional().messages({
        'string.base': 'APIv3密钥必须是字符串',
        'string.length': 'APIv3密钥长度必须是32位'
      }),
      
      mch_private_key: Joi.string().optional().messages({
        'string.base': '商户私钥必须是字符串'
      }),
      
      mch_public_key: Joi.string().allow('', null).optional(),
      
      wechat_pay_cert: Joi.string().optional().messages({
        'string.base': '微信支付公钥必须是字符串'
      }),
      
      wechat_pay_public_key_id: Joi.string().allow('', null).optional(),
      
      native_callback_url: Joi.string().uri().optional().messages({
        'string.base': '回调链接必须是字符串',
        'string.uri': '回调链接格式不正确'
      }),
      
      // 支付宝字段
      app_id: Joi.string().min(1).max(50).optional().messages({
        'string.base': '支付宝应用ID必须是字符串',
        'string.min': '支付宝应用ID长度至少1位',
        'string.max': '支付宝应用ID长度不能超过50位'
      }),
      
      private_key: Joi.string().optional().messages({
        'string.base': '应用私钥必须是字符串'
      }),
      
      public_key: Joi.string().allow('', null).optional(),
      
      alipay_public_key: Joi.string().optional().messages({
        'string.base': '支付宝公钥必须是字符串'
      }),
      
      alipay_cert: Joi.string().allow('', null).optional()
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 查询参数验证规则
   */
  static validateQuery(data) {
    const schema = Joi.object({
      payment_type: Joi.string().valid('wechat', 'alipay', 'unionpay').optional().messages({
        'string.base': '支付类型必须是字符串',
        'any.only': '支付类型只能是wechat、alipay或unionpay'
      }),
      
      enabled: Joi.boolean().optional().messages({
        'boolean.base': '启用状态必须是布尔值'
      }),
      
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '页码必须是数字',
        'number.integer': '页码必须是整数',
        'number.min': '页码最小值为1'
      }),
      
      limit: Joi.number().integer().min(1).max(100).default(20).messages({
        'number.base': '每页数量必须是数字',
        'number.integer': '每页数量必须是整数',
        'number.min': '每页数量最小值为1',
        'number.max': '每页数量最大值为100'
      })
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * ID参数验证规则
   */
  static validateId(id) {
    const schema = Joi.string().pattern(/^\d+$/).required().messages({
      'string.base': 'ID必须是字符串',
      'string.pattern.base': 'ID格式不正确',
      'any.required': 'ID不能为空'
    });

    return schema.validate(id);
  }
}

module.exports = PaymentConfigDto;
