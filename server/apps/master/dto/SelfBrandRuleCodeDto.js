const Joi = require('joi');

// 自主品牌规则码DTO验证类
class SelfBrandRuleCodeDto {
  /**
   * 查询自主品牌规则码列表的验证规则
   * @returns {Object} 验证规则
   */
  static getListValidate() {
    return Joi.object({
      page: Joi.number().integer().min(1).default(1).description('页码'),
      limit: Joi.number().integer().min(1).max(100).default(20).description('每页数量'),
      name: Joi.string().allow('').description('品牌名称'),
      rule_code: Joi.string().allow('').description('溯源码规则'),
      start_time: Joi.number().integer().allow(null).description('创建开始时间戳（毫秒）'),
      end_time: Joi.number().integer().allow(null).description('创建结束时间戳（毫秒）')
    });
  }

  /**
   * 获取自主品牌规则码详情的验证规则
   * @returns {Object} 验证规则
   */
  static getDetailValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('品牌规则ID')
    });
  }

  /**
   * 创建自主品牌规则码的验证规则
   * @returns {Object} 验证规则
   */
  static createValidate() {
    return Joi.object({
      name: Joi.string().required().max(100).description('品牌名称'),
      rule_code: Joi.string().required().max(16).description('溯源码规则'),
      brand_source: Joi.string().required().max(100).description('品牌所属地')
    });
  }

  /**
   * 更新自主品牌规则码的验证规则
   * @returns {Object} 验证规则
   */
  static updateValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('品牌规则ID'),
      name: Joi.string().max(100).description('品牌名称'),
      rule_code: Joi.string().max(16).description('溯源码规则'),
      brand_source: Joi.string().max(100).description('品牌所属地')
    });
  }

  /**
   * 删除自主品牌规则码的验证规则
   * @returns {Object} 验证规则
   */
  static deleteValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('品牌规则ID')
    });
  }
}

module.exports = SelfBrandRuleCodeDto;
