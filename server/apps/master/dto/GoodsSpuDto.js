/**
 * 商品数据传输对象
 */
const Joi = require('joi');

class GoodsSpuDto {
  /**
   * 验证创建商品的数据
   * @param {Object} data - 商品数据
   * @returns {Object} - 验证结果
   */
  static validateCreate(data) {
    const schema = Joi.object({
      // --- 基本信息 ---
      basicInfo: Joi.object({
        name: Joi.string().required().messages({
          'string.empty': '商品名称不能为空',
          'any.required': '商品名称为必填项'
        }),
        categoryId: Joi.string().required().messages({
          'string.empty': '商品分类ID不能为空',
          'any.required': '商品分类ID为必填项'
        }),
        brandId: Joi.alternatives().try(
          Joi.number().integer(),
          Joi.string().pattern(/^\d+$/)
        ).required().messages({
          'alternatives.match': '商品品牌ID格式不正确',
          'any.required': '商品品牌ID为必填项'
        }),
        subtitle: Joi.string().allow('').allow(null),
        spuImages: Joi.array().items(
          Joi.string()
        ).allow(null).allow('').default([]).messages({
          'array.base': '商品图片格式不正确'
        }),
        video: Joi.alternatives().try(
          Joi.object({
            url: Joi.string().required(),
            coverUrl: Joi.string()
          }),
          Joi.string(),
          Joi.allow(null)
        )
      }).required(),

      // --- 关联信息 ---
      associations: Joi.object({
        tagIds: Joi.array().items(
          Joi.alternatives().try(Joi.string(), Joi.number().integer())
        ).default([]),
        serviceIds: Joi.array().items(
          Joi.alternatives().try(Joi.string(), Joi.number().integer())
        ).default([])
      }).default({ tagIds: [], serviceIds: [] }),

      // --- 物流表单 ---
      logisticsForm: Joi.object({
        isFreeShipping: Joi.alternatives().try(
          Joi.number().valid(1, 2),
          Joi.string().valid('1', '2')
        ).required().messages({
          'any.only': '包邮状态必须是1(包邮)或2(不包邮)',
          'any.required': '包邮状态为必填项'
        }),
        freightTemplateId: Joi.when('isFreeShipping', {
          is: Joi.alternatives().try(2, '2'),
          then: Joi.string().required().messages({
            'string.empty': '不包邮时运费模板ID不能为空',
            'any.required': '不包邮时运费模板ID为必填项'
          }),
          otherwise: Joi.string().allow('').allow(null)
        })
      }).required(),

      // --- 配送表单 ---
      // 完全放宽验证，允许任意类型的deliveryArea和deliveryTime，服务层会忽略这些值
      deliveryForm: Joi.object().unknown().optional().default({}),

      // --- 属性值 ---
      attributeValues: Joi.array().items(
        Joi.object({
          attributeItemId: Joi.alternatives().try(Joi.string(), Joi.number().integer()).required(),
          value: Joi.string().required()
        })
      ).default([]),

      // --- 规格/SKU ---
      specifications: Joi.object({
        skuType: Joi.alternatives().try(
          Joi.number().valid(1, 2),
          Joi.string().valid('1', '2', 'single', 'multi')
        ).required().messages({
          'any.only': 'SKU类型必须是1(单规格)或2(多规格)',
          'any.required': 'SKU类型为必填项'
        }),
        individual: Joi.when('skuType', {
          is: Joi.alternatives().try(1, '1', 'single'),
          then: Joi.object({
            price: Joi.number().precision(2).positive().required().messages({
              'number.base': '销售价必须为数字',
              'number.positive': '销售价必须大于0',
              'any.required': '销售价为必填项'
            }),
            marketPrice: Joi.alternatives().try(
              Joi.number().precision(2).min(0),
              Joi.string().allow('').allow(null).custom((value, helpers) => {
                if (value === '' || value === null) return null;
                return helpers.error('any.invalid');
              })
            ).allow(null).allow('').default(null),
            costPrice: Joi.alternatives().try(
              Joi.number().precision(2).min(0),
              Joi.string().allow('').allow(null).custom((value, helpers) => {
                if (value === '' || value === null) return null;
                return helpers.error('any.invalid');
              })
            ).allow(null).allow('').default(null),
            stock: Joi.number().integer().min(0).required().messages({
              'number.base': '库存必须为数字',
              'number.min': '库存不能为负数',
              'any.required': '库存为必填项'
            }),
            salesVolume: Joi.number().integer().min(0).default(0).messages({
              'number.base': 'SKU销量必须为数字',
              'number.min': 'SKU销量不能为负数'
            }),
            code: Joi.string().required().messages({
              'string.empty': 'SKU编码不能为空',
              'any.required': 'SKU编码为必填项'
            }),
            image: Joi.string().allow(null),
            weight: Joi.number().allow(null),
            volume: Joi.number().allow(null),
            unit: Joi.alternatives().try(
              Joi.string(),
              Joi.number()
            ).allow(null)
          }).required(),
          otherwise: Joi.object().optional()
        }),
        multiple: Joi.when('skuType', {
          is: Joi.alternatives().try(2, '2', 'multi'),
          then: Joi.object({
            specs: Joi.array().items(
              Joi.object({
                name: Joi.string().required(),
                values: Joi.array().items(Joi.string()).min(1).required(),
                inputValue: Joi.string().allow('').allow(null)
              })
            ).min(1).required().messages({
              'array.min': '多规格商品至少需要一个规格定义',
              'any.required': '多规格商品需要规格定义'
            }),
            skuList: Joi.array().items(
              Joi.object({
                specs: Joi.object().pattern(Joi.string(), Joi.string()),
                price: Joi.number().precision(2).positive().required().messages({
                  'number.base': '销售价必须为数字',
                  'number.positive': '销售价必须大于0',
                  'any.required': '销售价为必填项'
                }),
                marketPrice: Joi.number().precision(2).min(0).allow(null),
                costPrice: Joi.number().precision(2).min(0).allow(null),
                stock: Joi.number().integer().min(0).required().messages({
                  'number.base': '库存必须为数字',
                  'number.min': '库存不能为负数',
                  'any.required': '库存为必填项'
                }),
                salesVolume: Joi.number().integer().min(0).default(0).messages({
                  'number.base': 'SKU销量必须为数字',
                  'number.min': 'SKU销量不能为负数'
                }),
                code: Joi.string().required().messages({
                  'string.empty': 'SKU编码不能为空',
                  'any.required': 'SKU编码为必填项'
                }),
                image: Joi.string().allow(null),
                weight: Joi.number().allow(null),
                volume: Joi.number().allow(null),
                unit: Joi.alternatives().try(
                  Joi.string(),
                  Joi.number()
                ).allow(null)
              })
            ).min(1).required().messages({
              'array.min': '至少需要一个SKU',
              'any.required': 'SKU列表为必填项'
            })
          }),
          otherwise: Joi.any().optional()
        })
      }).required(),

      // --- 商品详情 ---
      detailForm: Joi.object({
        content: Joi.string().required().messages({
          'string.empty': '商品详情不能为空',
          'any.required': '商品详情为必填项'
        })
      }).required(),

      // --- 其他设置 ---
      otherForm: Joi.object({
        status: Joi.number().valid(0, 1).required().messages({
          'any.only': '商品状态必须是0(下架)或1(上架)',
          'any.required': '商品状态为必填项'
        }),
        sort: Joi.number().integer().min(0).default(100),
        sales: Joi.number().integer().min(0).default(0),
        totalSales: Joi.number().integer().min(0).default(0).messages({
          'number.base': '总销量必须为数字',
          'number.min': '总销量不能为负数'
        }),
        keywords: Joi.string().allow('').allow(null),
        seoTitle: Joi.string().allow('').allow(null),
        seoDescription: Joi.string().allow('').allow(null),
        isVirtual: Joi.alternatives().try(
          Joi.number().valid(0, 1),
          Joi.string().valid('0', '1')
        ).default(0).messages({
          'any.only': '虚拟商品标志必须是0(实物商品)或1(虚拟商品)'
        }),
        isShippingRequired: Joi.alternatives().try(
          Joi.number().valid(0, 1),
          Joi.string().valid('0', '1')
        ).default(1).messages({
          'any.only': '配送需求标志必须是0(不需要配送)或1(需要配送)'
        })
      }).required()
    });

    return schema.validate(data, { abortEarly: false });
  }

  /**
   * 验证查询商品列表的参数
   * @param {Object} params - 查询参数
   * @returns {Object} - 验证结果
   */
  static validateListQuery(params) {
    const schema = Joi.object({
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '页码必须是数字',
        'number.min': '页码必须大于等于1'
      }),
      pageSize: Joi.number().integer().min(1).max(100).default(20).messages({
        'number.base': '每页数量必须是数字',
        'number.min': '每页数量必须大于等于1',
        'number.max': '每页数量不能超过100'
      }),
      // 是否查询回收站商品（0正常商品、1回收站商品）
      isDeleted: Joi.alternatives().try(
        Joi.string().valid('0', '1'),
        Joi.number().valid(0, 1)
      ).allow(null).allow('').default('0').messages({
        'any.only': '回收站状态必须是0(正常商品)或1(回收站商品)'
      }),
      // 商品分类ID筛选，允许雪花ID和其他格式
      categoryId: Joi.alternatives().try(
        Joi.string(),
        Joi.number()
      ).allow(null).allow('').messages({
        'alternatives.match': '分类ID格式不正确'
      }),
      keyword: Joi.string().allow('').allow(null),
      // 商品状态筛选（0下架、1上架）
      status: Joi.alternatives().try(
        Joi.string().valid('on_shelf', 'off_shelf', '0', '1'),
        Joi.number().valid(0, 1)
      ).allow(null).allow(''),
      // 品牌ID筛选，允许雪花ID和其他格式
      brandId: Joi.alternatives().try(
        Joi.string(),
        Joi.number()
      ).allow(null).allow('').messages({
        'alternatives.match': '品牌ID格式不正确'
      }),
      // 创建时间范围筛选（毫秒时间戳）
      startTime: Joi.alternatives().try(
        Joi.string().pattern(/^\d+$/), // 毫秒时间戳字符串
        Joi.number().integer(),         // 毫秒时间戳数字
        Joi.date().iso()               // ISO格式日期字符串
      ).allow('').allow(null),
      endTime: Joi.alternatives().try(
        Joi.string().pattern(/^\d+$/),
        Joi.number().integer(),
        Joi.date().iso()
      ).allow('').allow(null)
        .when('startTime', {
          is: Joi.exist().not(null).not(''),
          then: Joi.alternatives().try(
            Joi.string().pattern(/^\d+$/),
            Joi.number().integer(),
            Joi.date().iso()
          ).messages({
            'date.min': '结束时间必须大于等于开始时间'
          })
        }),
      sortField: Joi.string().valid('id', 'created_at', 'sales', 'stock', 'price').default('created_at'),
      sortOrder: Joi.string().valid('asc', 'desc').default('desc')
    });

    return schema.validate(params, { abortEarly: false });
  }
  
  /**
   * 验证更新商品的数据
   * @param {Object} data - 商品数据
   * @returns {Object} - 验证结果
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      // --- 基本信息 ---
      basicInfo: Joi.object({
        productName: Joi.string().messages({
          'string.empty': '商品名称不能为空'
        }),
        categoryId: Joi.string().messages({
          'string.empty': '商品分类ID不能为空'
        }),
        brandId: Joi.number().integer().messages({
          'number.base': '商品品牌ID必须为数字'
        }),
        subtitle: Joi.string().allow('').allow(null),
        freightTemplateId: Joi.string().messages({
          'string.empty': '运费模板ID不能为空'
        })
      }),

      // --- 媒体文件 ---
      media: Joi.object({
        spuImages: Joi.array().items(
          Joi.object({
            url: Joi.string().required(),
            sortOrder: Joi.number().integer().default(0),
            isDefault: Joi.boolean().default(false)
          })
        ).min(1).messages({
          'array.min': '至少需要一张商品图片'
        }),
        video: Joi.object({
          url: Joi.string().required(),
          coverUrl: Joi.string().required(),
          sortOrder: Joi.number().integer().default(0)
        }).allow(null)
      }),

      // --- 关联信息 ---
      associations: Joi.object({
        tagIds: Joi.array().items(Joi.number().integer()),
        serviceIds: Joi.array().items(Joi.number().integer())
      }),

      // --- 属性信息 ---
      attributes: Joi.object({
        attributeValues: Joi.array().items(
          Joi.object({
            attributeItemId: Joi.alternatives().try(Joi.string(), Joi.number().integer()).required(),
            value: Joi.string().required()
          })
        ),
        qualificationInfo: Joi.array().items(
          Joi.object({
            name: Joi.string().required(),
            url: Joi.string().required()
          })
        )
      }),

      // --- 规格/SKU ---
      skuInfo: Joi.object({
        skuType: Joi.string().valid('single', 'multi').messages({
          'any.only': 'SKU类型必须是"single"或"multi"'
        }),
        specDefinitions: Joi.when('skuType', {
          is: 'multi',
          then: Joi.array().items(
            Joi.object({
              name: Joi.string().required(),
              values: Joi.array().items(Joi.string()).min(1).required()
            })
          ).min(1).messages({
            'array.min': '多规格商品至少需要一个规格定义'
          }),
          otherwise: Joi.array().optional()
        }),
        skuList: Joi.array().items(
          Joi.object({
            skuCode: Joi.string().required().messages({
              'string.empty': 'SKU编码不能为空',
              'any.required': 'SKU编码为必填项'
            }),
            specs: Joi.object().pattern(Joi.string(), Joi.string()),
            image: Joi.object({
              url: Joi.string().required()
            }).allow(null),
            salesPrice: Joi.number().precision(2).min(0).required().messages({
              'number.base': '销售价必须为数字',
              'number.min': '销售价必须大于等于0',
              'any.required': '销售价为必填项'
            }),
            stock: Joi.number().integer().min(0).required().messages({
              'number.base': '库存必须为数字',
              'number.min': '库存不能为负数',
              'any.required': '库存为必填项'
            }),
            salesVolume: Joi.number().integer().min(0).default(0).messages({
              'number.base': 'SKU销量必须为数字',
              'number.min': 'SKU销量不能为负数'
            }),
            unit: Joi.string().allow('').allow(null),
            weight: Joi.number().allow(null),
            volume: Joi.number().allow(null),
            costPrice: Joi.number().precision(2).min(0).allow(null),
            marketPrice: Joi.number().precision(2).min(0).allow(null),
            lowStockThreshold: Joi.number().integer().min(0).allow(null),
            barcode: Joi.string().allow('').allow(null),
            expirationDate: Joi.string().allow('').allow(null)
          })
        )
      }),

      // --- 商品详情 ---
      details: Joi.object({
        description: Joi.string().allow('').allow(null)
      }),

      // --- 其他设置 ---
      settings: Joi.object({
        status: Joi.string().valid('on_shelf', 'off_shelf').messages({
          'any.only': '商品状态必须是"on_shelf"或"off_shelf"'
        }),
        sortOrder: Joi.number().integer().min(0),
        totalSales: Joi.number().integer().min(0).messages({
          'number.base': '总销量必须为数字',
          'number.min': '总销量不能为负数'
        }),
        isVirtual: Joi.boolean(),
        metaTitle: Joi.string().allow('').allow(null),
        metaKeywords: Joi.string().allow('').allow(null),
        metaDescription: Joi.string().allow('').allow(null)
      })
    });

    return schema.validate(data, { abortEarly: false });
  }
}

// 创建商品验证模式
const goodsSpuCreateSchema = {
  validate: (data) => GoodsSpuDto.validateCreate(data)
};

// 更新商品验证模式
const goodsSpuUpdateSchema = {
  validate: (data) => GoodsSpuDto.validateUpdate(data)
};

module.exports = GoodsSpuDto;
module.exports.goodsSpuCreateSchema = goodsSpuCreateSchema;
module.exports.goodsSpuUpdateSchema = goodsSpuUpdateSchema;
