const Joi = require('joi');

// 产品品牌DTO验证类
class SelfProductBrandDto {
  /**
   * 查询产品品牌列表的验证规则
   * @returns {Object} 验证规则
   */
  static getListValidate() {
    return Joi.object({
      page: Joi.number().integer().min(1).default(1).description('页码'),
      limit: Joi.number().integer().min(1).max(100).default(20).description('每页数量'),
      brand_code: Joi.string().allow('').description('品牌代码'),
      brand_name: Joi.string().allow('').description('品牌名称'),
      is_self_brand: Joi.number().valid(1, 2).description('是否自主品牌：1-否，2-是'),
      start_time: Joi.number().integer().allow(null).description('创建开始时间戳（毫秒）'),
      end_time: Joi.number().integer().allow(null).description('创建结束时间戳（毫秒）')
    });
  }

  /**
   * 获取产品品牌详情的验证规则
   * @returns {Object} 验证规则
   */
  static getDetailValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('产品品牌ID')
    });
  }

  /**
   * 创建产品品牌的验证规则
   * @returns {Object} 验证规则
   */
  static createValidate() {
    return Joi.object({
      brand_code: Joi.string().required().max(32).description('品牌代码'),
      brand_name: Joi.string().required().max(255).description('品牌名称'),
      is_self_brand: Joi.number().valid(1, 2).required().description('是否自主品牌：1-否，2-是')
    });
  }

  /**
   * 更新产品品牌的验证规则
   * @returns {Object} 验证规则
   */
  static updateValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('产品品牌ID'),
      brand_code: Joi.string().max(32).description('品牌代码'),
      brand_name: Joi.string().max(255).description('品牌名称'),
      is_self_brand: Joi.number().valid(1, 2).description('是否自主品牌：1-否，2-是')
    });
  }

  /**
   * 删除产品品牌的验证规则
   * @returns {Object} 验证规则
   */
  static deleteValidate() {
    return Joi.object({
      id: Joi.number().integer().positive().required().description('产品品牌ID')
    });
  }
}

module.exports = SelfProductBrandDto;
