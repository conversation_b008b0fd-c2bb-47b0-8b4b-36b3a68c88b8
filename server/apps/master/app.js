const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { prisma } = require('../../core/database/prisma');
const userRoutes = require('./system/user/routes/UserManagementRoute');
const menuRoutes = require('./system/user/routes/MenuManagementRoute');
const integrationRoutes = require('./system/integration/routes/index');
const platformRoutes = require('./platformManagement/index');

const app = express();

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 初始化Swagger路径对象
app.swaggerPaths = {};

// 路由
// 注册用户和菜单路由
app.use('/api/v1/master/system/user', userRoutes);
app.use('/api/v1/master/system/menu', menuRoutes);

// 注册平台管理模块路由
app.use('/api/v1/master/platform', platformRoutes);

// 合并Swagger路径
if (userRoutes.paths) {
  app.swaggerPaths = { ...app.swaggerPaths, ...userRoutes.paths };
}
if (menuRoutes.paths) {
  app.swaggerPaths = { ...app.swaggerPaths, ...menuRoutes.paths };
}
if (platformRoutes.paths) {
  app.swaggerPaths = { ...app.swaggerPaths, ...platformRoutes.paths };
}

// 注册集成模块路由（使用函数式注册）
integrationRoutes(app, prisma);

// 错误处理
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        code: 500,
        message: '服务器内部错误',
        data: null
    });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});
