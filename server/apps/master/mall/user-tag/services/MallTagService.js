const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');
const prismaManager = require('../../../../../core/prisma');

/**
 * 商城会员标签服务类
 */
class MallTagService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    // 使用传入的prisma客户端，如果没有则使用单例模式获取
    this.prisma = prisma || prismaManager.getClient('base');
  }

  /**
   * 创建商城会员标签
   * @param {Object} tagData 标签数据
   * @returns {Object} 创建的标签对象
   */
  async create(tagData) {
    try {
      // 检查标签组是否存在
      const tagGroup = await this.prisma.MallTagGroup.findFirst({
        where: {
          id: BigInt(tagData.tag_group_id),
          deleted_at: null
        }
      });

      if (!tagGroup) {
        throw new Error('标签组不存在或已删除');
      }

      // 检查标签名称是否已存在于同一标签组
      const existingTag = await this.prisma.MallTag.findFirst({
        where: {
          tag_title: tagData.tag_name,
          tag_group_id: BigInt(tagData.tag_group_id),
          subsite_id: tagData.subsite_id ? BigInt(tagData.subsite_id) : null,
          deleted_at: null
        }
      });

      if (existingTag) {
        throw new Error('标签名称在同一标签组中已存在');
      }

      // 生成ID
      const id = generateSnowflakeId();
      
      // 获取当前时间戳
      const now = BigInt(Date.now());
      
      // 构建创建数据
      const createData = {
        id: BigInt(id),
        tag_title: tagData.tag_name,
        tag_group_id: BigInt(tagData.tag_group_id),
        tag_sort: tagData.sort || 0,
        tag_buildin: tagData.tag_buildin || 0,
        tag_enable: tagData.tag_enable || 1,
        subsite_id: tagData.subsite_id ? BigInt(tagData.subsite_id) : null,
        status: tagData.status || 1,
        created_at: now,
        updated_at: now,
        created_by: tagData.createdBy ? BigInt(tagData.createdBy) : null,
        updated_by: tagData.createdBy ? BigInt(tagData.createdBy) : null,
        remark: tagData.remark || null
      };
      
      console.log('创建标签数据:', createData);
      
      const newTag = await this.prisma.MallTag.create({
        data: createData
      });

      return newTag;
    } catch (error) {
      console.error('创建标签失败:', error);
      throw error;
    }
  }

  /**
   * 更新商城会员标签
   * @param {string} id 标签ID
   * @param {Object} tagData 更新的标签数据
   * @returns {Object} 更新后的标签对象
   */
  async update(id, tagData) {
    try {
      // 检查标签是否存在
      const existingTag = await this.prisma.MallTag.findUnique({
        where: { id: BigInt(id) }
      });

      if (!existingTag) {
        throw new Error('标签不存在');
      }

      // 如果要更新标签组，检查标签组是否存在
      if (tagData.tag_group_id && tagData.tag_group_id !== existingTag.tag_group_id.toString()) {
        const tagGroup = await this.prisma.MallTagGroup.findFirst({
          where: {
            id: BigInt(tagData.tag_group_id),
            deleted_at: null
          }
        });

        if (!tagGroup) {
          throw new Error('标签组不存在或已删除');
        }
      }

      // 如果要更新标签名称，检查新名称是否已被使用
      if (tagData.tag_name && tagData.tag_name !== existingTag.tag_title) {
        const tagWithSameName = await this.prisma.MallTag.findFirst({
          where: { 
            tag_title: tagData.tag_name,
            tag_group_id: tagData.tag_group_id ? BigInt(tagData.tag_group_id) : existingTag.tag_group_id,
            subsite_id: tagData.subsite_id ? BigInt(tagData.subsite_id) : existingTag.subsite_id,
            deleted_at: null,
            id: { not: BigInt(id) }
          }
        });

        if (tagWithSameName) {
          throw new Error('标签名称在同一标签组中已存在');
        }
      }

      // 如果是系统内置标签，不允许修改tag_buildin字段
      if (existingTag.tag_buildin === 1 && tagData.tag_buildin === 0) {
        throw new Error('系统内置标签不允许修改为非系统内置');
      }
      
      // 更新时间戳
      const now = BigInt(Date.now());
      
      // 构建更新数据
      const updateData = {};
      
      console.log('服务层收到的更新数据:', tagData);
      
      // 只更新提供的字段
      if (tagData.tag_name !== undefined) {
        updateData.tag_title = tagData.tag_name;
      }
      
      if (tagData.tag_group_id !== undefined) {
        updateData.tag_group_id = BigInt(tagData.tag_group_id);
      }
      
      if (tagData.sort !== undefined) {
        updateData.tag_sort = tagData.sort;
      }
      
      if (tagData.tag_buildin !== undefined) {
        updateData.tag_buildin = tagData.tag_buildin;
      }
      
      if (tagData.tag_enable !== undefined) {
        updateData.tag_enable = tagData.tag_enable;
      }
      
      if (tagData.subsite_id !== undefined) {
        updateData.subsite_id = tagData.subsite_id ? BigInt(tagData.subsite_id) : null;
      }
      
      if (tagData.status !== undefined) {
        updateData.status = tagData.status;
      }
      
      if (tagData.remark !== undefined) {
        updateData.remark = tagData.remark;
      }
      
      // 更新时间和操作人
      updateData.updated_at = now;
      updateData.updated_by = tagData.updatedBy ? BigInt(tagData.updatedBy) : null;
      
      console.log('实际更新数据:', updateData);
      
      // 更新标签
      const updatedTag = await this.prisma.MallTag.update({
        where: { id: BigInt(id) },
        data: updateData
      });

      return updatedTag;
    } catch (error) {
      console.error('更新标签失败:', error);
      throw error;
    }
  }

  /**
   * 删除商城会员标签（软删除）
   * @param {string} id 标签ID
   * @param {string} updatedBy 操作人ID
   * @returns {Object} 删除结果
   */
  async delete(id, updatedBy) {
    try {
      // 检查标签是否存在
      const existingTag = await this.prisma.MallTag.findFirst({
        where: { 
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!existingTag) {
        throw new Error('标签不存在或已删除');
      }

      // 如果是系统内置标签，不允许删除
      if (existingTag.tag_buildin === 1) {
        throw new Error('系统内置标签不允许删除');
      }
      
      // 软删除
      const now = BigInt(Date.now());
      
      const result = await this.prisma.MallTag.update({
        where: { id: BigInt(id) },
        data: {
          deleted_at: now,
          updated_at: now,
          updated_by: updatedBy ? BigInt(updatedBy) : null
        }
      });

      return { success: true, message: '删除成功' };
    } catch (error) {
      console.error('删除标签失败:', error);
      throw error;
    }
  }

  /**
   * 获取商城会员标签详情
   * @param {string} id 标签ID
   * @returns {Object} 标签详情
   */
  async getDetail(id) {
    try {
      const tag = await this.prisma.MallTag.findUnique({
        where: { 
          id: BigInt(id)
        },
        include: {
          tag_group: {
            select: {
              id: true,
              tag_group_name: true
            }
          }
        }
      });

      if (!tag) {
        throw new Error('标签不存在或已删除');
      }
      
      return tag;
    } catch (error) {
      console.error('获取标签详情失败:', error);
      throw error;
    }

  }

  /**
   * 获取商城会员标签列表
   * @param {Object} queryParams 查询参数
   * @returns {Object} 标签列表和总数
   */
  async getList(queryParams) {
    const {
      id,
      tagTitle,
      tagGroupId,
      tagBuildin,
      tagEnable,
      subsiteId,
      status,
      keyword,
      page = 1,
      pageSize = 10,
      sortField = 'tag_sort',
      sortOrder = 'asc'
    } = queryParams;

    // 构建查询条件
    const where = {
      deleted_at: null
    };

    if (id) {
      where.id = BigInt(id);
    }

    if (tagTitle) {
      where.tag_title = tagTitle;
    }

    if (tagGroupId) {
      where.tag_group_id = BigInt(tagGroupId);
    }

    if (tagBuildin !== undefined) {
      where.tag_buildin = tagBuildin;
    }

    if (tagEnable !== undefined) {
      where.tag_enable = tagEnable;
    }

    if (subsiteId) {
      where.subsite_id = BigInt(subsiteId);
    }

    if (status !== undefined) {
      where.status = status;
    }

    if (keyword) {
      where.tag_title = {
        contains: keyword
      };
    }

    // 计算总数
    const total = await this.prisma.mall_tag.count({ where });

    // 获取分页数据
    const tags = await this.prisma.mall_tag.findMany({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: {
        [sortField]: sortOrder
      },
      include: {
        mall_tag_group: {
          select: {
            id: true,
            tag_group_name: true
          }
        }
      }
    });

    return {
      list: tags,
      pagination: {
        current: page,
        pageSize,
        total
      }
    };
  }

  /**
   * 获取特定标签组下的所有标签
   * @param {string} groupId 标签组ID
   * @returns {Array} 标签列表，包含所有字段
   */
  async getTagsByGroupId(groupId) {
    try {
      // 不使用select选项，返回标签的所有字段
      const tags = await this.prisma.MallTag.findMany({
        where: {
          tag_group_id: BigInt(groupId),
          tag_enable: 1,
          deleted_at: null
        },
        orderBy: {
          tag_sort: 'asc'
        }
      });

      console.log('获取到标签组ID:', groupId, '的标签数量:', tags.length);
      if (tags.length > 0) {
        console.log('标签字段示例:', Object.keys(tags[0]));
      }

      return tags;
    } catch (error) {
      console.error('获取标签组下的标签失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有有效的标签（用于下拉选择）
   * @param {Object} queryParams 查询参数
   * @returns {Array} 标签列表
   */
  async getAllEnabled(queryParams = {}) {
    const { subsiteId, tagGroupId } = queryParams;

    try {
      const where = {
        deleted_at: null,
        tag_enable: 1,
        status: 1
      };

      if (subsiteId) {
        where.subsite_id = BigInt(subsiteId);
      }

      if (tagGroupId) {
        where.tag_group_id = BigInt(tagGroupId);
      }

      const tags = await this.prisma.MallTag.findMany({
        where,
        orderBy: [
          { tag_group_id: 'asc' },
          { tag_sort: 'asc' }
        ],
        select: {
          id: true,
          tag_title: true,
          tag_group_id: true,
          tag_sort: true,
          tag_buildin: true,
          tag_group: {
            select: {
              tag_group_name: true
            }
          }
        }
      });

      return tags;
    } catch (error) {
      console.error('获取有效标签列表失败:', error);
      throw error;
    }
  }

  /**
   * 批量为用户添加标签
   * @param {string} userId 用户ID
   * @param {Array<string>} tagIds 标签ID数组
   * @param {string} operatorId 操作人ID
   * @returns {Object} 添加结果
   */
  async addTagsToUser(userId, tagIds, operatorId) {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    if (!tagIds || !Array.isArray(tagIds) || tagIds.length === 0) {
      throw new Error('标签ID数组不能为空');
    }

    // 检查用户是否存在
    const user = await this.prisma.MasterMallUser.findUnique({
      where: {
        id: BigInt(userId),
        deleted_at: null
      }
    });

    if (!user) {
      throw new Error('用户不存在或已删除');
    }

    // 检查标签是否都存在且有效
    const tags = await this.prisma.mall_tag.findMany({
      where: {
        id: {
          in: tagIds.map(id => BigInt(id))
        },
        tag_enable: 1,
        status: 1,
        deleted_at: null
      }
    });

    if (tags.length !== tagIds.length) {
      throw new Error('部分标签不存在或已禁用');
    }

    // 获取用户已有的标签
    const existingUserTags = await this.prisma.mall_user_tag.findMany({
      where: {
        user_id: BigInt(userId),
        deleted_at: null
      }
    });

    const existingTagIds = existingUserTags.map(ut => ut.tag_id.toString());
    
    // 过滤出需要添加的标签ID
    const newTagIds = tagIds.filter(id => !existingTagIds.includes(id));
    
    if (newTagIds.length === 0) {
      return { success: true, message: '用户已拥有所有指定标签', addedCount: 0 };
    }

    // 获取当前时间戳
    const now = BigInt(Date.now());
    
    // 构建批量创建数据
    const createData = newTagIds.map(tagId => ({
      id: BigInt(generateSnowflakeId()),
      user_id: BigInt(userId),
      tag_id: BigInt(tagId),
      created_at: now,
      updated_at: now,
      created_by: operatorId ? BigInt(operatorId) : null,
      updated_by: operatorId ? BigInt(operatorId) : null
    }));
    
    // 批量创建用户标签关联
    const result = await this.prisma.$transaction(
      createData.map(data => 
        this.prisma.mall_user_tag.create({ data })
      )
    );

    return { 
      success: true, 
      message: '添加标签成功', 
      addedCount: result.length,
      totalCount: existingUserTags.length + result.length
    };
  }

  /**
   * 批量为用户移除标签
   * @param {string} userId 用户ID
   * @param {Array<string>} tagIds 标签ID数组
   * @param {string} operatorId 操作人ID
   * @returns {Object} 移除结果
   */
  async removeTagsFromUser(userId, tagIds, operatorId) {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    if (!tagIds || !Array.isArray(tagIds) || tagIds.length === 0) {
      throw new Error('标签ID数组不能为空');
    }

    // 获取当前时间戳
    const now = BigInt(Date.now());
    
    // 软删除用户标签关联
    const result = await this.prisma.mall_user_tag.updateMany({
      where: {
        user_id: BigInt(userId),
        tag_id: {
          in: tagIds.map(id => BigInt(id))
        },
        deleted_at: null
      },
      data: {
        deleted_at: now,
        updated_at: now,
        updated_by: operatorId ? BigInt(operatorId) : null
      }
    });

    return { 
      success: true, 
      message: '移除标签成功', 
      removedCount: result.count
    };
  }

  /**
   * 获取用户的所有标签
   * @param {string} userId 用户ID
   * @returns {Array} 标签列表
   */
  async getUserTags(userId) {
    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    // 检查用户是否存在
    const user = await this.prisma.MasterMallUser.findUnique({
      where: {
        id: BigInt(userId),
        deleted_at: null
      }
    });

    if (!user) {
      throw new Error('用户不存在或已删除');
    }

    // 获取用户的标签
    const userTags = await this.prisma.mall_user_tag.findMany({
      where: {
        user_id: BigInt(userId),
        deleted_at: null
      },
      include: {
        mall_tag: {
          include: {
            mall_tag_group: true
          }
        }
      }
    });

    // 按标签组分组
    const groupedTags = {};
    
    userTags.forEach(ut => {
      if (ut.mall_tag && ut.mall_tag.mall_tag_group) {
        const groupId = ut.mall_tag.tag_group_id.toString();
        const groupName = ut.mall_tag.mall_tag_group.tag_group_name;
        
        if (!groupedTags[groupId]) {
          groupedTags[groupId] = {
            groupId,
            groupName,
            tags: []
          };
        }
        
        groupedTags[groupId].tags.push({
          id: ut.tag_id.toString(),
          title: ut.mall_tag.tag_title,
          sort: ut.mall_tag.tag_sort,
          isBuildin: ut.mall_tag.tag_buildin === 1
        });
      }
    });

    return Object.values(groupedTags);
  }
}

module.exports = MallTagService;
