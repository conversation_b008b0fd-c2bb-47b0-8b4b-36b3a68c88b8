const express = require('express');
const MallTagController = require('../controllers/MallTagController');
const authMiddleware = require('../../../../../core/middleware/AuthMiddleware');
const { prisma } = require('../../../../../core/database/prisma');

const router = express.Router();
// 获取base schema的prisma客户端
const tagController = new MallTagController(prisma);

/**
 * @swagger
 * /api/v1/master/mall/user-tag:
 *   post:
 *     summary: 创建商城会员标签
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tag_title
 *               - tag_group_id
 *             properties:
 *               tag_title:
 *                 type: string
 *                 description: 标签名称
 *               tag_group_id:
 *                 type: string
 *                 description: 所属标签组ID
 *               tag_sort:
 *                 type: integer
 *                 description: 排序值，数字越小越靠前
 *                 default: 0
 *               tag_buildin:
 *                 type: integer
 *                 description: 是否系统内置：1-是，0-否
 *                 default: 0
 *               tag_enable:
 *                 type: integer
 *                 description: 是否启用：1-启用，0-禁用
 *                 default: 1
 *               subsite_id:
 *                 type: string
 *                 description: 子站点ID，为空表示适用于所有子站点
 *               status:
 *                 type: integer
 *                 description: 状态：1-正常，0-禁用
 *                 default: 1
 *               remark:
 *                 type: string
 *                 description: 备注信息
 *     responses:
 *       200:
 *         description: 创建成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/', authMiddleware, tagController.create.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag/{id}:
 *   put:
 *     summary: 更新商城会员标签
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tag_title:
 *                 type: string
 *                 description: 标签名称
 *               tag_group_id:
 *                 type: string
 *                 description: 所属标签组ID
 *               tag_sort:
 *                 type: integer
 *                 description: 排序值，数字越小越靠前
 *               tag_buildin:
 *                 type: integer
 *                 description: 是否系统内置：1-是，0-否
 *               tag_enable:
 *                 type: integer
 *                 description: 是否启用：1-启用，0-禁用
 *               subsite_id:
 *                 type: string
 *                 description: 子站点ID，为空表示适用于所有子站点
 *               status:
 *                 type: integer
 *                 description: 状态：1-正常，0-禁用
 *               remark:
 *                 type: string
 *                 description: 备注信息
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.put('/:id', authMiddleware, tagController.update.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag/{id}:
 *   delete:
 *     summary: 删除商城会员标签
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.delete('/:id', authMiddleware, tagController.delete.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag/{id}:
 *   get:
 *     summary: 获取商城会员标签详情
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', authMiddleware, tagController.getDetail.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag:
 *   get:
 *     summary: 获取商城会员标签列表
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *         description: 标签ID
 *       - in: query
 *         name: tag_title
 *         schema:
 *           type: string
 *         description: 标签名称
 *       - in: query
 *         name: tag_group_id
 *         schema:
 *           type: string
 *         description: 所属标签组ID
 *       - in: query
 *         name: tag_buildin
 *         schema:
 *           type: integer
 *         description: 是否系统内置：1-是，0-否
 *       - in: query
 *         name: tag_enable
 *         schema:
 *           type: integer
 *         description: 是否启用：1-启用，0-禁用
 *       - in: query
 *         name: subsite_id
 *         schema:
 *           type: string
 *         description: 子站点ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: 状态：1-正常，0-禁用
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 关键词搜索
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *         default: 1
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *         description: 每页条数
 *         default: 10
 *       - in: query
 *         name: sortField
 *         schema:
 *           type: string
 *         description: 排序字段
 *         default: tag_sort
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *         description: 排序方向
 *         default: asc
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/', authMiddleware, tagController.getList.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag/group/{groupId}:
 *   get:
 *     summary: 获取特定标签组下的所有标签
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签组ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/group/:groupId', authMiddleware, tagController.getTagsByGroupId.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag/options/enabled:
 *   get:
 *     summary: 获取所有启用的标签（用于下拉选择）
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: subsite_id
 *         schema:
 *           type: string
 *         description: 子站点ID
 *       - in: query
 *         name: tag_group_id
 *         schema:
 *           type: string
 *         description: 标签组ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       500:
 *         description: 服务器错误
 */
router.get('/options/enabled', authMiddleware, tagController.getAllEnabled.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag/user/add:
 *   post:
 *     summary: 批量为用户添加标签
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - tagIds
 *             properties:
 *               userId:
 *                 type: string
 *                 description: 用户ID
 *               tagIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 标签ID数组
 *     responses:
 *       200:
 *         description: 添加成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/user/add', authMiddleware, tagController.addTagsToUser.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag/user/remove:
 *   post:
 *     summary: 批量为用户移除标签
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - tagIds
 *             properties:
 *               userId:
 *                 type: string
 *                 description: 用户ID
 *               tagIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 标签ID数组
 *     responses:
 *       200:
 *         description: 移除成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/user/remove', authMiddleware, tagController.removeTagsFromUser.bind(tagController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag/user/{userId}:
 *   get:
 *     summary: 获取用户的所有标签
 *     tags: [商城会员标签]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: 用户ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/user/:userId', authMiddleware, tagController.getUserTags.bind(tagController));

module.exports = router;
