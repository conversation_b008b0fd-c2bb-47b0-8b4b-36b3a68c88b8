const BaseController = require('../../../../../core/controllers/BaseController');
const MallTagService = require('../services/MallTagService');
const MallTagDto = require('../dto/MallTagDto');
const prismaManager = require('../../../../../core/prisma');

/**
 * 商城会员标签控制器
 */
class MallTagController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    // 使用传入的prisma客户端，如果没有则使用单例模式获取
    const prismaClient = prisma || prismaManager.getClient('base');
    this.tagService = new MallTagService(prismaClient);
  }

  /**
   * 创建商城会员标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async create(req, res) {
    try {
      console.log('创建会员标签请求数据:', req.body);
      
      // 处理前端传递的下划线命名字段
      const formattedData = {
        tag_name: req.body.tag_name || req.body.tag_title || req.body.tagTitle,
        tag_group_id: req.body.tag_group_id || req.body.tagGroupId,
        sort: req.body.sort || req.body.tag_sort || req.body.tagSort,
        tag_buildin: req.body.tag_buildin !== undefined ? parseInt(req.body.tag_buildin) : (req.body.tagBuildin !== undefined ? parseInt(req.body.tagBuildin) : undefined),
        tag_enable: req.body.tag_enable !== undefined ? parseInt(req.body.tag_enable) : (req.body.tagEnable !== undefined ? parseInt(req.body.tagEnable) : undefined),
        subsite_id: req.body.subsite_id || req.body.subsiteId,
        status: req.body.status !== undefined ? parseInt(req.body.status) : undefined,
        remark: req.body.remark
      };
      
      console.log('格式化后的数据:', formattedData);
      
      // 去除未定义的字段
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key] === undefined) {
          delete formattedData[key];
        }
      });
      
      console.log('去除未定义字段后的数据:', formattedData);
      
      // 验证请求数据
      const { error, value } = MallTagDto.validateCreate(formattedData);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      console.log('验证后的数据:', value);
      
      // 添加创建者信息
      value.createdBy = req.user?.id;
      
      // 创建标签
      const tag = await this.tagService.create(value);
      
      return this.success(res, tag, '创建商城会员标签成功');
    } catch (error) {
      console.error('创建商城会员标签失败:', error);
      return this.fail(res, error.message || '创建商城会员标签失败', 500);
    }
  }

  /**
   * 更新商城会员标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async update(req, res) {
    try {
      console.log('更新会员标签请求数据:', req.body);
      
      // 处理前端传递的下划线命名字段
      const formattedData = {
        id: req.params.id,
        tag_name: req.body.tag_name || req.body.tag_title || req.body.tagTitle,
        tag_group_id: req.body.tag_group_id || req.body.tagGroupId,
        sort: req.body.sort || req.body.tag_sort || req.body.tagSort,
        tag_buildin: req.body.tag_buildin !== undefined ? parseInt(req.body.tag_buildin) : (req.body.tagBuildin !== undefined ? parseInt(req.body.tagBuildin) : undefined),
        tag_enable: req.body.tag_enable !== undefined ? parseInt(req.body.tag_enable) : (req.body.tagEnable !== undefined ? parseInt(req.body.tagEnable) : undefined),
        subsite_id: req.body.subsite_id || req.body.subsiteId,
        status: req.body.status !== undefined ? parseInt(req.body.status) : undefined,
        remark: req.body.remark
      };
      
      console.log('格式化后的数据:', formattedData);
      
      // 去除未定义的字段
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key] === undefined) {
          delete formattedData[key];
        }
      });
      
      console.log('去除未定义字段后的数据:', formattedData);
      
      // 验证请求数据
      const { error, value } = MallTagDto.validateUpdate(formattedData);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      console.log('验证后的数据:', value);
      
      // 添加更新者信息
      value.updatedBy = req.user?.id;
      
      // 更新标签
      const tag = await this.tagService.update(req.params.id, value);
      
      return this.success(res, tag, '更新商城会员标签成功');
    } catch (error) {
      console.error('更新商城会员标签失败:', error);
      return this.fail(res, error.message || '更新商城会员标签失败', 500);
    }
  }

  /**
   * 删除商城会员标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async delete(req, res) {
    try {
      const id = req.params.id;
      const updatedBy = req.user?.id;
      
      // 删除标签
      const result = await this.tagService.delete(id, updatedBy);
      
      return this.success(res, result, '删除商城会员标签成功');
    } catch (error) {
      console.error('删除商城会员标签失败:', error);
      return this.fail(res, error.message || '删除商城会员标签失败', 500);
    }
  }

  /**
   * 获取商城会员标签详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getDetail(req, res) {
    try {
      const id = req.params.id;
      
      // 获取标签详情
      const tag = await this.tagService.getDetail(id);
      
      return this.success(res, tag, '获取商城会员标签详情成功');
    } catch (error) {
      console.error('获取商城会员标签详情失败:', error);
      return this.fail(res, error.message || '获取商城会员标签详情失败', 500);
    }
  }

  /**
   * 获取商城会员标签列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getList(req, res) {
    try {
      // 处理查询参数
      const queryParams = {
        id: req.query.id,
        tagTitle: req.query.tag_title || req.query.tagTitle,
        tagGroupId: req.query.tag_group_id || req.query.tagGroupId,
        tagBuildin: req.query.tag_buildin !== undefined ? parseInt(req.query.tag_buildin) : (req.query.tagBuildin !== undefined ? parseInt(req.query.tagBuildin) : undefined),
        tagEnable: req.query.tag_enable !== undefined ? parseInt(req.query.tag_enable) : (req.query.tagEnable !== undefined ? parseInt(req.query.tagEnable) : undefined),
        subsiteId: req.query.subsite_id || req.query.subsiteId,
        status: req.query.status !== undefined ? parseInt(req.query.status) : undefined,
        keyword: req.query.keyword,
        page: req.query.page ? parseInt(req.query.page) : 1,
        pageSize: req.query.pageSize ? parseInt(req.query.pageSize) : 10,
        sortField: req.query.sortField || 'tag_sort',
        sortOrder: req.query.sortOrder || 'asc'
      };
      
      // 验证查询参数
      const { error, value } = MallTagDto.validateQuery(queryParams);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      // 获取标签列表
      const result = await this.tagService.getList(value);
      
      return this.success(res, result, '获取商城会员标签列表成功');
    } catch (error) {
      console.error('获取商城会员标签列表失败:', error);
      return this.fail(res, error.message || '获取商城会员标签列表失败', 500);
    }
  }

  /**
   * 获取特定标签组下的所有标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getTagsByGroupId(req, res) {
    try {
      const groupId = req.params.groupId;
      
      if (!groupId) {
        return this.fail(res, '标签组ID不能为空', 400);
      }
      
      // 获取标签组下的标签
      const tags = await this.tagService.getTagsByGroupId(groupId);
      
      return this.success(res, tags, '获取标签组下的标签成功');
    } catch (error) {
      console.error('获取标签组下的标签失败:', error);
      return this.fail(res, error.message || '获取标签组下的标签失败', 500);
    }
  }

  /**
   * 获取所有启用的标签（用于下拉选择）
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getAllEnabled(req, res) {
    try {
      const queryParams = {
        subsiteId: req.query.subsite_id || req.query.subsiteId,
        tagGroupId: req.query.tag_group_id || req.query.tagGroupId
      };
      
      // 获取所有启用的标签
      const tags = await this.tagService.getAllEnabled(queryParams);
      
      return this.success(res, tags, '获取所有启用的标签成功');
    } catch (error) {
      console.error('获取所有启用的标签失败:', error);
      return this.fail(res, error.message || '获取所有启用的标签失败', 500);
    }
  }

  /**
   * 批量为用户添加标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async addTagsToUser(req, res) {
    try {
      const { userId, tagIds } = req.body;
      
      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }
      
      if (!tagIds || !Array.isArray(tagIds) || tagIds.length === 0) {
        return this.fail(res, '标签ID数组不能为空', 400);
      }
      
      const operatorId = req.user?.id;
      
      // 为用户添加标签
      const result = await this.tagService.addTagsToUser(userId, tagIds, operatorId);
      
      return this.success(res, result, '为用户添加标签成功');
    } catch (error) {
      console.error('为用户添加标签失败:', error);
      return this.fail(res, error.message || '为用户添加标签失败', 500);
    }
  }

  /**
   * 批量为用户移除标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async removeTagsFromUser(req, res) {
    try {
      const { userId, tagIds } = req.body;
      
      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }
      
      if (!tagIds || !Array.isArray(tagIds) || tagIds.length === 0) {
        return this.fail(res, '标签ID数组不能为空', 400);
      }
      
      const operatorId = req.user?.id;
      
      // 为用户移除标签
      const result = await this.tagService.removeTagsFromUser(userId, tagIds, operatorId);
      
      return this.success(res, result, '为用户移除标签成功');
    } catch (error) {
      console.error('为用户移除标签失败:', error);
      return this.fail(res, error.message || '为用户移除标签失败', 500);
    }
  }

  /**
   * 获取用户的所有标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getUserTags(req, res) {
    try {
      const userId = req.params.userId;
      
      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }
      
      // 获取用户的标签
      const tags = await this.tagService.getUserTags(userId);
      
      return this.success(res, tags, '获取用户标签成功');
    } catch (error) {
      console.error('获取用户标签失败:', error);
      return this.fail(res, error.message || '获取用户标签失败', 500);
    }
  }
}

module.exports = MallTagController;
