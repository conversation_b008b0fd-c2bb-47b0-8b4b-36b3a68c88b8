const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');

/**
 * 商城会员标签组服务类
 */
class MallTagGroupService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 创建商城会员标签组
   * @param {Object} tagGroupData 标签组数据
   * @returns {Object} 创建的标签组对象
   */
  async create(tagGroupData) {
    // 检查标签组名称是否已存在
    const existingTagGroup = await this.prisma.MallTagGroup.findFirst({
      where: {
        tag_group_name: tagGroupData.tagGroupName,
        subsite_id: tagGroupData.subsiteId ? BigInt(tagGroupData.subsiteId) : null,
        deleted_at: null
      }
    });

    if (existingTagGroup) {
      throw new Error('标签组名称已存在');
    }

    // 生成ID
    const id = generateSnowflakeId();
    
    // 获取当前时间戳
    const now = BigInt(Date.now());
    
    // 构建创建数据
    const createData = {
      id: BigInt(id),
      tag_group_name: tagGroupData.tagGroupName,
      tag_group_sort: tagGroupData.tagGroupSort || 0,
      tag_group_buildin: tagGroupData.tagGroupBuildin || 0,
      tag_group_enable: tagGroupData.tagGroupEnable || 1,
      subsite_id: tagGroupData.subsiteId ? BigInt(tagGroupData.subsiteId) : null,
      status: tagGroupData.status || 1,
      created_at: now,
      updated_at: now,
      created_by: tagGroupData.createdBy ? BigInt(tagGroupData.createdBy) : null,
      updated_by: tagGroupData.createdBy ? BigInt(tagGroupData.createdBy) : null,
      remark: tagGroupData.remark || null
    };
    
    console.log('创建标签组数据:', createData);
    
    const newTagGroup = await this.prisma.MallTagGroup.create({
      data: createData
    });

    return newTagGroup;
  }

  /**
   * 更新商城会员标签组
   * @param {string} id 标签组ID
   * @param {Object} tagGroupData 更新的标签组数据
   * @returns {Object} 更新后的标签组对象
   */
  async update(id, tagGroupData) {
    // 检查标签组是否存在
    const existingTagGroup = await this.prisma.MallTagGroup.findUnique({
      where: { id: BigInt(id) }
    });

    if (!existingTagGroup) {
      throw new Error('标签组不存在');
    }

    // 如果要更新标签组名称，检查新名称是否已被使用
    if (tagGroupData.tagGroupName && tagGroupData.tagGroupName !== existingTagGroup.tag_group_name) {
      const tagGroupWithSameName = await this.prisma.MallTagGroup.findFirst({
        where: { 
          tag_group_name: tagGroupData.tagGroupName,
          subsite_id: tagGroupData.subsiteId ? BigInt(tagGroupData.subsiteId) : existingTagGroup.subsite_id,
          deleted_at: null,
          id: { not: BigInt(id) }
        }
      });

      if (tagGroupWithSameName) {
        throw new Error('标签组名称已存在');
      }
    }

    // 如果是系统内置标签组，不允许修改tag_group_buildin字段
    if (existingTagGroup.tag_group_buildin === 1 && tagGroupData.tagGroupBuildin === 0) {
      throw new Error('系统内置标签组不允许修改为非系统内置');
    }
    
    // 更新时间戳
    const now = BigInt(Date.now());
    
    // 构建更新数据
    const updateData = {};
    
    console.log('服务层收到的更新数据:', tagGroupData);
    
    // 只更新提供的字段
    if (tagGroupData.tagGroupName !== undefined) {
      console.log('更新标签组名称:', tagGroupData.tagGroupName);
      updateData.tag_group_name = tagGroupData.tagGroupName;
    }
    if (tagGroupData.tagGroupSort !== undefined) updateData.tag_group_sort = tagGroupData.tagGroupSort;
    if (tagGroupData.tagGroupBuildin !== undefined) updateData.tag_group_buildin = tagGroupData.tagGroupBuildin;
    if (tagGroupData.tagGroupEnable !== undefined) updateData.tag_group_enable = tagGroupData.tagGroupEnable;
    if (tagGroupData.subsiteId !== undefined) {
      updateData.subsite_id = tagGroupData.subsiteId ? BigInt(tagGroupData.subsiteId) : null;
    }
    if (tagGroupData.status !== undefined) updateData.status = tagGroupData.status;
    if (tagGroupData.remark !== undefined) updateData.remark = tagGroupData.remark;
    
    // 添加审计字段
    updateData.updated_at = now;
    if (tagGroupData.updatedBy) updateData.updated_by = BigInt(tagGroupData.updatedBy);
    
    console.log('实际更新数据:', updateData);
    
    // 更新标签组
    const updatedTagGroup = await this.prisma.MallTagGroup.update({
      where: { id: BigInt(id) },
      data: updateData
    });

    return updatedTagGroup;
  }

  /**
   * 删除商城会员标签组（软删除）
   * @param {string} id 标签组ID
   * @param {string} updatedBy 操作人ID
   * @returns {Object} 删除结果
   */
  async delete(id, updatedBy) {
    // 检查标签组是否存在
    const existingTagGroup = await this.prisma.MallTagGroup.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!existingTagGroup) {
      throw new Error('标签组不存在或已删除');
    }

    // 检查是否为系统内置标签组
    if (existingTagGroup.tag_group_buildin === 1) {
      throw new Error('系统内置标签组不允许删除');
    }

    // 检查标签组下是否有标签
    const tagsCount = await this.prisma.MallTag.count({
      where: {
        tag_group_id: BigInt(id),
        deleted_at: null
      }
    });

    if (tagsCount > 0) {
      throw new Error('标签组下存在标签，不允许删除');
    }

    // 软删除
    const now = BigInt(Date.now());
    
    const result = await this.prisma.MallTagGroup.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: now,
        updated_at: now,
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });

    return { success: true, message: '删除成功' };
  }

  /**
   * 获取商城会员标签组详情
   * @param {string} id 标签组ID
   * @returns {Object} 标签组详情及其所有标签
   */
  async getDetail(id) {
    // 获取标签组信息，并包含其下的所有标签
    const tagGroup = await this.prisma.MallTagGroup.findUnique({
      where: { 
        id: BigInt(id)
      },
      include: {
        tags: {
          where: {
            deleted_at: null
          },
          orderBy: {
            tag_sort: 'asc'
          }
        }
      }
    });

    if (!tagGroup) {
      throw new Error('标签组不存在或已删除');
    }

    console.log('获取到的标签组及标签数据:', {
      id: tagGroup.id.toString(),
      tag_group_name: tagGroup.tag_group_name,
      tags_count: tagGroup.tags.length
    });

    return tagGroup;
  }

  /**
   * 获取标签组列表
   * @param {Object} queryParams 查询参数
   * @returns {Object} 标签组列表和分页信息
   */
  async getList(queryParams = {}) {
    const {
      id,
      tagGroupName,
      tagGroupBuildin,
      tagGroupEnable,
      subsiteId,
      status,
      keyword,
      page = 1,
      pageSize = 10,
      sortField = 'tag_group_sort',
      sortOrder = 'asc'
    } = queryParams;

    try {
      // 构建查询条件
      const where = {
        deleted_at: null
      };

      if (id) {
        where.id = BigInt(id);
      }

      if (tagGroupName) {
        where.tag_group_name = tagGroupName;
      }

      if (tagGroupBuildin !== undefined) {
        where.tag_group_buildin = tagGroupBuildin;
      }

      if (tagGroupEnable !== undefined) {
        where.tag_group_enable = tagGroupEnable;
      }

      if (subsiteId) {
        where.subsite_id = BigInt(subsiteId);
      }

      if (status !== undefined) {
        where.status = status;
      }

      if (keyword) {
        where.tag_group_name = {
          contains: keyword,
          mode: 'insensitive'
        };
      }

      // 计算总数
      const total = await this.prisma.MallTagGroup.count({ where });

      // 获取分页数据
      const tagGroups = await this.prisma.MallTagGroup.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: {
          [sortField]: sortOrder
        }
      });

      return {
        list: tagGroups,
        pagination: {
          page,
          pageSize,
          total
        }
      };
    } catch (error) {
      console.error('获取标签组列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有有效的标签组（用于下拉选择）
   * @param {Object} queryParams 查询参数
   * @returns {Array} 标签组列表及其所有标签
   */
  async getAllEnabled(queryParams = {}) {
    const { subsiteId } = queryParams;

    try {
      const where = {
        deleted_at: null,
        tag_group_enable: 1,
        status: 1
      };
        
      if (subsiteId) {
        where.subsite_id = BigInt(subsiteId);
      }

      // 获取标签组及其所有标签
      const tagGroups = await this.prisma.MallTagGroup.findMany({
        where,
        orderBy: {
          tag_group_sort: 'asc'
        },
        include: {
          tags: {
            where: {
              deleted_at: null
            },
            orderBy: {
              tag_sort: 'asc'
            }
          }
        }
      });

      console.log('获取到的标签组列表及标签数据:', {
        count: tagGroups.length,
        groups: tagGroups.map(group => ({
          id: group.id.toString(),
          tag_group_name: group.tag_group_name,
          tags_count: group.tags.length
        }))
      });

      return tagGroups;
    } catch (error) {
      console.error('获取启用的标签组列表失败:', error);
      throw error;
    }
  }
}

module.exports = MallTagGroupService;
