/**
 * 商城新闻分类管理控制器类
 */
const BaseController = require('../../../../../core/controllers/BaseController');
const NewsManagementService = require('../services/NewsManagementService');
const NewsArticleService = require('../services/NewsArticleService');

class NewsManagementController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.newsManagementService = new NewsManagementService(prisma);
    this.newsArticleService = new NewsArticleService(prisma);
  }

  /**
   * 创建新闻分类
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async create(req, res) {
    try {
      const userId = req.user?.id || 1; // 如果用户未登录，使用默认ID 1

      const categoryData = req.body;
      // 基本字段验证
      if (!categoryData.name) {
        return this.fail(res, '分类名称不能为空');
      }

      if (categoryData.name.length > 100) {
        return this.fail(res, '分类名称不能超过100个字符');
      }

      const result = await this.newsManagementService.createCategory(categoryData, userId);
      // 只返回id
      this.success(res, { id: result.id }, '创建新闻分类成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取新闻分类详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '分类ID不能为空');
      }

      const category = await this.newsManagementService.getCategoryById(id);
      this.success(res, category);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取新闻分类列表（分页）
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getList(req, res) {
    try {
      // 获取查询参数
      const {
        page = 1,
        pageSize = 10,
        name,
        isEnabled,
        is_enabled,
        parent_id
      } = req.query;
      
      // 打印请求参数以进行调试
      console.log('商城新闻分类请求参数:', req.query);

      const params = {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };

      // 添加可选筛选条件
      if (name) params.name = name;
      
      // 优先使用 is_enabled 参数，如果不存在则使用 isEnabled
      const enabledValue = is_enabled !== undefined ? is_enabled : isEnabled;
      if (enabledValue !== undefined) {
        params.is_enabled = parseInt(enabledValue);
        console.log('状态筛选参数:', params.is_enabled);
      }

      if (parent_id !== undefined) {
        params.parent_id = parent_id;
      }

      const result = await this.newsManagementService.getCategories(params);
      this.success(res, result);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取新闻分类树形结构
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getTree(req, res) {
    try {
      const categoryTree = await this.newsManagementService.getCategoryTree();
      this.success(res, categoryTree);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 更新新闻分类
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async update(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录或登录信息已过期');
      }

      const { id } = req.params;
      const updateData = req.body;

      if (!id) {
        return this.fail(res, '分类ID不能为空');
      }

      // 基本字段验证
      if (updateData.name && updateData.name.length > 100) {
        return this.fail(res, '分类名称不能超过100个字符');
      }

      const result = await this.newsManagementService.updateCategory(id, updateData, userId);
      this.success(res, result, '更新新闻分类成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 删除新闻分类
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async delete(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录或登录信息已过期');
      }

      const { id } = req.params;

      if (!id) {
        return this.fail(res, '分类ID不能为空');
      }

      const result = await this.newsManagementService.deleteCategory(id, userId);
      this.success(res, result, '删除新闻分类成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 切换新闻分类启用状态
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async toggleStatus(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录或登录信息已过期');
      }

      const { id } = req.params;
      const { isEnabled, is_enabled } = req.body;

      if (!id) {
        return this.fail(res, '分类ID不能为空');
      }
      
      // 优先使用 is_enabled 参数
      const enabledValue = is_enabled !== undefined ? is_enabled : isEnabled;
      if (enabledValue === undefined || ![0, 1].includes(Number(enabledValue))) {
        return this.fail(res, '状态参数错误');
      }

      const result = await this.newsManagementService.toggleCategoryStatus(id, enabledValue, userId);
      this.success(res, result, '切换分类状态成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  // ==================== 新闻文章相关方法 ====================

  /**
   * 获取新闻文章列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getArticleList(req, res) {
    try {
      const {
        page = 1,
        pageSize = 10,
        title,
        category_id,
        is_enabled,
        status
      } = req.query;

      const params = {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };

      if (title) params.title = title;
      if (category_id !== undefined) params.category_id = category_id;
      if (is_enabled !== undefined) params.is_enabled = parseInt(is_enabled);
      if (status !== undefined) params.status = parseInt(status);

      const result = await this.newsArticleService.getArticles(params);
      this.success(res, result);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 获取新闻文章详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getArticleById(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, '文章ID不能为空');
      }

      const article = await this.newsArticleService.getArticleById(id);
      this.success(res, article);
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 创建新闻文章
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async createArticle(req, res) {
    try {
      const userId = req.user?.id || 1;
      const articleData = req.body;

      // 基本字段验证
      if (!articleData.title) {
        return this.fail(res, '文章标题不能为空');
      }
      if (!articleData.content) {
        return this.fail(res, '文章内容不能为空');
      }
      if (!articleData.category_id) {
        return this.fail(res, '文章分类不能为空');
      }

      if (articleData.title.length > 200) {
        return this.fail(res, '文章标题不能超过200个字符');
      }

      const result = await this.newsArticleService.createArticle(articleData, userId);
      this.success(res, { id: result.id }, '创建新闻文章成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 更新新闻文章
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async updateArticle(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录或登录信息已过期');
      }

      const { id } = req.params;
      const articleData = req.body;

      if (!id) {
        return this.fail(res, '文章ID不能为空');
      }

      // 基本字段验证
      if (articleData.title && articleData.title.length > 200) {
        return this.fail(res, '文章标题不能超过200个字符');
      }

      const result = await this.newsArticleService.updateArticle(id, articleData, userId);
      this.success(res, result, '更新新闻文章成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 删除新闻文章
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async deleteArticle(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录或登录信息已过期');
      }

      const { id } = req.params;

      if (!id) {
        return this.fail(res, '文章ID不能为空');
      }

      const result = await this.newsArticleService.deleteArticle(id, userId);
      this.success(res, result, '删除新闻文章成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }

  /**
   * 切换新闻文章启用状态
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async toggleArticleStatus(req, res) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录或登录信息已过期');
      }

      const { id } = req.params;
      const { isEnabled, is_enabled } = req.body;

      if (!id) {
        return this.fail(res, '文章ID不能为空');
      }

      // 优先使用 is_enabled 参数
      const enabledValue = is_enabled !== undefined ? is_enabled : isEnabled;
      if (enabledValue === undefined || ![0, 1].includes(Number(enabledValue))) {
        return this.fail(res, '状态参数错误');
      }

      const result = await this.newsArticleService.toggleArticleStatus(id, enabledValue, userId);
      this.success(res, result, '切换文章状态成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }
}

module.exports = NewsManagementController;
