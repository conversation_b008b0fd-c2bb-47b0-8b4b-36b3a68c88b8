/**
 * 商城新闻分类管理路由
 * 定义新闻分类相关的API路由
 */
const express = require('express');
const router = express.Router();
const NewsManagementController = require('../controllers/NewsManagementController');
const RouterConfig = require('../../../../../core/routes/RouterConfig');

/**
 * 创建新闻分类管理路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} Express路由实例
 */
function createNewsManagementRoutes(prisma) {
  // 创建控制器实例
  const newsManagementController = new NewsManagementController(prisma);

  // 创建受JWT保护的路由
  const protectedRouter = RouterConfig.authRoute(router);

  /**
   * @swagger
   * tags:
   *   name: 商城新闻分类管理
   *   description: 商城新闻分类管理接口
   */

  /**
   * @swagger
   * /api/v1/master/mall/news-categories:
   *   get:
   *     tags:
   *       - 商城新闻分类管理
   *     summary: 获取新闻分类列表
   *     description: 获取商城新闻分类列表，支持分页和筛选
   *     parameters:
   *       - name: page
   *         in: query
   *         description: 页码
   *         required: false
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         description: 每页数量
   *         required: false
   *         schema:
   *           type: integer
   *           default: 10
   *       - name: name
   *         in: query
   *         description: 分类名称（模糊搜索）
   *         required: false
   *         schema:
   *           type: string
   *       - name: is_enabled
   *         in: query
   *         description: 启用状态（1-启用，0-禁用）
   *         required: false
   *         schema:
   *           type: integer
   *           enum: [0, 1]
   *       - name: parent_id
   *         in: query
   *         description: 父分类ID
   *         required: false
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 成功获取分类列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 操作成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     items:
   *                       type: array
   *                       items:
   *                         type: object
   *                     pageInfo:
   *                       type: object
   *                       properties:
   *                         page:
   *                           type: integer
   *                         pageSize:
   *                           type: integer
   *                         total:
   *                           type: integer
   *                         totalPages:
   *                           type: integer
   */
  router.get('/', newsManagementController.getList.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-categories/tree:
   *   get:
   *     tags:
   *       - 商城新闻分类管理
   *     summary: 获取新闻分类树形结构
   *     description: 获取商城新闻分类的树形结构数据
   *     responses:
   *       200:
   *         description: 成功获取分类树形结构
   */
  router.get('/tree', newsManagementController.getTree.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-categories/{id}:
   *   get:
   *     tags:
   *       - 商城新闻分类管理
   *     summary: 获取新闻分类详情
   *     description: 根据ID获取单个新闻分类的详细信息
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 新闻分类ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 成功获取分类详情
   */
  router.get('/:id', newsManagementController.getById.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-categories:
   *   post:
   *     tags:
   *       - 商城新闻分类管理
   *     summary: 创建新闻分类
   *     description: 创建新的商城新闻分类
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *             properties:
   *               name:
   *                 type: string
   *                 description: 分类名称
   *                 maxLength: 100
   *               description:
   *                 type: string
   *                 description: 分类描述
   *               parent_id:
   *                 type: integer
   *                 description: 父分类ID
   *               sort_order:
   *                 type: integer
   *                 description: 排序权重
   *               image_url:
   *                 type: string
   *                 description: 分类图片URL
   *               is_enabled:
   *                 type: integer
   *                 enum: [0, 1]
   *                 description: 启用状态
   *     responses:
   *       200:
   *         description: 创建成功
   */
  protectedRouter.post('/', newsManagementController.create.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-categories/{id}:
   *   put:
   *     tags:
   *       - 商城新闻分类管理
   *     summary: 更新新闻分类
   *     description: 更新指定ID的新闻分类信息
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 新闻分类ID
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: 分类名称
   *               description:
   *                 type: string
   *                 description: 分类描述
   *               parent_id:
   *                 type: integer
   *                 description: 父分类ID
   *               sort_order:
   *                 type: integer
   *                 description: 排序权重
   *               image_url:
   *                 type: string
   *                 description: 分类图片URL
   *               is_enabled:
   *                 type: integer
   *                 enum: [0, 1]
   *                 description: 启用状态
   *     responses:
   *       200:
   *         description: 更新成功
   */
  protectedRouter.put('/:id', newsManagementController.update.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-categories/{id}:
   *   delete:
   *     tags:
   *       - 商城新闻分类管理
   *     summary: 删除新闻分类
   *     description: 删除指定ID的新闻分类（软删除）
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 新闻分类ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 删除成功
   */
  protectedRouter.delete('/:id', newsManagementController.delete.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-categories/{id}/status:
   *   put:
   *     tags:
   *       - 商城新闻分类管理
   *     summary: 切换新闻分类状态
   *     description: 切换指定ID的新闻分类启用/禁用状态
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 新闻分类ID
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - is_enabled
   *             properties:
   *               is_enabled:
   *                 type: integer
   *                 enum: [0, 1]
   *                 description: 启用状态（1-启用，0-禁用）
   *     responses:
   *       200:
   *         description: 状态切换成功
   */
  protectedRouter.put('/:id/status', newsManagementController.toggleStatus.bind(newsManagementController));

  return router;
}

/**
 * 创建新闻文章管理路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} Express路由实例
 */
function createNewsArticleRoutes(prisma) {
  const router = express.Router();
  const newsManagementController = new NewsManagementController(prisma);
  const protectedRouter = RouterConfig.authRoute(router);

  /**
   * @swagger
   * tags:
   *   name: 商城新闻文章管理
   *   description: 商城新闻文章管理接口
   */

  /**
   * @swagger
   * /api/v1/master/mall/news-articles:
   *   get:
   *     tags:
   *       - 商城新闻文章管理
   *     summary: 获取新闻文章列表
   *     description: 获取商城新闻文章列表，支持分页和筛选
   *     parameters:
   *       - name: page
   *         in: query
   *         description: 页码
   *         required: false
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         description: 每页数量
   *         required: false
   *         schema:
   *           type: integer
   *           default: 10
   *       - name: title
   *         in: query
   *         description: 文章标题（模糊搜索）
   *         required: false
   *         schema:
   *           type: string
   *       - name: category_id
   *         in: query
   *         description: 分类ID
   *         required: false
   *         schema:
   *           type: integer
   *       - name: is_enabled
   *         in: query
   *         description: 启用状态（1-启用，0-禁用）
   *         required: false
   *         schema:
   *           type: integer
   *           enum: [0, 1]
   *       - name: status
   *         in: query
   *         description: 文章状态（1-正常，0-隐藏，-1-删除）
   *         required: false
   *         schema:
   *           type: integer
   *           enum: [-1, 0, 1]
   *     responses:
   *       200:
   *         description: 成功获取文章列表
   */
  router.get('/', newsManagementController.getArticleList.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-articles/{id}:
   *   get:
   *     tags:
   *       - 商城新闻文章管理
   *     summary: 获取新闻文章详情
   *     description: 根据ID获取单个新闻文章的详细信息
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 新闻文章ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 成功获取文章详情
   */
  router.get('/:id', newsManagementController.getArticleById.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-articles:
   *   post:
   *     tags:
   *       - 商城新闻文章管理
   *     summary: 创建新闻文章
   *     description: 创建新的新闻文章
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - title
   *               - content
   *               - category_id
   *             properties:
   *               title:
   *                 type: string
   *                 description: 文章标题
   *               summary:
   *                 type: string
   *                 description: 文章摘要
   *               content:
   *                 type: string
   *                 description: 文章内容
   *               category_id:
   *                 type: integer
   *                 description: 分类ID
   *               image_url:
   *                 type: string
   *                 description: 封面图片URL
   *               sort_order:
   *                 type: integer
   *                 description: 排序权重
   *               is_enabled:
   *                 type: integer
   *                 enum: [0, 1]
   *                 description: 启用状态
   *               status:
   *                 type: integer
   *                 enum: [-1, 0, 1]
   *                 description: 文章状态
   *               seo_title:
   *                 type: string
   *                 description: SEO标题
   *               seo_keywords:
   *                 type: string
   *                 description: SEO关键词
   *               seo_description:
   *                 type: string
   *                 description: SEO描述
   *     responses:
   *       200:
   *         description: 创建成功
   */
  protectedRouter.post('/', newsManagementController.createArticle.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-articles/{id}:
   *   put:
   *     tags:
   *       - 商城新闻文章管理
   *     summary: 更新新闻文章
   *     description: 更新指定ID的新闻文章信息
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 新闻文章ID
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *                 description: 文章标题
   *               summary:
   *                 type: string
   *                 description: 文章摘要
   *               content:
   *                 type: string
   *                 description: 文章内容
   *               category_id:
   *                 type: integer
   *                 description: 分类ID
   *               image_url:
   *                 type: string
   *                 description: 封面图片URL
   *               sort_order:
   *                 type: integer
   *                 description: 排序权重
   *               is_enabled:
   *                 type: integer
   *                 enum: [0, 1]
   *                 description: 启用状态
   *               status:
   *                 type: integer
   *                 enum: [-1, 0, 1]
   *                 description: 文章状态
   *               seo_title:
   *                 type: string
   *                 description: SEO标题
   *               seo_keywords:
   *                 type: string
   *                 description: SEO关键词
   *               seo_description:
   *                 type: string
   *                 description: SEO描述
   *     responses:
   *       200:
   *         description: 更新成功
   */
  protectedRouter.put('/:id', newsManagementController.updateArticle.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-articles/{id}:
   *   delete:
   *     tags:
   *       - 商城新闻文章管理
   *     summary: 删除新闻文章
   *     description: 删除指定ID的新闻文章（软删除）
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 新闻文章ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 删除成功
   */
  protectedRouter.delete('/:id', newsManagementController.deleteArticle.bind(newsManagementController));

  /**
   * @swagger
   * /api/v1/master/mall/news-articles/{id}/status:
   *   put:
   *     tags:
   *       - 商城新闻文章管理
   *     summary: 切换新闻文章状态
   *     description: 切换指定ID的新闻文章启用/禁用状态
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 新闻文章ID
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - is_enabled
   *             properties:
   *               is_enabled:
   *                 type: integer
   *                 enum: [0, 1]
   *                 description: 启用状态（1-启用，0-禁用）
   *     responses:
   *       200:
   *         description: 状态切换成功
   */
  protectedRouter.put('/:id/status', newsManagementController.toggleArticleStatus.bind(newsManagementController));

  return router;
}

module.exports = {
  createNewsManagementRoutes,
  createNewsArticleRoutes
};
