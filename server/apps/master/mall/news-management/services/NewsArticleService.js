/**
 * 商城新闻文章服务类
 */
const { Prisma } = require('@prisma/client');

class NewsArticleService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取新闻文章列表（分页）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getArticles(params = {}) {
    try {
      const {
        page = 1,
        pageSize = 10,
        title,
        category_id,
        is_enabled,
        status
      } = params;

      // 构建基础WHERE条件
      let whereClause = 'WHERE a.deleted_at IS NULL';
      const queryParams = [];

      // 动态添加查询条件
      if (title) {
        whereClause += ` AND a.title ILIKE '%${title}%'`;
      }

      if (category_id !== undefined && category_id !== '') {
        whereClause += ` AND a.category_id = ${BigInt(category_id)}`;
      }

      if (is_enabled !== undefined && is_enabled !== '') {
        whereClause += ` AND a.is_enabled = ${parseInt(is_enabled)}`;
      }

      if (status !== undefined && status !== '') {
        whereClause += ` AND a.status = ${parseInt(status)}`;
      }

      // 查询总记录数
      const countSql = `
        SELECT COUNT(*) as total
        FROM base.mall_news_articles a
        ${whereClause}
      `;

      const countResult = await this.prisma.$queryRaw(Prisma.raw(countSql));
      const total = Number(countResult[0]?.total || 0);

      // 计算分页参数
      const offset = (page - 1) * pageSize;

      // 查询数据
      const dataSql = `
        SELECT a.*, c.name as category_name
        FROM base.mall_news_articles a
        LEFT JOIN base.mall_news_categories c ON a.category_id = c.id
        ${whereClause}
        ORDER BY a.updated_at DESC, a.sort_order DESC, a.created_at DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `;

      const list = await this.prisma.$queryRaw(Prisma.raw(dataSql));

      // 计算分页信息
      const totalPages = Math.ceil(total / pageSize);

      return {
        items: list.map(item => ({
          ...item,
          id: item.id.toString(),
          category_id: item.category_id ? item.category_id.toString() : null,
          created_at: item.created_at.toString(),
          updated_at: item.updated_at.toString(),
          deleted_at: item.deleted_at ? item.deleted_at.toString() : null,
          created_by: item.created_by.toString(),
          updated_by: item.updated_by.toString()
        })),
        pageInfo: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          totalPages,
          current: parseInt(page)
        }
      };
    } catch (error) {
      console.error('获取新闻文章列表失败:', error);
      throw new Error(`获取新闻文章列表失败: ${error.message}`);
    }
  }

  /**
   * 根据ID获取新闻文章详情
   * @param {string|number} id 文章ID
   * @returns {Promise<Object>} 文章详情
   */
  async getArticleById(id) {
    try {
      const result = await this.prisma.$queryRaw`
        SELECT a.*, c.name as category_name 
        FROM base.mall_news_articles a
        LEFT JOIN base.mall_news_categories c ON a.category_id = c.id
        WHERE a.id = ${BigInt(id)} AND a.deleted_at IS NULL
        LIMIT 1
      `;
      
      if (!result || result.length === 0) {
        throw new Error('新闻文章不存在');
      }

      const article = result[0];
      return {
        ...article,
        id: article.id.toString(),
        category_id: article.category_id.toString(),
        created_at: article.created_at.toString(),
        updated_at: article.updated_at.toString(),
        deleted_at: article.deleted_at ? article.deleted_at.toString() : null,
        created_by: article.created_by.toString(),
        updated_by: article.updated_by.toString()
      };
    } catch (error) {
      console.error('获取新闻文章详情失败:', error);
      throw new Error(`获取新闻文章详情失败: ${error.message}`);
    }
  }

  /**
   * 创建新闻文章
   * @param {Object} data 文章数据
   * @param {number} userId 创建者ID
   * @returns {Promise<Object>} 创建结果
   */
  async createArticle(data, userId) {
    try {
      // 验证分类是否存在
      if (data.category_id) {
        const category = await this.prisma.$queryRaw`
          SELECT id FROM base.mall_news_categories 
          WHERE id = ${BigInt(data.category_id)} AND deleted_at IS NULL
        `;
        if (!category || category.length === 0) {
          throw new Error('新闻分类不存在');
        }
      }

      const now = BigInt(Date.now());
      const insertData = {
        title: data.title,
        summary: data.summary || null,
        content: data.content,
        category_id: BigInt(data.category_id),
        image_url: data.image_url || null,
        sort_order: data.sort_order || 0,
        is_enabled: data.is_enabled !== undefined ? data.is_enabled : 1,
        status: data.status !== undefined ? data.status : 1,
        seo_title: data.seo_title || null,
        seo_keywords: data.seo_keywords || null,
        seo_description: data.seo_description || null,
        view_count: 0,
        created_at: now,
        updated_at: now,
        created_by: BigInt(userId),
        updated_by: BigInt(userId)
      };

      const result = await this.prisma.$queryRaw`
        INSERT INTO base.mall_news_articles (
          title, summary, content, category_id, image_url, sort_order, is_enabled, 
          status, seo_title, seo_keywords, seo_description, view_count,
          created_at, updated_at, created_by, updated_by
        )
        VALUES (
          ${insertData.title}, ${insertData.summary}, ${insertData.content}, 
          ${insertData.category_id}, ${insertData.image_url}, ${insertData.sort_order}, 
          ${insertData.is_enabled}, ${insertData.status}, ${insertData.seo_title}, 
          ${insertData.seo_keywords}, ${insertData.seo_description}, ${insertData.view_count},
          ${insertData.created_at}, ${insertData.updated_at}, 
          ${insertData.created_by}, ${insertData.updated_by}
        )
        RETURNING id
      `;
      
      return { id: result[0].id.toString() };
    } catch (error) {
      console.error('创建新闻文章失败:', error);
      throw new Error(`创建新闻文章失败: ${error.message}`);
    }
  }

  /**
   * 更新新闻文章
   * @param {string|number} id 文章ID
   * @param {Object} data 更新数据
   * @param {number} userId 更新者ID
   * @returns {Promise<Object>} 更新结果
   */
  async updateArticle(id, data, userId) {
    try {
      // 检查文章是否存在
      const existingArticle = await this.prisma.$queryRaw`
        SELECT id FROM base.mall_news_articles
        WHERE id = ${BigInt(id)} AND deleted_at IS NULL
      `;
      if (!existingArticle || existingArticle.length === 0) {
        throw new Error('新闻文章不存在');
      }

      // 验证分类是否存在
      if (data.category_id) {
        const category = await this.prisma.$queryRaw`
          SELECT id FROM base.mall_news_categories
          WHERE id = ${BigInt(data.category_id)} AND deleted_at IS NULL
        `;
        if (!category || category.length === 0) {
          throw new Error('新闻分类不存在');
        }
      }

      const now = BigInt(Date.now());
      const updateFields = [];

      if (data.title !== undefined) {
        updateFields.push(`title = '${data.title.replace(/'/g, "''")}'`);
      }
      if (data.summary !== undefined) {
        updateFields.push(`summary = ${data.summary ? `'${data.summary.replace(/'/g, "''")}'` : 'NULL'}`);
      }
      if (data.content !== undefined) {
        updateFields.push(`content = '${data.content.replace(/'/g, "''")}'`);
      }
      if (data.category_id !== undefined) {
        updateFields.push(`category_id = ${BigInt(data.category_id)}`);
      }
      if (data.image_url !== undefined) {
        updateFields.push(`image_url = ${data.image_url ? `'${data.image_url.replace(/'/g, "''")}'` : 'NULL'}`);
      }
      if (data.sort_order !== undefined) {
        updateFields.push(`sort_order = ${data.sort_order}`);
      }
      if (data.is_enabled !== undefined) {
        updateFields.push(`is_enabled = ${data.is_enabled}`);
      }
      if (data.status !== undefined) {
        updateFields.push(`status = ${data.status}`);
      }
      if (data.seo_title !== undefined) {
        updateFields.push(`seo_title = ${data.seo_title ? `'${data.seo_title.replace(/'/g, "''")}'` : 'NULL'}`);
      }
      if (data.seo_keywords !== undefined) {
        updateFields.push(`seo_keywords = ${data.seo_keywords ? `'${data.seo_keywords.replace(/'/g, "''")}'` : 'NULL'}`);
      }
      if (data.seo_description !== undefined) {
        updateFields.push(`seo_description = ${data.seo_description ? `'${data.seo_description.replace(/'/g, "''")}'` : 'NULL'}`);
      }

      updateFields.push(`updated_at = ${now}`);
      updateFields.push(`updated_by = ${BigInt(userId)}`);

      const updateSql = `
        UPDATE base.mall_news_articles
        SET ${updateFields.join(', ')}
        WHERE id = ${BigInt(id)} AND deleted_at IS NULL
        RETURNING id
      `;

      const result = await this.prisma.$queryRaw(Prisma.raw(updateSql));

      if (!result || result.length === 0) {
        throw new Error('更新失败，文章不存在');
      }

      return { id: result[0].id.toString() };
    } catch (error) {
      console.error('更新新闻文章失败:', error);
      throw new Error(`更新新闻文章失败: ${error.message}`);
    }
  }

  /**
   * 删除新闻文章（软删除）
   * @param {string|number} id 文章ID
   * @param {number} userId 删除者ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteArticle(id, userId) {
    try {
      // 检查文章是否存在
      const existingArticle = await this.prisma.$queryRaw`
        SELECT id FROM base.mall_news_articles
        WHERE id = ${BigInt(id)} AND deleted_at IS NULL
      `;
      if (!existingArticle || existingArticle.length === 0) {
        throw new Error('新闻文章不存在');
      }

      const now = BigInt(Date.now());
      const result = await this.prisma.$queryRaw`
        UPDATE base.mall_news_articles
        SET deleted_at = ${now}, updated_at = ${now}, updated_by = ${BigInt(userId)}
        WHERE id = ${BigInt(id)} AND deleted_at IS NULL
        RETURNING id
      `;

      if (!result || result.length === 0) {
        throw new Error('删除失败，文章不存在');
      }

      return { id: result[0].id.toString() };
    } catch (error) {
      console.error('删除新闻文章失败:', error);
      throw new Error(`删除新闻文章失败: ${error.message}`);
    }
  }

  /**
   * 切换新闻文章启用状态
   * @param {string|number} id 文章ID
   * @param {number} isEnabled 启用状态（1-启用，0-禁用）
   * @param {number} userId 操作者ID
   * @returns {Promise<Object>} 操作结果
   */
  async toggleArticleStatus(id, isEnabled, userId) {
    try {
      // 检查文章是否存在
      const existingArticle = await this.prisma.$queryRaw`
        SELECT id FROM base.mall_news_articles
        WHERE id = ${BigInt(id)} AND deleted_at IS NULL
      `;
      if (!existingArticle || existingArticle.length === 0) {
        throw new Error('新闻文章不存在');
      }

      const now = BigInt(Date.now());
      const result = await this.prisma.$queryRaw`
        UPDATE base.mall_news_articles
        SET is_enabled = ${parseInt(isEnabled)}, updated_at = ${now}, updated_by = ${BigInt(userId)}
        WHERE id = ${BigInt(id)} AND deleted_at IS NULL
        RETURNING id, is_enabled
      `;

      if (!result || result.length === 0) {
        throw new Error('状态切换失败，文章不存在');
      }

      return {
        id: result[0].id.toString(),
        is_enabled: result[0].is_enabled
      };
    } catch (error) {
      console.error('切换新闻文章状态失败:', error);
      throw new Error(`切换新闻文章状态失败: ${error.message}`);
    }
  }

  /**
   * 增加文章浏览次数
   * @param {string|number} id 文章ID
   * @returns {Promise<Object>} 操作结果
   */
  async incrementViewCount(id) {
    try {
      const result = await this.prisma.$queryRaw`
        UPDATE base.mall_news_articles
        SET view_count = view_count + 1
        WHERE id = ${BigInt(id)} AND deleted_at IS NULL
        RETURNING id, view_count
      `;

      if (!result || result.length === 0) {
        throw new Error('文章不存在');
      }

      return {
        id: result[0].id.toString(),
        view_count: result[0].view_count
      };
    } catch (error) {
      console.error('增加浏览次数失败:', error);
      throw new Error(`增加浏览次数失败: ${error.message}`);
    }
  }
}

module.exports = NewsArticleService;
