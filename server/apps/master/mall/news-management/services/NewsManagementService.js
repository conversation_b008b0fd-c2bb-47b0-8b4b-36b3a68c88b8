/**
 * 商城新闻分类管理服务类
 */

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (!data) return data;

  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }

  if (typeof data === 'object' && data !== null) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];
        if (typeof value === 'bigint') {
          result[key] = value.toString();
        } else if (typeof value === 'object' && value !== null) {
          result[key] = handleBigInt(value);
        } else {
          result[key] = value;
        }
      }
    }
    return result;
  }

  return data;
}

class NewsManagementService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 创建新闻分类
   * @param {Object} data 分类数据
   * @param {number} userId 创建者ID
   * @returns {Promise<Object>} 创建结果
   */
  async createCategory(data, userId) {
    try {
      // 检查父分类是否存在
      if (data.parent_id) {
        const parentCategory = await this.prisma.mall_news_categories.findUnique({
          where: { id: BigInt(data.parent_id) }
        });
        if (!parentCategory) {
          throw new Error('父分类不存在');
        }
        
        // 设置层级
        data.level = (parentCategory.level || 0) + 1;
      } else {
        // 顶级分类
        data.level = 1;
        data.parent_id = null;
      }

      // 处理数据类型转换
      const createData = {
        name: data.name,
        description: data.description || null,
        parent_id: data.parent_id ? BigInt(data.parent_id) : null,
        level: data.level,
        sort_order: data.sort_order || 0,
        image_url: data.image_url || null,
        is_enabled: data.is_enabled !== undefined ? parseInt(data.is_enabled) : 1,
        status: data.status !== undefined ? parseInt(data.status) : 1,
        seo_title: data.seo_title || null,
        seo_keywords: data.seo_keywords || null,
        seo_description: data.seo_description || null,
        created_by: userId ? BigInt(userId) : null,
        updated_by: userId ? BigInt(userId) : null
      };

      const result = await this.prisma.mall_news_categories.create({
        data: createData
      });

      return handleBigInt(result);
    } catch (error) {
      throw new Error(`创建新闻分类失败: ${error.message}`);
    }
  }

  /**
   * 获取单个新闻分类详情
   * @param {number} id 分类ID
   * @returns {Promise<Object>} 分类信息
   */
  async getCategoryById(id) {
    try {
      const category = await this.prisma.mall_news_categories.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        include: {
          parent: true,
          children: {
            where: { deleted_at: null },
            orderBy: { sort_order: 'desc' }
          }
        }
      });

      if (!category) {
        throw new Error('新闻分类不存在');
      }

      return handleBigInt(category);
    } catch (error) {
      throw new Error(`获取新闻分类详情失败: ${error.message}`);
    }
  }

  /**
   * 获取所有新闻分类列表（支持分页、筛选）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分类分页数据
   */
  async getCategories(params = {}) {
    try {
      const {
        page = 1,
        pageSize = 10,
        name,
        is_enabled,
        parent_id
      } = params;

      // 构建查询条件
      const where = {
        deleted_at: null
      };

      if (name) {
        where.name = {
          contains: name
        };
      }

      if (is_enabled !== undefined) {
        where.is_enabled = parseInt(is_enabled);
      }

      if (parent_id !== undefined) {
        where.parent_id = parent_id ? BigInt(parent_id) : null;
      }

      // 计算分页
      const skip = (parseInt(page) - 1) * parseInt(pageSize);
      const take = parseInt(pageSize);

      // 查询数据
      const [list, total] = await Promise.all([
        this.prisma.mall_news_categories.findMany({
          where,
          skip,
          take,
          orderBy: [
            { sort_order: 'desc' },
            { created_at: 'desc' }
          ],
          include: {
            parent: true,
            children: {
              where: { deleted_at: null },
              orderBy: { sort_order: 'desc' }
            }
          }
        }),
        this.prisma.mall_news_categories.count({ where })
      ]);

      return {
        items: handleBigInt(list),
        pageInfo: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          totalPages: Math.ceil(total / parseInt(pageSize))
        }
      };
    } catch (error) {
      throw new Error(`获取新闻分类列表失败: ${error.message}`);
    }
  }

  /**
   * 获取新闻分类树形结构
   * @returns {Promise<Array>} 分类树形结构
   */
  async getCategoryTree() {
    try {
      const categories = await this.prisma.mall_news_categories.findMany({
        where: {
          deleted_at: null,
          is_enabled: 1
        },
        orderBy: [
          { level: 'asc' },
          { sort_order: 'desc' },
          { created_at: 'asc' }
        ]
      });

      const processedCategories = handleBigInt(categories);
      
      // 构建树形结构
      const categoryMap = {};
      const rootCategories = [];
      
      // 先将所有分类放入map
      processedCategories.forEach(category => {
        categoryMap[category.id] = {
          ...category,
          children: []
        };
      });
      
      // 构建父子关系
      processedCategories.forEach(category => {
        if (category.parent_id) {
          const parent = categoryMap[category.parent_id];
          if (parent) {
            parent.children.push(categoryMap[category.id]);
          }
        } else {
          rootCategories.push(categoryMap[category.id]);
        }
      });
      
      return rootCategories;
    } catch (error) {
      throw new Error(`获取新闻分类树形结构失败: ${error.message}`);
    }
  }

  /**
   * 更新新闻分类
   * @param {number} id 分类ID
   * @param {Object} data 更新数据
   * @param {number} userId 更新人ID
   * @returns {Promise<Object>} 更新结果
   */
  async updateCategory(id, data, userId) {
    try {
      // 检查分类是否存在
      const existingCategory = await this.prisma.mall_news_categories.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!existingCategory) {
        throw new Error('新闻分类不存在');
      }
      
      // 检查是否修改了父分类
      if (data.parent_id !== undefined) {
        // 不能将分类设为自己的子分类
        if (data.parent_id && BigInt(data.parent_id) === BigInt(id)) {
          throw new Error('不能将分类设为自己的子分类');
        }
        
        // 检查父分类是否存在
        if (data.parent_id) {
          const parentCategory = await this.prisma.mall_news_categories.findUnique({
            where: { id: BigInt(data.parent_id) }
          });
          if (!parentCategory) {
            throw new Error('父分类不存在');
          }
          
          // 设置层级
          data.level = (parentCategory.level || 0) + 1;
        } else {
          // 顶级分类
          data.level = 1;
        }
      }

      // 处理数据类型转换
      const updateData = {
        updated_by: userId ? BigInt(userId) : null
      };

      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.parent_id !== undefined) updateData.parent_id = data.parent_id ? BigInt(data.parent_id) : null;
      if (data.level !== undefined) updateData.level = data.level;
      if (data.sort_order !== undefined) updateData.sort_order = data.sort_order;
      if (data.image_url !== undefined) updateData.image_url = data.image_url;
      if (data.is_enabled !== undefined) updateData.is_enabled = parseInt(data.is_enabled);
      if (data.status !== undefined) updateData.status = parseInt(data.status);
      if (data.seo_title !== undefined) updateData.seo_title = data.seo_title;
      if (data.seo_keywords !== undefined) updateData.seo_keywords = data.seo_keywords;
      if (data.seo_description !== undefined) updateData.seo_description = data.seo_description;

      const result = await this.prisma.mall_news_categories.update({
        where: { id: BigInt(id) },
        data: updateData
      });

      return handleBigInt(result);
    } catch (error) {
      throw new Error(`更新新闻分类失败: ${error.message}`);
    }
  }

  /**
   * 删除新闻分类（软删除）
   * @param {number} id 分类ID
   * @param {number} userId 删除人ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteCategory(id, userId) {
    try {
      // 检查分类是否存在
      const existingCategory = await this.prisma.mall_news_categories.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        include: {
          children: {
            where: { deleted_at: null }
          }
        }
      });

      if (!existingCategory) {
        throw new Error('新闻分类不存在');
      }

      // 检查是否有子分类
      if (existingCategory.children && existingCategory.children.length > 0) {
        throw new Error('该分类下还有子分类，无法删除');
      }

      // 软删除
      const result = await this.prisma.mall_news_categories.update({
        where: { id: BigInt(id) },
        data: {
          deleted_at: BigInt(Date.now()),
          updated_by: userId ? BigInt(userId) : null
        }
      });

      return handleBigInt(result);
    } catch (error) {
      throw new Error(`删除新闻分类失败: ${error.message}`);
    }
  }

  /**
   * 切换新闻分类启用状态
   * @param {number} id 分类ID
   * @param {number} isEnabled 启用状态
   * @param {number} userId 操作人ID
   * @returns {Promise<Object>} 更新结果
   */
  async toggleCategoryStatus(id, isEnabled, userId) {
    try {
      const result = await this.prisma.mall_news_categories.update({
        where: {
          id: BigInt(id)
        },
        data: {
          is_enabled: parseInt(isEnabled),
          updated_by: userId ? BigInt(userId) : null
        }
      });

      return handleBigInt(result);
    } catch (error) {
      throw new Error(`切换分类状态失败: ${error.message}`);
    }
  }
}

module.exports = NewsManagementService;
