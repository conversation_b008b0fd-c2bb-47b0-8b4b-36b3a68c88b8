{"openapi": "3.0.0", "info": {"title": "商城用户管理 API", "description": "商城用户管理相关接口", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000", "description": "本地开发服务器"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "parameters": {"IdParam": {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "资源ID"}, "PageParam": {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "页码，默认为1"}, "PageSizeParam": {"name": "pageSize", "in": "query", "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}, "description": "每页记录数，默认为10"}}, "schemas": {"MallUser": {"type": "object", "properties": {"id": {"type": "string", "description": "用户ID，16位雪花算法"}, "username": {"type": "string", "description": "用户名，唯一"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatar": {"type": "string", "description": "头像地址"}, "phone": {"type": "string", "description": "手机号"}, "email": {"type": "string", "description": "邮箱"}, "status": {"type": "integer", "enum": [0, 1], "description": "状态：1-正常，0-禁用"}, "last_login_ip": {"type": "string", "description": "最后登录IP"}, "last_login_time": {"type": "string", "description": "最后登录时间戳（毫秒）"}, "login_count": {"type": "integer", "description": "登录次数"}, "created_at": {"type": "string", "description": "创建时间戳（毫秒）"}, "updated_at": {"type": "string", "description": "更新时间戳（毫秒）"}, "remark": {"type": "string", "description": "备注信息"}}}, "MallUserCreateRequest": {"type": "object", "required": ["username", "password"], "properties": {"username": {"type": "string", "description": "用户名，必填，唯一"}, "password": {"type": "string", "description": "密码，必填"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatar": {"type": "string", "description": "头像地址"}, "phone": {"type": "string", "description": "手机号"}, "email": {"type": "string", "description": "邮箱"}, "status": {"type": "integer", "enum": [0, 1], "default": 1, "description": "状态：1-正常，0-禁用"}, "remark": {"type": "string", "description": "备注信息"}}}, "MallUserUpdateRequest": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatar": {"type": "string", "description": "头像地址"}, "phone": {"type": "string", "description": "手机号"}, "email": {"type": "string", "description": "邮箱"}, "status": {"type": "integer", "enum": [0, 1], "description": "状态：1-正常，0-禁用"}, "remark": {"type": "string", "description": "备注信息"}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "message": {"type": "string", "description": "错误信息"}}}}, "responses": {"MallUserResponse": {"description": "商城用户详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/MallUser"}}}}}}, "MallUserListResponse": {"description": "商城用户列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "example": 200}, "message": {"type": "string", "description": "提示信息", "example": "操作成功"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/MallUser"}}, "total": {"type": "integer", "description": "总记录数", "example": 100}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "pageSize": {"type": "integer", "description": "每页记录数", "example": 10}}}}}}}}, "Error": {"description": "错误响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "paths": {"/api/v1/master/mall/user": {"get": {"tags": ["商城管理/用户管理"], "summary": "获取商城用户列表", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/PageSizeParam"}, {"name": "username", "in": "query", "schema": {"type": "string"}, "description": "用户名（模糊查询）"}, {"name": "nickname", "in": "query", "schema": {"type": "string"}, "description": "昵称（模糊查询）"}, {"name": "email", "in": "query", "schema": {"type": "string"}, "description": "邮箱（模糊查询）"}, {"name": "phone", "in": "query", "schema": {"type": "string"}, "description": "手机号（模糊查询）"}, {"name": "status", "in": "query", "schema": {"type": "integer", "enum": [0, 1]}, "description": "状态：1-正常，0-禁用"}, {"name": "keyword", "in": "query", "schema": {"type": "string"}, "description": "关键字（模糊查询用户名、昵称、邮箱、手机号）"}], "responses": {"200": {"$ref": "#/components/responses/MallUserListResponse"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}}}, "post": {"tags": ["商城管理/用户管理"], "summary": "创建商城用户", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallUserCreateRequest"}}}}, "responses": {"201": {"$ref": "#/components/responses/MallUserResponse"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}}}}, "/api/v1/master/mall/user/{id}": {"get": {"tags": ["商城管理/用户管理"], "summary": "获取商城用户详情", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/IdParam"}], "responses": {"200": {"$ref": "#/components/responses/MallUserResponse"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}, "404": {"description": "用户不存在"}}}, "put": {"tags": ["商城管理/用户管理"], "summary": "更新商城用户", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/IdParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallUserUpdateRequest"}}}}, "responses": {"200": {"$ref": "#/components/responses/MallUserResponse"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}, "404": {"description": "用户不存在"}}}, "delete": {"tags": ["商城管理/用户管理"], "summary": "删除商城用户", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/IdParam"}], "responses": {"204": {"description": "删除成功"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}, "404": {"description": "用户不存在"}}}}}}