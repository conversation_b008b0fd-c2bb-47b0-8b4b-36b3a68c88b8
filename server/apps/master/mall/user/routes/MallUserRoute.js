const express = require('express');
const router = express.Router();
const MallUserController = require('../controllers/MallUserController');
const { prisma } = require('../../../../../core/database/prisma');
const RouterConfig = require('../../../../../core/routes/RouterConfig');

// 获取base schema的prisma客户端
const controller = new MallUserController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/master/mall/user:
 *   get:
 *     tags: [商城管理/用户管理]
 *     summary: 获取商城用户列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallUserListResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.get('/', controller.list.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user:
 *   post:
 *     tags: [商城管理/用户管理]
 *     summary: 创建商城用户
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MallUserCreateRequest'
 *     responses:
 *       201:
 *         $ref: '#/components/responses/MallUserResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.post('/', controller.create.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user/{id}:
 *   put:
 *     tags: [商城管理/用户管理]
 *     summary: 更新商城用户
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MallUserUpdateRequest'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallUserResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 用户不存在
 */
protectedRouter.put('/:id', controller.update.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user/{id}:
 *   delete:
 *     tags: [商城管理/用户管理]
 *     summary: 删除商城用户
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       204:
 *         description: 删除成功
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 用户不存在
 */
protectedRouter.delete('/:id', controller.delete.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user/{id}:
 *   get:
 *     tags: [商城管理/用户管理]
 *     summary: 获取商城用户详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallUserResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 用户不存在
 */
protectedRouter.get('/:id', controller.getById.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user/realname-auth/{userId}:
 *   get:
 *     tags: [商城管理/用户管理]
 *     summary: 获取用户实名认证信息
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: 用户ID
 *     responses:
 *       200:
 *         description: 获取实名认证信息成功
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.get('/realname-auth/:userId', controller.getRealnameAuth.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user/realname-auth/approve/{userId}:
 *   post:
 *     tags: [商城管理/用户管理]
 *     summary: 审核通过用户实名认证
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: 用户ID
 *     responses:
 *       200:
 *         description: 审核通过成功
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.post('/realname-auth/approve/:userId', controller.approveRealnameAuth.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/user/realname-auth/reject/{userId}:
 *   post:
 *     tags: [商城管理/用户管理]
 *     summary: 拒绝用户实名认证
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: 用户ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               reason:
 *                 type: string
 *                 description: 拒绝原因
 *     responses:
 *       200:
 *         description: 拒绝认证成功
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.post('/realname-auth/reject/:userId', controller.rejectRealnameAuth.bind(controller));

module.exports = router;

// 导出 paths 对象供动态扫描
module.exports.paths = {
  '/api/v1/master/mall/user': {
    get: {
      tags: ['商城管理/用户管理'],
      summary: '获取商城用户列表',
      parameters: [
        { $ref: '#/components/parameters/PageParam' },
        { $ref: '#/components/parameters/PageSizeParam' }
      ],
      responses: {
        200: { $ref: '#/components/responses/MallUserListResponse' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' }
      }
    },
    post: {
      tags: ['商城管理/用户管理'],
      summary: '创建商城用户',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/MallUserCreateRequest' }
          }
        }
      },
      responses: {
        201: { $ref: '#/components/responses/MallUserResponse' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' }
      }
    }
  },
  '/api/v1/master/mall/user/{id}': {
    get: {
      tags: ['商城管理/用户管理'],
      summary: '获取商城用户详情',
      parameters: [
        { $ref: '#/components/parameters/IdParam' }
      ],
      responses: {
        200: { $ref: '#/components/responses/MallUserResponse' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' },
        404: { description: '用户不存在' }
      }
    },
    put: {
      tags: ['商城管理/用户管理'],
      summary: '更新商城用户',
      parameters: [
        { $ref: '#/components/parameters/IdParam' }
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/MallUserUpdateRequest' }
          }
        }
      },
      responses: {
        200: { $ref: '#/components/responses/MallUserResponse' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' },
        404: { description: '用户不存在' }
      }
    },
    delete: {
      tags: ['商城管理/用户管理'],
      summary: '删除商城用户',
      parameters: [
        { $ref: '#/components/parameters/IdParam' }
      ],
      responses: {
        204: { description: '删除成功' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' },
        404: { description: '用户不存在' }
      }
    }
  },
  '/api/v1/master/mall/user/realname-auth/{userId}': {
    get: {
      tags: ['商城管理/用户管理'],
      summary: '获取用户实名认证信息',
      parameters: [
        {
          name: 'userId',
          in: 'path',
          required: true,
          schema: {
            type: 'string'
          },
          description: '用户ID'
        }
      ],
      responses: {
        200: {
          description: '获取实名认证信息成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean' },
                  code: { type: 'integer' },
                  message: { type: 'string' },
                  data: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      user_id: { type: 'string' },
                      type: { type: 'integer' },
                      auth_status: { type: 'integer' },
                      real_name: { type: 'string' },
                      identity_no: { type: 'string' },
                      auth_phone: { type: 'string' },
                      bank_card_no: { type: 'string' },
                      id_card_front_url: { type: 'string' },
                      id_card_back_url: { type: 'string' },
                      created_at: { type: 'string' },
                      updated_at: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' }
      }
    }
  },
  '/api/v1/master/mall/user/realname-auth/approve/{userId}': {
    post: {
      tags: ['商城管理/用户管理'],
      summary: '审核通过用户实名认证',
      parameters: [
        {
          name: 'userId',
          in: 'path',
          required: true,
          schema: {
            type: 'string'
          },
          description: '用户ID'
        }
      ],
      responses: {
        200: { description: '审核通过成功' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' }
      }
    }
  },
  '/api/v1/master/mall/user/realname-auth/reject/{userId}': {
    post: {
      tags: ['商城管理/用户管理'],
      summary: '拒绝用户实名认证',
      parameters: [
        {
          name: 'userId',
          in: 'path',
          required: true,
          schema: {
            type: 'string'
          },
          description: '用户ID'
        }
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['reason'],
              properties: {
                reason: {
                  type: 'string',
                  description: '拒绝原因'
                }
              }
            }
          }
        }
      },
      responses: {
        200: { description: '拒绝认证成功' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' }
      }
    }
  }
};
