const BaseController = require('../../../../../core/controllers/BaseController');
const MallUserLevelService = require('../services/MallUserLevelService');
const MallUserLevelDto = require('../dto/MallUserLevelDto');

/**
 * 商城会员等级管理控制器
 * 处理商城会员等级相关的请求和响应
 */
class MallUserLevelController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.levelService = new MallUserLevelService(prisma);
  }

  /**
   * 创建商城会员等级
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async create(req, res) {
    try {
      console.log('创建会员等级请求数据:', req.body);
      
      // 处理前端传递的下划线命名字段
      const formattedData = {
        levelName: req.body.level_name || req.body.levelName, // 兼容两种命名方式
        experienceValue: req.body.experience_value || req.body.experienceValue || 0,
        totalConsumption: req.body.total_consumption || req.body.totalConsumption || 0,
        levelIcon: req.body.level_icon || req.body.levelIcon || '',
        discountRate: req.body.discount_rate || req.body.discountRate || 100,
        isSystem: req.body.is_system !== undefined ? parseInt(req.body.is_system) : (req.body.isSystem !== undefined ? parseInt(req.body.isSystem) : 0),
        status: req.body.status !== undefined ? parseInt(req.body.status) : 1,
        remark: req.body.remark || ''
      };
      
      console.log('格式化后的数据:', formattedData);
      
      // 验证请求数据
      const { error, value } = MallUserLevelDto.validateCreate(formattedData);
      value.isSystem = value.is_system
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      // 添加创建者信息
      value.created_by = req.user?.id;
      
      // 确保 levelName 字段存在且有值
      if (!value.levelName && formattedData.levelName) {
        value.levelName = formattedData.levelName;
      }
      
      console.log('最终传递给服务层的数据:', value);
      
      // 创建等级
      const level = await this.levelService.create(value);
      
      // 处理BigInt序列化
      const serializedLevel = JSON.parse(JSON.stringify(level, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      return res.status(200).json({
        success: true,
        message: '创建商城会员等级成功',
        code: 200,
        data: serializedLevel
      });
    } catch (err) {
      console.error('创建商城会员等级失败:', err);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 更新商城会员等级
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async update(req, res) {
    try {
      console.log('更新会员等级请求数据:', req.body);
      console.log('原始 is_system 值:', req.body.is_system, '类型:', typeof req.body.is_system);
      
      // 处理前端传递的下划线命名字段
      const formattedData = {
        id: req.params.id,
        levelName: req.body.level_name || req.body.levelName, // 兼容两种命名方式
        experienceValue: req.body.experience_value || req.body.experienceValue,
        totalConsumption: req.body.total_consumption || req.body.totalConsumption,
        levelIcon: req.body.level_icon || req.body.levelIcon,
        discountRate: req.body.discount_rate || req.body.discountRate,
        remark: req.body.remark
      };
      
      // 直接处理 is_system 字段，不使用 || 运算符，因为当值为0时会被当作假值
      if (req.body.is_system !== undefined) {
        console.log('使用 is_system 字段:', req.body.is_system);
        formattedData.isSystem = parseInt(req.body.is_system);
      } else if (req.body.isSystem !== undefined) {
        console.log('使用 isSystem 字段:', req.body.isSystem);
        formattedData.isSystem = parseInt(req.body.isSystem);
      }
      
      // 直接处理 status 字段
      if (req.body.status !== undefined) {
        console.log('处理 status 字段:', req.body.status);
        formattedData.status = parseInt(req.body.status);
      }
      
      console.log('格式化后的数据:', formattedData);
      console.log('isSystem 字段值:', formattedData.isSystem, '类型:', typeof formattedData.isSystem);
      
      // 去除未定义的字段
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key] === undefined) {
          delete formattedData[key];
        }
      });
      
      console.log('去除未定义字段后的数据:', formattedData);
      
      // 验证请求数据
      const { error, value } = MallUserLevelDto.validateUpdate(formattedData);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      value.isSystem = value.is_system
      console.log('验证后的数据:', value);
      console.log('验证后 isSystem 字段值:', value.isSystem, '类型:', typeof value.isSystem);
      
      // 添加更新者信息
      value.updated_by = req.user?.id;
      
      // 更新等级
      const updatedLevel = await this.levelService.update(req.params.id, value);
      
      // 处理BigInt序列化
      const serializedLevel = JSON.parse(JSON.stringify(updatedLevel, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      return res.status(200).json({
        success: true,
        message: '更新商城会员等级成功',
        code: 200,
        data: serializedLevel
      });
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 删除商城会员等级
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async delete(req, res) {
    try {
      // 传入当前操作用户ID作为更新人
      await this.levelService.delete(req.params.id, req.user?.id);
      
      return this.success(res, '删除商城会员等级成功', null, 200);
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取商城会员等级详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getById(req, res) {
    try {
      const level = await this.levelService.getById(req.params.id);
      
      // 处理BigInt序列化
      const serializedLevel = JSON.parse(JSON.stringify(level, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      return this.success(res, '获取商城会员等级详情成功', serializedLevel);
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取商城会员等级列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async list(req, res) {
    try {
      // 参数校验（包含分页参数）
      const { error, value } = MallUserLevelDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      // 提取分页参数
      const { page, page_size, ...filters } = value;
      // 组装分页参数
      const pagination = this.getPagination({ page, pageSize: page_size });
      // 调用服务获取等级列表
      const result = await this.levelService.list({ ...filters, ...pagination });
      // 处理BigInt序列化
      const serializedItems = JSON.parse(JSON.stringify(result.list, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));

      // 使用服务层返回的pageSize计算总页数，确保与实际分页一致
      const actualPageSize = result.pageSize || parseInt(page_size);
      const totalPage = Math.ceil(result.total / actualPageSize);
      
      return res.status(200).json({
        success: true,
        message: '获取商城会员等级列表成功',
        code: 200,
        data: {
          items: serializedItems,
          pageInfo: {
            total: result.total,
            currentPage: parseInt(page),
            totalPage: totalPage
          }
        }
      });
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }
}

module.exports = MallUserLevelController;
