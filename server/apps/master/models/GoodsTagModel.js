const prismaManager = require('../../../core/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    // 对于id字段，返回字符串形式，避免精度丢失
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // 如果是id字段且值为bigint，返回字符串
        if (key === 'id' && typeof data[key] === 'bigint') {
          result[key] = data[key].toString();
        } else {
          result[key] = handleBigInt(data[key]);
        }
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 商品标签模型
 */
class GoodsTagModel {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取所有商品标签
   * @param {Object} options 查询选项
   * @param {string} options.name 标签名称（模糊搜索）
   * @param {boolean} options.is_enabled 是否启用
   * @param {string} options.startTime 创建时间范围开始
   * @param {string} options.endTime 创建时间范围结束
   * @param {number} options.page 页码
   * @param {number} options.pageSize 每页数量
   * @returns {Promise<Object>} 返回标签列表和总数
   */
  async getAllTags(options = {}) {
    try {
      const { name, is_enabled, description } = options;
      
      const where = {
        deleted_at: null
      };
      
      if (name) {
        where.name = {
          contains: name,
          mode: 'insensitive'
        };
      }
      
      if (description) {
        where.description = {
          contains: description,
          mode: 'insensitive'
        };
      }
      
      if (is_enabled !== undefined) {
        where.is_enabled = is_enabled === 'true' || is_enabled === true;
      }
      
      // 如果提供了创建时间范围，添加时间范围条件
      if (options.startTime || options.endTime) {
        where.created_at = {};
        
        if (options.startTime) {
          // 直接使用毫秒级时间戳，并作为BigInt处理
          const startTimeMs = BigInt(options.startTime);
          where.created_at.gte = startTimeMs;
        }
        
        if (options.endTime) {
          // 直接使用毫秒级时间戳，并作为BigInt处理
          const endTimeMs = BigInt(options.endTime);
          where.created_at.lte = endTimeMs;
        }
      }
      
      // 分页参数
      const page = options.page || 1;
      const pageSize = options.pageSize || 10;
      const skip = (page - 1) * pageSize;
      
      // 从数据库获取标签和总数
      const [tags, total] = await Promise.all([
        this.prisma.goodsTag.findMany({
          where,
          orderBy: {
            sort_order: 'asc'
          },
          skip,
          take: pageSize
        }),
        this.prisma.goodsTag.count({
          where
        })
      ]);
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(tags);
      const processedTags = convertKeysToCamelCase(processedData);
      
      return {
        data: processedTags,
        total: Number(total)
      };
    } catch (error) {
      console.error('获取商品标签列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取商品标签
   * @param {string|number} id 标签ID
   * @returns {Promise<Object>} 商品标签
   */
  async getTagById(id) {
    try {
      try {
        const tagId = BigInt(id);
        
        // 正常查询，包含deleted_at条件
        const tag = await this.prisma.goodsTag.findFirst({
          where: {
            id: tagId,
            deleted_at: null
          }
        });
        
        if (!tag) {
          return null;
        }
        
        // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
        const processedData = handleBigInt(tag);
        return convertKeysToCamelCase(processedData);
      } catch (conversionError) {
        return null;
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * 创建商品标签
   * @param {Object} tagData 标签数据
   * @param {number} userId 当前用户ID
   * @returns {Promise<Object>} 新创建的商品标签
   */
  async createTag(tagData, userId = null) {
    try {
      const { name, image_url, description, sort_order, is_enabled } = tagData;
      
      // 生成slug
      const slug = this.generateSlug(name);
      
      const now = Date.now(); // 毫秒级时间戳
      
      // 生成雪花ID
      const tagId = generateSnowflakeId();
      
      const newTag = await this.prisma.goodsTag.create({
        data: {
          id: tagId,
          name,
          slug,
          image_url,
          description,
          sort_order: sort_order || 0,
          is_enabled: is_enabled !== false,
          created_at: now,
          updated_at: now,
          created_by: userId ? BigInt(userId) : null,
          updated_by: userId ? BigInt(userId) : null
        }
      });
      
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(newTag);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('创建商品标签失败:', error);
      throw error;
    }
  }

  /**
   * 检查标签名称是否已存在
   * @param {string} name 标签名称
   * @param {BigInt} excludeId 排除的标签ID
   * @returns {Promise<boolean>} 是否存在
   */
  async isTagNameExists(name, excludeId = null) {
    try {
      const where = {
        name,
        deleted_at: null
      };
      
      if (excludeId) {
        where.id = {
          not: BigInt(excludeId)
        };
      }
      
      const existingTag = await this.prisma.goodsTag.findFirst({
        where
      });
      
      return !!existingTag;
    } catch (error) {
      console.error('检查标签名称是否存在失败:', error);
      throw error;
    }
  }

  /**
   * 更新商品标签
   * @param {string|number} id 标签ID
   * @param {Object} tagData 标签数据
   * @param {number} userId 当前用户ID
   * @returns {Promise<Object>} 更新后的商品标签
   */
  async updateTag(id, tagData, userId = null) {
    try {
      const tagId = BigInt(id);
      
      // 准备更新数据
      const updateData = { ...tagData };
      
      // 如果更新了名称，生成新的slug
      if (updateData.name) {
        updateData.slug = this.generateSlug(updateData.name);
      }
      
      // 更新时间和更新人
      const now = Date.now(); // 毫秒级时间戳
      updateData.updated_at = now;
      if (userId) {
        updateData.updated_by = BigInt(userId);
      }
      
      const updatedTag = await this.prisma.goodsTag.update({
        where: {
          id: tagId
        },
        data: updateData
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(updatedTag);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`更新ID为${id}的标签失败:`, error);
      throw error;
    }
  }

  /**
   * 删除商品标签
   * @param {string|number} id 标签ID
   * @param {number} userId 当前用户ID
   * @returns {Promise<void>}
   */
  async deleteTag(id, userId = null) {
    try {
      const tagId = BigInt(id);
      
      // 首先检查标签是否存在且未被删除
      const existingTag = await this.prisma.goodsTag.findFirst({
        where: {
          id: tagId,
          deleted_at: null
        }
      });
      
      if (!existingTag) {
        throw new Error(`ID为${id}的标签不存在或已被删除`);
      }
      
      // 软删除标签
      const now = Date.now(); // 毫秒级时间戳
      await this.prisma.goodsTag.update({
        where: {
          id: tagId
        },
        data: {
          deleted_at: now,
          updated_at: now,
          updated_by: userId ? BigInt(userId) : null
        }
      });
    } catch (error) {
      console.error(`删除ID为${id}的标签失败:`, error);
      throw error;
    }
  }

  /**
   * 检查标签是否被商品使用
   * @param {string|number} id 标签ID
   * @returns {Promise<boolean>} 是否被使用
   */
  async isTagInUse(id) {
    try {
      const tagId = BigInt(id);
      
      const tagUsage = await this.prisma.goodsTagAssociation.findFirst({
        where: {
          goods_tag_id: tagId
        }
      });
      
      return !!tagUsage;
    } catch (error) {
      console.error(`检查ID为${id}的标签是否被使用失败:`, error);
      throw error;
    }
  }

  /**
   * 生成标签slug
   * @param {string} name 标签名称
   * @returns {string} 生成的slug
   */
  generateSlug(name) {
    // 将名称转换为小写，替换空格为连字符，移除特殊字符
    const slug = name
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-');
    
    // 添加时间戳确保唯一性
    return `${slug}-${Math.floor(Date.now() / 1000)}`;
  }
}

module.exports = GoodsTagModel;
