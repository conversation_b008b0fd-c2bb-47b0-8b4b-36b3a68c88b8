/**
 * 支付记录数据模型
 * 处理支付记录相关的数据库操作
 */
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class PaymentRecordModel {
  /**
   * 创建支付记录
   * @param {Object} data - 支付记录数据
   * @returns {Promise<Object>} - 创建的支付记录
   */
  static async create(data) {
    try {
      const result = await prisma.payment_records.create({
        data: {
          ...data,
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
      return result;
    } catch (error) {
      console.error('创建支付记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取支付记录
   * @param {string|number} id - 支付记录ID
   * @returns {Promise<Object|null>} - 支付记录信息
   */
  static async findById(id) {
    try {
      const result = await prisma.payment_records.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        },
        include: {
          order: true
        }
      });
      return result;
    } catch (error) {
      console.error('获取支付记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据支付流水号获取支付记录
   * @param {string} paymentSn - 支付流水号
   * @returns {Promise<Object|null>} - 支付记录信息
   */
  static async findByPaymentSn(paymentSn) {
    try {
      const result = await prisma.payment_records.findFirst({
        where: {
          payment_sn: paymentSn,
          deleted_at: null
        },
        include: {
          order: true
        }
      });
      return result;
    } catch (error) {
      console.error('根据支付流水号获取支付记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据第三方交易号获取支付记录
   * @param {string} thirdPartyTradeNo - 第三方交易号
   * @returns {Promise<Object|null>} - 支付记录信息
   */
  static async findByThirdPartyTradeNo(thirdPartyTradeNo) {
    try {
      const result = await prisma.payment_records.findFirst({
        where: {
          third_party_trade_no: thirdPartyTradeNo,
          deleted_at: null
        },
        include: {
          order: true
        }
      });
      return result;
    } catch (error) {
      console.error('根据第三方交易号获取支付记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据订单ID获取支付记录列表
   * @param {string|number} orderId - 订单ID
   * @returns {Promise<Array>} - 支付记录列表
   */
  static async findByOrderId(orderId) {
    try {
      const result = await prisma.payment_records.findMany({
        where: {
          order_id: BigInt(orderId),
          deleted_at: null
        },
        orderBy: {
          created_at: 'desc'
        }
      });
      return result;
    } catch (error) {
      console.error('根据订单ID获取支付记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新支付记录
   * @param {string|number} id - 支付记录ID
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} - 更新后的支付记录
   */
  static async update(id, data) {
    try {
      const result = await prisma.payment_records.update({
        where: {
          id: BigInt(id)
        },
        data: {
          ...data,
          updated_at: BigInt(Date.now())
        }
      });
      return result;
    } catch (error) {
      console.error('更新支付记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新支付状态
   * @param {string|number} id - 支付记录ID
   * @param {number} status - 支付状态
   * @param {Object} additionalData - 额外数据
   * @returns {Promise<Object>} - 更新后的支付记录
   */
  static async updateStatus(id, status, additionalData = {}) {
    try {
      const updateData = {
        payment_status: status,
        updated_at: BigInt(Date.now()),
        ...additionalData
      };

      // 如果是支付成功，记录支付时间
      if (status === 2) { // SUCCESS
        updateData.payment_time = BigInt(Date.now());
      }

      const result = await prisma.payment_records.update({
        where: {
          id: BigInt(id)
        },
        data: updateData
      });
      return result;
    } catch (error) {
      console.error('更新支付状态失败:', error);
      throw error;
    }
  }

  /**
   * 软删除支付记录
   * @param {string|number} id - 支付记录ID
   * @returns {Promise<Object>} - 删除结果
   */
  static async softDelete(id) {
    try {
      const result = await prisma.payment_records.update({
        where: {
          id: BigInt(id)
        },
        data: {
          deleted_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
      return result;
    } catch (error) {
      console.error('删除支付记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取支付记录列表（分页）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 分页结果
   */
  static async findMany(options = {}) {
    try {
      const {
        page = 1,
        pageSize = 20,
        userId,
        paymentMethodId,
        paymentStatus,
        startTime,
        endTime
      } = options;

      const where = {
        deleted_at: null
      };

      if (userId) {
        where.user_id = BigInt(userId);
      }

      if (paymentMethodId) {
        where.payment_method_id = paymentMethodId;
      }

      if (paymentStatus !== undefined) {
        where.payment_status = paymentStatus;
      }

      if (startTime && endTime) {
        where.created_at = {
          gte: BigInt(startTime),
          lte: BigInt(endTime)
        };
      }

      const [total, records] = await Promise.all([
        prisma.payment_records.count({ where }),
        prisma.payment_records.findMany({
          where,
          include: {
            order: true
          },
          orderBy: {
            created_at: 'desc'
          },
          skip: (page - 1) * pageSize,
          take: pageSize
        })
      ]);

      return {
        records,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      };
    } catch (error) {
      console.error('获取支付记录列表失败:', error);
      throw error;
    }
  }
}

module.exports = PaymentRecordModel;
