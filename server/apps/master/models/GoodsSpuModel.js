/**
 * 商品SPU模型
 */
const prismaManager = require('../../../core/prisma');
// 使用 slugify 包生成URL友好的slug
const slugify = require('slugify');
// 导入雪花ID生成工具
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
// 导入uuid库，替代nanoid
const { v4: uuidv4 } = require('uuid');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 将下划线命名法转换为驼峰命名法
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const convertKeysToCamelCase = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data !== 'object' || data instanceof Date) return data;
  
  if (Array.isArray(data)) {
    return data.map(item => convertKeysToCamelCase(item));
  }
  
  const newData = {};
  for (const key in data) {
    const camelCaseKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    newData[camelCaseKey] = convertKeysToCamelCase(data[key]);
  }
  return newData;
};

class GoodsSpuModel {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    // 使用传入的prisma实例或使用 prismaManager
    this.prisma = prismaManager.getClient('base');
  }
  
  /**
   * 将ID转换为BigInt类型
   * @param {string|number} id - 需要转换的ID
   * @returns {BigInt} - 转换后的BigInt类型ID
   */
  ensureBigInt(id) {
    return typeof id === 'string' ? BigInt(id) : id;
  }

  /**
   * 生成唯一的slug
   * @param {string} name - 商品名称
   * @returns {string} - 唯一的slug
   */
  async generate_unique_slug(name) {
    // 使用 slugify 将中文名称转换为URL友好的格式
    const baseSlug = slugify(name, {
      lower: true,      // 转换为小写
      strict: true,     // 严格模式，移除特殊字符
      locale: 'zh'      // 中文支持
    });
    
    try {
      // 检查slug是否已存在
      const existingSpu = await this.prisma.goodsSpu.findFirst({
        where: { slug: baseSlug }
      });
      
      // 如果已存在，则添加随机字符串
      if (existingSpu) {
        // 使用uuid生成6位随机字符
        return `${baseSlug}-${uuidv4().substring(0, 6)}`;
      }
      
      return baseSlug;
    } catch (error) {
      console.error('生成唯一slug失败:', error);
      // 如果出错，生成一个带随机字符串的slug
      return `${baseSlug}-${uuidv4().substring(0, 6)}`;
    }
  }

  /**
   * 将前端状态映射为数据库状态码
   * @param {string} statusString - 前端状态字符串(on_shelf/off_shelf)
   * @returns {number} - 数据库状态码
   */
  map_status_code(status) {
    // 将数字转换为数据库状态码
    // 0 -> 1(下架), 1 -> 2(上架)
    if (status === 0) {
      return 1; // 下架
    } else if (status === 1) {
      return 2; // 上架
    }
    
    return 0; // 默认为0(草稿)
  }

  /**
   * 添加商品SPU
   * @param {Object} spuData - 商品SPU数据
   * @param {Object} tx - 事务对象
   * @returns {Object} - 创建的SPU对象
   */
  async add_spu(spuData, tx) {
    return await tx.goodsSpu.create({
      data: spuData
    });
  }

  /**
   * 添加分类关联
   * @param {number|string} spuId - 商品SPU ID
   * @param {number|string} categoryId - 分类ID
   * @param {Object} tx - 事务对象
   */
  async add_category_association(spuId, categoryId, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    const catId = this.ensureBigInt(categoryId);
    
    return await tx.goodsCategoryAssociation.create({
      data: {
        goods_spu_id: id,
        goods_category_id: catId,
        is_primary: true
      }
    });
  }

  /**
   * 添加SPU图片
   * @param {number|string} spuId - 商品SPU ID
   * @param {Array} images - 图片数组
   * @param {Object} tx - 事务对象
   */
  async add_spu_images(spuId, images, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    const now = BigInt(Date.now()); // 获取当前时间戳
    
    const imagePromises = images.map(image => 
      tx.goodsImage.create({
        data: {
          goods_spu_id: id,
          goods_sku_id: null,
          image_url: image.url,
          sort_order: image.sortOrder,
          is_default: image.isDefault,
          created_at: now,
          updated_at: now
        }
      })
    );
    
    return await Promise.all(imagePromises);
  }

  /**
   * 添加视频信息
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} video - 视频信息
   * @param {Object} tx - 事务对象
   */
  async add_video(spuId, video, tx) {
    if (!video) return null;
    
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    const now = BigInt(Date.now()); // 获取当前时间戳
    
    return await tx.goodsVideo.create({
      data: {
        goods_spu_id: id,
        video_url: video.url,
        cover_image_url: video.coverUrl,
        sort_order: video.sortOrder,
        created_at: now,
        updated_at: now
      }
    });
  }

  /**
   * 添加标签关联
   * @param {number} spuId - 商品SPU ID
   * @param {Array} tagIds - 标签ID数组
   * @param {Object} tx - 事务对象
   */
  async add_tag_associations(spuId, tagIds, tx) {
    if (!tagIds || tagIds.length === 0) return [];
    
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    const tagPromises = tagIds.map(tagId => {
      const tagIdBigInt = this.ensureBigInt(tagId);
      return tx.goodsTagAssociation.create({
        data: {
          goods_spu_id: id,
          goods_tag_id: tagIdBigInt
        }
      });
    });
    
    return await Promise.all(tagPromises);
  }

  /**
   * 添加服务关联
   * @param {number|string} spuId - 商品SPU ID
   * @param {Array} serviceIds - 服务ID数组
   * @param {Object} tx - 事务对象
   */
  async add_service_associations(spuId, serviceIds, tx) {
    if (!serviceIds || serviceIds.length === 0) return [];
    
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    const servicePromises = serviceIds.map(serviceId => {
      const serviceIdBigInt = this.ensureBigInt(serviceId);
      return tx.goodsServiceAssociation.create({
        data: {
          goods_spu_id: id,
          goods_service_id: serviceIdBigInt
        }
      });
    });
    
    return await Promise.all(servicePromises);
  }

  /**
   * 添加属性值
   * @param {number|string} spuId - 商品SPU ID
   * @param {Array} attributeValues - 属性值数组
   * @param {Object} tx - 事务对象
   */
  async add_attribute_values(spuId, attributeValues, tx) {
    if (!attributeValues || attributeValues.length === 0) return [];
    
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    const attrPromises = attributeValues.map(attr => {
      const attrItemIdBigInt = this.ensureBigInt(attr.attributeItemId);
      // 为每个属性值生成雪花ID
      const attrValueId = generateSnowflakeId();
      return tx.goodsAttributeValue.create({
        data: {
          id: BigInt(attrValueId),
          goods_spu_id: id,
          goods_attribute_item_id: attrItemIdBigInt,
          value: attr.value,
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
    });
    
    return await Promise.all(attrPromises);
  }

  /**
   * 查找或创建规格名
   * @param {string} name - 规格名
   * @param {Object} tx - 事务对象
   * @returns {Object} - 规格名对象
   */
  async find_or_create_spec_name(name, tx) {
    // 只选择必要的字段，避免 created_at 字段的类型转换问题
    const existingSpecName = await tx.goodsSpecificationName.findFirst({
      where: { name },
      select: {
        id: true,
        name: true
      }
    });
    
    if (existingSpecName) {
      return existingSpecName;
    }
    
    const now = BigInt(Date.now()); // 获取当前时间戳作为BigInt
    
    return await tx.goodsSpecificationName.create({
      data: { 
        name,
        created_at: now,
        updated_at: now
      },
      select: {
        id: true,
        name: true
      }
    });
  }

  /**
   * 查找或创建规格值
   * @param {number} specNameId - 规格名ID
   * @param {string} value - 规格值
   * @param {Object} tx - 事务对象
   * @returns {Object} - 规格值对象
   */
  async find_or_create_spec_value(specNameId, value, tx) {
    // 只选择必要的字段，避免 created_at 字段的类型转换问题
    const existingSpecValue = await tx.goodsSpecificationValue.findFirst({
      where: {
        goods_specification_name_id: specNameId,
        value
      },
      select: {
        id: true,
        goods_specification_name_id: true,
        value: true
      }
    });
    
    if (existingSpecValue) {
      return existingSpecValue;
    }
    
    const now = BigInt(Date.now()); // 获取当前时间戳作为BigInt
    
    return await tx.goodsSpecificationValue.create({
      data: {
        goods_specification_name_id: specNameId,
        value,
        created_at: now,
        updated_at: now
      },
      select: {
        id: true,
        goods_specification_name_id: true,
        value: true
      }
    });
  }

  /**
   * 添加SKU
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} skuData - SKU数据
   * @param {Object} tx - 事务对象
   */
  async add_sku(spuId, skuData, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    // 确保移除不存在于数据库的字段
    const { is_default, ...validSkuData } = skuData;
    
    return await tx.goodsSku.create({
      data: {
        goods_spu_id: id,
        ...validSkuData
      }
    });
  }

  /**
   * 添加SKU规格值关联
   * @param {number} skuId - SKU ID
   * @param {number} specValueId - 规格值ID
   * @param {Object} tx - 事务对象
   */
  async add_sku_spec_value(skuId, specValueId, tx) {
    return await tx.goodsSkuSpecificationValue.create({
      data: {
        goods_sku_id: BigInt(skuId),
        goods_specification_value_id: BigInt(specValueId)
      }
    });
  }

  /**
   * 添加SKU图片
   * @param {number|string} spuId - 商品SPU ID
   * @param {number|string} skuId - SKU ID
   * @param {string} imageUrl - 图片URL
   * @param {Object} tx - 事务对象
   */
  async add_sku_image(spuId, skuId, imageUrl, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    const skuIdBigInt = this.ensureBigInt(skuId);
    const now = BigInt(Date.now()); // 获取当前时间戳
    
    return await tx.goodsImage.create({
      data: {
        goods_spu_id: id,
        goods_sku_id: skuIdBigInt,
        image_url: imageUrl,
        sort_order: 0,
        is_default: false,
        created_at: now,
        updated_at: now
      }
    });
  }

  /**
   * 更新SPU总库存
   * @param {number|string} spuId - 商品SPU ID
   * @param {number} totalStock - 总库存
   * @param {Object} tx - 事务对象
   */
  async update_spu_total_stock(spuId, totalStock, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);

    return await tx.goodsSpu.update({
      where: { id },
      data: { total_stock: totalStock }
    });
  }

  /**
   * 更新SPU总销量
   * @param {number|string} spuId - 商品SPU ID
   * @param {number} totalSales - 总销量
   * @param {Object} tx - 事务对象
   */
  async update_spu_total_sales(spuId, totalSales, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);

    return await tx.goodsSpu.update({
      where: { id },
      data: { total_sales: totalSales }
    });
  }
  
  /**
   * 更新SPU
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} spuData - 更新数据
   * @param {Object} tx - 事务对象
   * @returns {Object} - 更新后的SPU对象
   */
  async update_spu(spuId, spuData, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    return await tx.goodsSpu.update({
      where: { id },
      data: spuData
    });
  }
  
  /**
   * 删除分类关联
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} tx - 事务对象
   */
  async delete_category_associations(spuId, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    return await tx.goodsCategoryAssociation.deleteMany({
      where: { goods_spu_id: id }
    });
  }
  
  /**
   * 删除SPU图片
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} tx - 事务对象
   */
  async delete_spu_images(spuId, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    return await tx.goodsImage.deleteMany({
      where: { 
        goods_spu_id: id,
        goods_sku_id: null
      }
    });
  }
  
  /**
   * 删除视频
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} tx - 事务对象
   */
  async delete_videos(spuId, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    return await tx.goodsVideo.deleteMany({
      where: { goods_spu_id: id }
    });
  }
  
  /**
   * 删除标签关联
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} tx - 事务对象
   */
  async delete_tag_associations(spuId, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    return await tx.goodsTagAssociation.deleteMany({
      where: { goods_spu_id: id }
    });
  }
  
  /**
   * 删除服务关联
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} tx - 事务对象
   */
  async delete_service_associations(spuId, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    return await tx.goodsServiceAssociation.deleteMany({
      where: { goods_spu_id: id }
    });
  }
  
  /**
   * 删除属性值
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} tx - 事务对象
   */
  async delete_attribute_values(spuId, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    return await tx.goodsAttributeValue.deleteMany({
      where: { goods_spu_id: id }
    });
  }
  
  /**
   * 删除SKU及相关数据
   * @param {number|string} spuId - 商品SPU ID
   * @param {Object} tx - 事务对象
   */
  async delete_skus(spuId, tx) {
    // 确保ID是BigInt类型
    const id = this.ensureBigInt(spuId);
    
    // 获取当前SPU的所有SKU
    const skus = await tx.goodsSku.findMany({
      where: { goods_spu_id: id }
    });
    
    const skuIds = skus.map(sku => sku.id);
    
    // 删除SKU规格值关联
    if (skuIds.length > 0) {
      await tx.goodsSkuSpecificationValue.deleteMany({
        where: { goods_sku_id: { in: skuIds } }
      });
      
      // 删除SKU图片
      await tx.goodsImage.deleteMany({
        where: { goods_sku_id: { in: skuIds } }
      });
    }
    
    // 删除SKU
    return await tx.goodsSku.deleteMany({
      where: { goods_spu_id: id }
    });
  }
  
  /**
   * 根据ID获取商品SPU
   * @param {number} id - 商品ID
   * @param {Object} tx - 可选的事务对象
   * @returns {Promise<Object|null>} - 商品对象或null
   */
  async getGoodsSpuById(id, tx = this.prisma) {
    try {
      const spu = await tx.goodsSpu.findUnique({
        where: { id: BigInt(id) },
        include: {
          goods_brands: true,
          goods_category_associations: true,
          goods_skus: true,
          goods_images: true,
          goods_videos: true,
          goods_tag_associations: true,
          goods_service_associations: true,
          goods_attribute_values: true
        }
      });
      
      return spu;
    } catch (error) {
      console.error(`获取商品ID ${id} 失败:`, error);
      return null;
    }
  }

  /**
   * 检查分类ID是否存在
   * @param {number} categoryId - 分类ID
   * @returns {boolean} - 是否存在
   */
  async check_category_exists(categoryId) {
    try {
      const category = await this.prisma.goodsCategory.findFirst({
        where: {
          id: categoryId,
          deleted_at: null
        }
      });
      return !!category;
    } catch (error) {
      console.error('检查分类存在性失败:', error);
      // 如果出错，暂时返回 true 以继续流程
      return true;
    }
  }

  /**
   * 检查品牌ID是否存在
   * @param {number} brandId - 品牌ID
   * @returns {boolean} - 是否存在
   */
  async check_brand_exists(brandId) {
    try {
      const brand = await this.prisma.goodsBrand.findUnique({
        where: { id: this.ensureBigInt(brandId) }
      });
      return !!brand;
    } catch (error) {
      console.error(`检查品牌ID ${brandId} 存在性失败:`, error);
      throw error;
    }
  }

  /**
   * 递归获取指定分类及其所有子分类的ID
   * @param {BigInt} categoryId - 分类ID
   * @returns {Promise<Array<BigInt>>} - 包含指定分类及其所有子分类ID的数组
   */
  async getCategoryWithSubcategoriesIds(categoryId) {
    // 初始化返回数组，包含当前分类ID
    const categoryIds = [categoryId];
    
    // 定义递归函数，收集所有子分类ID
    const collectChildCategories = async (parentId) => {
      // 查询直接子分类
      const childCategories = await this.prisma.goodsCategory.findMany({
        where: {
          goods_parent_category_id: parentId,
          is_enabled: 1,
          deleted_at: null
        },
        select: { id: true }
      });
      
      // 处理每个子分类
      for (const childCategory of childCategories) {
        categoryIds.push(childCategory.id);
        // 递归查询子分类的子分类
        await collectChildCategories(childCategory.id);
      }
    };
    
    // 开始递归查询
    await collectChildCategories(categoryId);
    
    return categoryIds;
  }

  /**
   * 检查运费模板ID是否存在
   * @param {number} templateId - 运费模板ID
   * @returns {boolean} - 是否存在
   */
  async check_freight_template_exists(templateId) {
    try {
      const template = await this.prisma.goodsFreightTemplate.findFirst({
        where: { 
          id: templateId,
          deleted_at: null 
        }
      });
      return !!template;
    } catch (error) {
      console.error('检查运费模板存在性失败:', error);
      // 如果出错，暂时返回 true 以继续流程
      return true;
    }
  }

  /**
   * 检查标签ID是否存在
   * @param {Array} tagIds - 标签ID数组
   * @returns {boolean} - 是否全部存在
   */
  async check_tags_exist(tagIds) {
    if (!tagIds || tagIds.length === 0) return true;
    
    try {
      const tags = await this.prisma.goodsTag.findMany({
        where: {
          id: { in: tagIds },
          deleted_at: null
        }
      });
      
      return tags.length === tagIds.length;
    } catch (error) {
      console.error('检查标签存在性失败:', error);
      // 如果出错，暂时返回 true 以继续流程
      return true;
    }
  }

  /**
   * 检查服务ID是否存在
   * @param {Array} serviceIds - 服务ID数组
   * @returns {boolean} - 是否全部存在
   */
  async check_services_exist(serviceIds) {
    if (!serviceIds || serviceIds.length === 0) return true;
    
    try {
      const services = await this.prisma.goodsService.findMany({
        where: {
          id: { in: serviceIds },
          deleted_at: null
        }
      });
      
      return services.length === serviceIds.length;
    } catch (error) {
      console.error('检查服务存在性失败:', error);
      // 如果出错，暂时返回 true 以继续流程
      return true;
    }
  }

  /**
   * 检查属性项ID是否存在
   * @param {Array} attributeItemIds - 属性项ID数组
   * @returns {boolean} - 是否全部存在
   */
  async check_attribute_items_exist(attributeItemIds) {
    if (!attributeItemIds || attributeItemIds.length === 0) return true;
    
    try {
      const attrs = await this.prisma.goodsAttributeItem.findMany({
        where: {
          id: { in: attributeItemIds },
          deleted_at: null
        }
      });
      
      return attrs.length === attributeItemIds.length;
    } catch (error) {
      console.error('检查属性项存在性失败:', error);
      // 如果出错，暂时返回 true 以继续流程
      return true;
    }
  }

  /**
   * 根据ID获取商品详情
   * @param {number} id - 商品ID
   * @returns {Promise<Object|null>} 返回商品详情对象或null
   */
  async getGoodsSpuDetailById(id) {
    try {
      console.log(`获取商品详情: ID=${id}`);
      
      // 使用Prisma客户端查询，使用select避免时间戳字段
      const goodsSpu = await this.prisma.goodsSpu.findFirst({
        where: { 
          id: BigInt(id),
          deleted_at: null // 只获取未删除的商品
        },
        select: {
          id: true,
          spu_code: true,
          name: true,
          subtitle: true,
          slug: true,
          description: true,
          goods_brand_id: true,
          goods_freight_template_id: true,
          meta_title: true,
          meta_keywords: true,
          meta_description: true,
          sort_order: true,
          status: true,
          published_at: true,
          is_virtual: true,
          is_shipping_required: true,
          is_free_shipping: true,
          deleted_at: true,
          total_sales: true,
          total_stock: true,
          delivery_area: true,
          delivery_time: true,
          
          // 关联数据，避免包含时间戳字段
          goods_brands: {
            select: {
              id: true,
              name: true,
              logo_url: true,
              description: true
            }
          },
          goods_category_associations: {
            select: {
              goods_category_id: true,
              is_primary: true
            }
          },
          goods_images: {
            where: {
              goods_sku_id: null  // 只获取SPU的图片，不包含SKU的图片
            },
            select: {
              id: true,
              image_url: true,
              sort_order: true
            },
            orderBy: {
              sort_order: 'asc'
            }
          },
          goods_videos: {
            select: {
              id: true,
              video_url: true,
              cover_image_url: true
            }
          },
          goods_tag_associations: {
            select: {
              goods_tag_id: true,
              goods_tags: {
                select: {
                  id: true,
                  name: true,
                  image_url: true
                }
              }
            }
          },
          goods_service_associations: {
            select: {
              goods_service_id: true,
              goods_service: {
                select: {
                  id: true,
                  name: true,
                  image_url: true,
                  description: true
                }
              }
            }
          },
          // 添加商品属性值关联查询，使用正确的字段名
          goods_attribute_values: {
            select: {
              id: true,
              goods_attribute_item_id: true,
              value: true,
              goods_attribute_items: {
                select: {
                  id: true,
                  name: true,
                  type: true
                }
              }
            }
          },
          goods_skus: {
            select: {
              id: true,
              sku_code: true,
              barcode: true,
              sales_price: true,
              market_price: true,
              cost_price: true,
              weight: true,
              volume: true,
              stock: true,
              sales_volume: true, // 添加销售数量字段
              unit: true,
              is_enabled: true,
              goods_images: {
                select: {
                  id: true,
                  image_url: true,
                  sort_order: true
                }
              },
              goods_sku_specification_values: {
                select: {
                  goods_specification_value_id: true,
                  goods_specification_value: {
                    select: {
                      id: true,
                      value: true,
                      goods_specification_name: {
                        select: {
                          id: true,
                          name: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });
      
      if (!goodsSpu) return null;
      
      // 打印SKU规格值关联数据
      if (goodsSpu.goods_skus && goodsSpu.goods_skus.length > 0) {
        console.log(`商品共有 ${goodsSpu.goods_skus.length} 个SKU`);
        goodsSpu.goods_skus.forEach((sku, index) => {
          console.log(`检查SKU ${index + 1}: ID=${sku.id}`);
          if (sku.goods_sku_specification_values && sku.goods_sku_specification_values.length > 0) {
            console.log(`SKU ${index + 1} 有 ${sku.goods_sku_specification_values.length} 个规格值关联`);
            sku.goods_sku_specification_values.forEach((specValue, i) => {
              const specName = specValue.goods_specification_value?.goods_specification_name?.name;
              const value = specValue.goods_specification_value?.value;
              console.log(`规格${i + 1}: 名称=${specName}, 值=${value}`);
            });
          } else {
            console.log(`SKU ${index + 1} 没有规格值关联数据`);
          }
        });
      } else {
        console.log('商品没有SKU数据');
      }
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(goodsSpu);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`获取ID为${id}的商品详情失败:`, error);
      console.error('错误堆栈:', error.stack);
      return null;
    }
  }

  /**
   * 获取商品列表
   * @param {Object} options - 查询参数
   */
  async getGoodsSpuList(options) {
    try {
      const {
        page = 1,
        pageSize = 20,
        keyword,
        categoryId,
        brandId,
        status,
        startTime,
        endTime,
        sortField = 'created_at',
        sortOrder = 'desc',
        isDeleted
      } = options;
      
      // 构建查询条件
      const where = {};
      
      // 根据 isDeleted 参数筛选已删除或未删除的商品
      if (isDeleted !== undefined && isDeleted !== null && isDeleted !== '') {
        // isDeleted=0 表示查询未删除的商品，isDeleted=1 表示查询已删除的商品
        if (parseInt(isDeleted) === 0) {
          where.deleted_at = null; // 未删除的商品
        } else if (parseInt(isDeleted) === 1) {
          where.deleted_at = { not: null }; // 已删除的商品（回收站）
        }
      } else {
        // 默认只查询未删除的商品
        where.deleted_at = null;
      }
      
      // 关键词搜索
      if (keyword) {
        where.OR = [
          { name: { contains: keyword } },
          { subtitle: { contains: keyword } },
          { 
            goods_skus: {
              some: {
                sku_code: { contains: keyword }
              }
            }
          }
        ];
      }
      
      // 商品分类筛选
      if (categoryId) {
        try {
          // 处理雪花ID精度问题，转为BigInt
          const categoryIdBigInt = BigInt(categoryId);
          
          // 获取指定分类及其所有子分类ID
          const categoryIds = await this.getCategoryWithSubcategoriesIds(categoryIdBigInt);
          
          // 使用in操作符查询所有符合分类条件的商品
          where.goods_category_associations = {
            some: {
              goods_category_id: {
                in: categoryIds
              }
            }
          };
        } catch (e) {
          console.error('分类ID转换或获取子分类失败:', e);
          // 如果转换失败，则当作普通整数处理
          where.goods_category_associations = {
            some: {
              goods_category_id: parseInt(categoryId)
            }
          };
        }
      }
      
      // 品牌筛选
      if (brandId) {
        try {
          // 处理雪花ID精度问题，转为BigInt
          where.goods_brand_id = BigInt(brandId);
        } catch (e) {
          console.error('品牌ID转换失败:', e);
          // 如果转换失败，则当作普通整数处理
          where.goods_brand_id = parseInt(brandId);
        }
      }
      
      // 商品状态筛选（0下架、1上架）
      if (status !== null && status !== undefined) {
        // 直接使用数值：0表示下架，1表示上架
        // 数据库中也使用相同的值：0表示下架，1表示上架
        where.status = parseInt(status);
      }
      
      // 创建时间范围筛选（毫秒时间戳）
      if (startTime) {
        // 直接使用毫秒时间戳，不需要Date对象转换
        where.created_at = {
          ...where.created_at,
          gte: BigInt(startTime)
        };
      }
      
      if (endTime) {
        where.created_at = {
          ...where.created_at,
          lte: BigInt(endTime)
        };
      }
      
      // 设置排序
      const orderBy = {};
      
      // 处理排序字段
      if (sortField === 'price') {
        // 价格排序，使用最低 SKU 价格排序
        orderBy.goods_skus = {
          orderBy: {
            sales_price: sortOrder
          }
        };
      } else if (sortField === 'sales') {
        // 按销量排序
        orderBy.total_sales = sortOrder;
      } else if (sortField === 'stock') {
        // 按库存排序
        orderBy.total_stock = sortOrder;
      } else {
        // 默认按创建时间排序
        orderBy[sortField] = sortOrder;
      }
      
      // 计算总数
      const total = await this.prisma.goodsSpu.count({
        where
      });
      
      // 查询商品列表
      const spuList = await this.prisma.goodsSpu.findMany({
        where,
        orderBy,
        skip: (page - 1) * pageSize,
        take: parseInt(pageSize),
        select: {
          id: true,
          name: true,
          subtitle: true,
          status: true,
          total_sales: true,
          total_stock: true,
          created_at: true,
          deleted_at: true, // 添加软删除字段
          // 关联品牌信息
          goods_brands: {
            select: {
              id: true,
              name: true
            }
          },
          // 关联分类
          goods_category_associations: {
            select: {
              goods_category_id: true,
              is_primary: true
            }
          },
          // 关联SKU信息
          goods_skus: {
            where: {
              deleted_at: isDeleted !== undefined && isDeleted !== null && isDeleted !== ''
                ? parseInt(isDeleted) === 0
                  ? null // 查询未删除的 SKU
                  : { not: null } // 查询已删除的 SKU
                : null // 默认查询未删除的 SKU
            },
            select: {
              id: true,
              sku_code: true,
              sales_price: true,
              market_price: true,
              cost_price: true,
              stock: true,
              sales_volume: true,
              unit: true,
              weight: true,
              volume: true,
              barcode: true,
              is_enabled: true,
              // SKU图片
              goods_images: {
                select: {
                  image_url: true
                }
              },
              // SKU规格值
              goods_sku_specification_values: {
                select: {
                  goods_specification_value_id: true,
                  goods_specification_value: {
                    select: {
                      id: true,
                      value: true,
                      goods_specification_name: {
                        select: {
                          id: true,
                          name: true
                        }
                      }
                    }
                  }
                }
              },
              // SKU关联的渠道信息
              goods_sku_channels: {
                where: {
                  deleted_at: null
                },
                select: {
                  id: true,
                  channel_id: true,
                  third_party_sku_code: true,
                  is_enabled: true,
                  channel: {
                    select: {
                      id: true,
                      name: true,
                      icon_url: true
                    }
                  }
                }
              }
            }
          },
          // 关联商品服务
          goods_service_associations: {
            select: {
              goods_service_id: true
            }
          },
          // 关联商品图片
          goods_images: {
            where: {
              goods_sku_id: null  // 只获取SPU的图片，不包含SKU的图片
            },
            select: {
              image_url: true,
              is_default: true
            }
          }
        }
      });
      
      // 获取分类数据
      const getCategoryData = async (categoryIds) => {
        if (!categoryIds || categoryIds.length === 0) return { name: '' };
        
        try {
          const category = await this.prisma.goodsCategory.findUnique({
            where: { id: categoryIds[0].goods_category_id },
            select: { name: true }
          });
          return category || { name: '' };
        } catch (error) {
          console.error('获取分类信息失败:', error);
          return { name: '' };
        }
      };
      
      // 获取服务数据
      const getServiceData = async (serviceAssociations) => {
        if (!serviceAssociations || serviceAssociations.length === 0) return [];
        
        try {
          const serviceIds = serviceAssociations.map(sa => sa.goods_service_id);
          const services = await this.prisma.goodsService.findMany({
            where: { id: { in: serviceIds } },
            select: { name: true }
          });
          return services.map(s => s.name);
        } catch (error) {
          console.error('获取服务信息失败:', error);
          return [];
        }
      };
      
      // 转换数据格式
      const itemsPromises = spuList.map(async spu => {
        // 处理SPU图片
        const imageList = [];
        let mainImage = '';
        
        // 遍历所有SPU图片
        spu.goods_images.forEach(img => {
          // 添加到图片数组
          imageList.push(img.image_url);
          
          // 设置主图
          if (img.is_default) {
            mainImage = img.image_url;
          }
        });
        
        // 如果没有设置默认主图，使用第一张图片
        if (!mainImage && imageList.length > 0) {
          mainImage = imageList[0];
        }
        
        // 如果没有图片，设置空字符串
        if (!mainImage) {
          mainImage = '';
        }
        
        // 处理SKU数据，添加规格名称和图片信息
        const processedSkus = spu.goods_skus.map(sku => {
          // 处理SKU规格值
          const specValues = {};
          const specs = [];
          
          sku.goods_sku_specification_values.forEach(specValue => {
            const specNameObj = specValue.goods_specification_value.goods_specification_name;
            const specValueObj = specValue.goods_specification_value;
            
            const specName = specNameObj.name;
            const value = specValueObj.value;
            
            // 添加到specValues对象
            specValues[specName] = value;
            
            // 添加到specs数组，包含完整信息
            specs.push({
              specNameId: specNameObj.id,
              specName: specName,
              specValueId: specValueObj.id,
              specValue: value,
              specificationValueId: specValue.goods_specification_value_id
            });
          });
          
          // 生成SKU名称，如果有规格则组合SPU名称和规格值，否则使用SPU名称
          let skuName = '';
          
          // 如果有规格值，则组合SPU名称和规格值作为SKU名称
          if (Object.keys(specValues).length > 0) {
            const specValueStr = Object.values(specValues).join(' ');
            skuName = `${spu.name} ${specValueStr}`;
          } else {
            // 如果SKU没有规格值，直接使用SPU名称
            skuName = spu.name;
          }
          
          // 获取SKU图片
          const skuImage = sku.goods_images.length > 0 ? 
                          sku.goods_images[0].image_url : 
                          mainImage;
          
          // 处理渠道信息
          const channels = [];
          if (sku.goods_sku_channels && sku.goods_sku_channels.length > 0) {
            sku.goods_sku_channels.forEach(channelInfo => {
              if (channelInfo.channel) {
                channels.push({
                  id: channelInfo.id,
                  channelId: channelInfo.channel_id,
                  channelName: channelInfo.channel.name,
                  channelIcon: channelInfo.channel.icon_url,
                  thirdPartySkuCode: channelInfo.third_party_sku_code,
                  isEnabled: channelInfo.is_enabled
                });
              }
            });
          }
          
          return {
            id: sku.id,
            skuCode: sku.sku_code,
            skuName,
            specValues,  // 保留简化的规格值对象，方便前端使用
            specs,      // 添加完整的规格信息数组，包含ID等信息
            imageUrl: skuImage,
            salesPrice: sku.sales_price,
            marketPrice: sku.market_price,
            costPrice: sku.cost_price,
            stock: sku.stock,
            salesVolume: sku.sales_volume || 0,
            unit: sku.unit || '',
            weight: sku.weight,
            volume: sku.volume,
            barcode: sku.barcode,
            status: sku.is_enabled,
            statusText: sku.is_enabled === 1 ? '启用' : '禁用',
            channels: channels  // 添加渠道信息
          };
        });
        
        // 从 SKU 中获取价格区间
        const prices = spu.goods_skus.map(sku => sku.sales_price).filter(price => price > 0);
        const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
        const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;
        
        // 计算库存总量
        const totalStock = spu.goods_skus.reduce((sum, sku) => sum + sku.stock, 0);
        
        // 状态文本 - status: 0表示下架，1表示上架
        const statusText = spu.status === 1 ? '已上架' : '已下架';
        
        // 获取分类名称
        const category = await getCategoryData(spu.goods_category_associations);
        
        // 获取品牌名称
        const brand = spu.goods_brands || { name: '' };
        
        // 获取服务名称列表
        const serviceList = await getServiceData(spu.goods_service_associations);
        
        // 判断SKU类型：单规格或多规格
        const skuType = spu.goods_skus.length > 1 ? 2 : 1; // 1=单规格，2=多规格
        
        return {
          id: spu.id,
          mainImage: mainImage,  // 主图
          imageList: imageList,  // 所有图片URL列表
          name: spu.name,
          categoryName: category.name,
          brandName: brand.name,
          price: minPrice === maxPrice ? `¥${minPrice}` : `¥${minPrice} ~ ¥${maxPrice}`,
          status: spu.status,
          statusText: statusText,
          sales: spu.total_sales,
          services: serviceList.join(', '),
          createdAt: spu.created_at.toString(), // 直接返回毫秒时间戳
          deletedAt: spu.deleted_at ? spu.deleted_at.toString() : null, // 添加软删除字段
          skuType: skuType, // 添加SKU类型
          skus: processedSkus
        };
      });
      
      const items = await Promise.all(itemsPromises);
      
      return { total, items };
    } catch (error) {
      console.error('获取商品列表失败:', error);
      console.error('错误堆栈:', error.stack);
      throw error;
    }
  }
  
  /**
   * 将毫秒时间戳格式化为日期时间字符串
   * @param {string} timestamp - 时间戳字符串
   * @returns {string} - 格式化后的日期时间字符串
   */
  formatTime(timestamp) {
    const date = new Date(parseInt(timestamp));
    return date.toISOString().replace('T', ' ').substring(0, 19);
  }

  /**
   * 软删除商品
   * @param {string|number} id - 商品ID
   * @returns {Promise<Object>} - 删除结果
   */
  async softDeleteGoodsSpu(id) {
    try {
      // 确保ID是BigInt类型
      const spuId = this.ensureBigInt(id);
      
      // 获取当前时间戳（毫秒）
      const currentTime = BigInt(Date.now());
      
      // 更新商品的deleted_at字段
      const result = await this.prisma.goodsSpu.update({
        where: { id: spuId },
        data: { deleted_at: currentTime }
      });

      // 同时软删除商品关联的SKU
      await this.prisma.goodsSku.updateMany({
        where: { goods_spu_id: spuId },
        data: { deleted_at: currentTime }
      });

      return result;
    } catch (error) {
      console.error(`软删除商品ID ${id} 失败:`, error);
      throw error;
    }
  }

  /**
   * 恢复已删除的商品
   * @param {string|number} id - 商品ID
   * @returns {Promise<Object>} - 恢复结果
   */
  async restoreGoodsSpu(id) {
    try {
      // 确保ID是BigInt类型
      const spuId = this.ensureBigInt(id);
      
      // 将商品的deleted_at字段设为null
      const result = await this.prisma.goodsSpu.update({
        where: { id: spuId },
        data: { deleted_at: null }
      });

      // 同时恢复商品关联的SKU
      await this.prisma.goodsSku.updateMany({
        where: { goods_spu_id: spuId },
        data: { deleted_at: null }
      });

      return result;
    } catch (error) {
      console.error(`恢复商品ID ${id} 失败:`, error);
      throw error;
    }
  }

  /**
   * 永久删除商品（物理删除）
   * @param {string|number} id - 商品ID
   * @returns {Promise<Object>} - 删除结果
   */
  async permanentDeleteGoodsSpu(id) {
    try {
      // 确保ID是BigInt类型
      const spuId = this.ensureBigInt(id);
      
      // 开始事务
      return await this.prisma.$transaction(async (tx) => {
        // 先删除所有关联数据
        
        // 1. 删除SKU规格值关联
        await tx.goodsSkuSpecificationValue.deleteMany({
          where: {
            goods_sku: {
              goods_spu_id: spuId
            }
          }
        });
        
        // 2. 删除SKU图片
        await tx.goodsImage.deleteMany({
          where: {
            goods_sku: {
              goods_spu_id: spuId
            }
          }
        });
        
        // 3. 删除SKU
        await tx.goodsSku.deleteMany({
          where: { goods_spu_id: spuId }
        });
        
        // 4. 删除SPU图片
        await tx.goodsImage.deleteMany({
          where: {
            goods_spu_id: spuId,
            goods_sku_id: null
          }
        });
        
        // 5. 删除SPU视频
        await tx.goodsVideo.deleteMany({
          where: { goods_spu_id: spuId }
        });
        
        // 6. 删除分类关联
        await tx.goodsCategoryAssociation.deleteMany({
          where: { goods_spu_id: spuId }
        });
        
        // 7. 删除标签关联
        await tx.goodsTagAssociation.deleteMany({
          where: { goods_spu_id: spuId }
        });
        
        // 8. 删除服务关联
        await tx.goodsServiceAssociation.deleteMany({
          where: { goods_spu_id: spuId }
        });
        
        // 9. 删除属性值
        await tx.goodsAttributeValue.deleteMany({
          where: { goods_spu_id: spuId }
        });
        
        // 10. 最后删除SPU
        const result = await tx.goodsSpu.delete({
          where: { id: spuId }
        });
        
        return result;
      });
    } catch (error) {
      console.error(`永久删除商品ID ${id} 失败:`, error);
      throw error;
    }
  }
}

module.exports = GoodsSpuModel;
