/**
 * 订单项模型
 */
const prismaManager = require('../../../../core/prisma');
const { generateSnowflakeId } = require('../../../../shared/utils/snowflake');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 将下划线命名法转换为驼峰命名法
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const convertKeysToCamelCase = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data !== 'object' || data instanceof Date) return data;
  
  if (Array.isArray(data)) {
    return data.map(item => convertKeysToCamelCase(item));
  }
  
  const newData = {};
  for (const key in data) {
    const camelCaseKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    newData[camelCaseKey] = convertKeysToCamelCase(data[key]);
  }
  return newData;
};

class OrderItemModel {
  /**
   * 构造函数
   */
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 确保ID是BigInt类型
   * @param {string|number|BigInt} id - 需要转换的ID
   * @returns {BigInt} - 转换后的BigInt类型ID
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    return BigInt(id);
  }

  /**
   * 根据订单ID获取订单项列表
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Array>} - 订单项列表
   */
  async getOrderItemsByOrderId(orderId) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);

      const orderItems = await this.prisma.order_items.findMany({
        where: {
          order_id: orderIdBigInt
        }
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedItems = handleBigInt(orderItems);
      return convertKeysToCamelCase(processedItems);
    } catch (error) {
      console.error('获取订单项列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取订单项详情
   * @param {string|number|BigInt} id - 订单项ID
   * @returns {Promise<Object>} - 订单项详情
   */
  async getOrderItemById(id) {
    try {
      const itemId = this.ensureBigInt(id);

      const orderItem = await this.prisma.order_items.findUnique({
        where: {
          id: itemId
        }
      });

      if (!orderItem) {
        return null;
      }

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(orderItem);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('获取订单项详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建订单项
   * @param {Object} itemData - 订单项数据
   * @returns {Promise<Object>} - 创建的订单项
   */
  async createOrderItem(itemData) {
    try {
      // 生成雪花ID
      const itemId = generateSnowflakeId();
      
      // 创建订单项
      const newOrderItem = await this.prisma.order_items.create({
        data: {
          id: itemId,
          order_id: this.ensureBigInt(itemData.orderId),
          goods_spu_id: itemData.goodsSpuId ? this.ensureBigInt(itemData.goodsSpuId) : null,
          goods_sku_id: this.ensureBigInt(itemData.goodsSkuId),
          spu_code_snapshot: itemData.spuCodeSnapshot,
          spu_name_snapshot: itemData.spuNameSnapshot,
          product_name: itemData.productName,
          sku_code: itemData.skuCode,
          sku_specifications: itemData.skuSpecifications,
          product_image: itemData.productImage,
          unit_price: itemData.unitPrice,
          market_price_snapshot: itemData.marketPriceSnapshot,
          cost_price_snapshot: itemData.costPriceSnapshot,
          weight_snapshot: itemData.weightSnapshot,
          volume_snapshot: itemData.volumeSnapshot,
          third_party_spu_id: itemData.thirdPartySpuId,
          third_party_sku_id: itemData.thirdPartySkuId,
          third_party_product_code: itemData.thirdPartyProductCode,
          third_party_item_snapshot: itemData.thirdPartyItemSnapshot,
          quantity: itemData.quantity || 1,
          total_price: itemData.totalPrice,
          item_paid_amount: itemData.itemPaidAmount
        }
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(newOrderItem);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('创建订单项失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建订单项
   * @param {Array} itemsData - 订单项数据数组
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<boolean>} - 是否创建成功
   */
  async createOrderItems(itemsData, tx) {
    try {
      const prismaClient = tx || this.prisma;
      
      // 准备订单项数据
      const orderItemsData = itemsData.map(item => {
        
        return {
          id: generateSnowflakeId(),
          // 确保订单ID类型一致，先将其转换为字符串，再转换为BigInt
          order_id: BigInt(item.orderId.toString()),
          goods_spu_id: item.goodsSpuId ? BigInt(item.goodsSpuId.toString()) : null,
          // 对于第三方商品，如果没有 goodsSkuId，直接设置为 null
          goods_sku_id: item.goodsSkuId ? BigInt(item.goodsSkuId.toString()) : null,
          spu_code_snapshot: item.spuCodeSnapshot,
          spu_name_snapshot: item.spuNameSnapshot,
          product_name: item.productName,
          sku_code: item.skuCode,
          sku_specifications: item.skuSpecifications,
          product_image: item.productImage,
          unit_price: item.unitPrice,
          market_price_snapshot: item.marketPriceSnapshot,
          cost_price_snapshot: item.costPriceSnapshot,
          weight_snapshot: item.weightSnapshot,
          volume_snapshot: item.volumeSnapshot,
          third_party_spu_id: item.thirdPartySpuId,
          third_party_sku_id: item.thirdPartySkuId,
          third_party_product_code: item.thirdPartyProductCode,
          third_party_item_snapshot: item.thirdPartyItemSnapshot,
          quantity: item.quantity || 1,
          total_price: item.totalPrice,
          item_paid_amount: item.itemPaidAmount
        };
      });

      // 批量创建订单项
      await prismaClient.order_items.createMany({
        data: orderItemsData
      });

      return true;
    } catch (error) {
      console.error('批量创建订单项失败:', error);
      throw error;
    }
  }

  /**
   * 更新订单项
   * @param {string|number|BigInt} id - 订单项ID
   * @param {Object} itemData - 订单项数据
   * @returns {Promise<Object>} - 更新后的订单项
   */
  async updateOrderItem(id, itemData) {
    try {
      const itemId = this.ensureBigInt(id);

      // 更新订单项
      const updatedOrderItem = await this.prisma.order_items.update({
        where: {
          id: itemId
        },
        data: {
          product_name: itemData.productName,
          quantity: itemData.quantity,
          unit_price: itemData.unitPrice,
          total_price: itemData.totalPrice,
          item_paid_amount: itemData.itemPaidAmount,
          updated_at: BigInt(Date.now())
        }
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(updatedOrderItem);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('更新订单项失败:', error);
      throw error;
    }
  }
}

module.exports = OrderItemModel;
