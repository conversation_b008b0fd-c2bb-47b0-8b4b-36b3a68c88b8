/**
 * 订单跟单员模型
 */
const { prisma } = require('../../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../../shared/utils/snowflake');
const { snakeToCamel } = require('../../../../shared/utils/format');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 递归处理数据，将所有层级的下划线命名转换为驼峰命名
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const recursiveSnakeToCamel = (data) => {
  if (data === null || data === undefined) return data;
  
  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => recursiveSnakeToCamel(item));
  }
  
  // 处理对象，排除Date类型
  if (typeof data === 'object' && !(data instanceof Date)) {
    // 先使用现有的snakeToCamel函数处理当前层级
    const camelData = snakeToCamel(data);
    
    // 然后递归处理每个属性
    const result = {};
    for (const key in camelData) {
      result[key] = recursiveSnakeToCamel(camelData[key]);
    }
    
    return result;
  }
  
  // 其他类型直接返回
  return data;
};

class OrderFollowerModel {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 获取订单的跟单员列表
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Array>} - 跟单员列表
   */
  async getOrderFollowers(orderId) {
    try {
      const orderIdBigInt = BigInt(orderId);
      
      // 使用原生 SQL 查询关联system_user表获取用户名
      const followersWithUsername = await this.prisma.$queryRaw`
        SELECT 
          f.id, 
          f.order_id, 
          f.follower_id, 
          f.created_at, 
          f.updated_at, 
          f.deleted_at, 
          f.created_by, 
          f.updated_by, 
          f.deleted_by,
          u.username as follower_name
        FROM 
          base.order_followers f
        LEFT JOIN 
          base.system_user u ON f.follower_id = u.id
        WHERE 
          f.order_id = ${orderIdBigInt} AND 
          f.deleted_at IS NULL
        ORDER BY 
          f.created_at ASC
      `;

      // 先处理BigInt，再转换为驼峰命名法
      const processedData = handleBigInt(followersWithUsername);
      return recursiveSnakeToCamel(processedData);
    } catch (error) {
      console.error('获取订单跟单员列表失败:', error);
      throw error;
    }
  }

  /**
   * 添加订单跟单员
   * @param {Object} data - 跟单员数据
   * @param {string|number|BigInt} data.orderId - 订单ID
   * @param {string|number|BigInt} data.followerId - 跟单员ID
   * @param {string|number|BigInt} data.createdBy - 创建人ID
   * @returns {Promise<Object>} - 创建的跟单员关联记录
   */
  async addOrderFollower(data) {
    try {
      const { orderId, followerId, createdBy } = data;
      

      
      // 检查是否已存在相同的关联（未删除的）
      const existingFollower = await this.prisma.order_followers.findFirst({
        where: {
          order_id: BigInt(orderId),
          follower_id: BigInt(followerId),
          deleted_at: null
        }
      });



      // 如果已存在且未删除，则直接返回
      if (existingFollower) {

        const processedData = handleBigInt(existingFollower);
        return recursiveSnakeToCamel(processedData);
      }

      // 查找已删除的记录
      const deletedFollower = await this.prisma.order_followers.findFirst({
        where: {
          order_id: BigInt(orderId),
          follower_id: BigInt(followerId),
          deleted_at: { not: null }
        }
      });


      if (deletedFollower) {

      }

      if (deletedFollower) {
        // 恢复已删除的记录

        const now = BigInt(Date.now());

        
        const restoredFollower = await this.prisma.order_followers.update({
          where: {
            id: deletedFollower.id
          },
          data: {
            deleted_at: null,
            updated_at: now,
            updated_by: createdBy && !isNaN(Number(createdBy)) ? BigInt(createdBy) : null
          }
        });
        

        if (restoredFollower) {

        }
        
        // 先处理BigInt，再转换为驼峰命名法
        const processedData = handleBigInt(restoredFollower);
        return recursiveSnakeToCamel(processedData);
      }

      // 创建新记录

      const newId = generateSnowflakeId();

      const now = BigInt(Date.now());

      
      const newFollower = await this.prisma.order_followers.create({
        data: {
          id: newId,
          order_id: BigInt(orderId),
          follower_id: BigInt(followerId),
          created_at: now,
          updated_at: now,
          created_by: createdBy && !isNaN(Number(createdBy)) ? BigInt(createdBy) : null,
          updated_by: createdBy && !isNaN(Number(createdBy)) ? BigInt(createdBy) : null
        }
      });


      if (newFollower) {

      }

      // 先处理BigInt，再转换为驼峰命名法
      const processedData = handleBigInt(newFollower);
      return recursiveSnakeToCamel(processedData);
    } catch (error) {

      throw error;
    }
  }

  /**
   * 删除订单跟单员
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {string|number|BigInt} followerId - 跟单员ID
   * @param {string|number|BigInt} deletedBy - 删除人ID
   * @returns {Promise<Object>} - 删除结果
   */
  async removeOrderFollower(orderId, followerId, deletedBy) {
    try {

      
      const orderIdBigInt = BigInt(orderId);
      const followerIdBigInt = BigInt(followerId);
      
      // 查找要删除的记录
      const follower = await this.prisma.order_followers.findFirst({
        where: {
          order_id: orderIdBigInt,
          follower_id: followerIdBigInt,
          deleted_at: null
        }
      });


      if (follower) {

      }

      if (!follower) {
        const errorMsg = `订单(ID: ${orderId})没有ID为${followerId}的跟单员`;

        throw new Error(errorMsg);
      }

      // 软删除记录

      const now = BigInt(Date.now());

      
      const deletedFollower = await this.prisma.order_followers.update({
        where: {
          id: follower.id
        },
        data: {
          deleted_at: now,
          updated_at: now,
          deleted_by: deletedBy && !isNaN(Number(deletedBy)) ? BigInt(deletedBy) : null
        }
      });


      if (deletedFollower) {

      }

      // 先处理BigInt，再转换为驼峰命名法
      const processedData = handleBigInt(deletedFollower);
      return recursiveSnakeToCamel(processedData);
    } catch (error) {

      throw error;
    }
  }

}

module.exports = OrderFollowerModel;
