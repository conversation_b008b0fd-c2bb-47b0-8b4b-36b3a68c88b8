/**
 * 订单配送信息模型
 */
const prismaManager = require('../../../../core/prisma');
const { generateSnowflakeId } = require('../../../../shared/utils/snowflake');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 将下划线命名法转换为驼峰命名法
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const convertKeysToCamelCase = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data !== 'object' || data instanceof Date) return data;
  
  if (Array.isArray(data)) {
    return data.map(item => convertKeysToCamelCase(item));
  }
  
  const newData = {};
  for (const key in data) {
    const camelCaseKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    newData[camelCaseKey] = convertKeysToCamelCase(data[key]);
  }
  return newData;
};

class OrderShippingInfoModel {
  
  /**
   * 构造函数
   */
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 确保ID是BigInt类型
   * @param {string|number|BigInt} id - 需要转换的ID
   * @returns {BigInt} - 转换后的BigInt类型ID
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    return BigInt(id);
  }

  /**
   * 根据订单ID获取配送信息
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object>} - 配送信息
   */
  async getShippingInfoByOrderId(orderId) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);

      const shippingInfo = await this.prisma.order_shipping_info.findUnique({
        where: {
          order_id: orderIdBigInt
        }
      });

      if (!shippingInfo) {
        return null;
      }

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(shippingInfo);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('获取订单配送信息失败:', error);
      throw error;
    }
  }

  /**
   * 创建订单配送信息
   * @param {Object} shippingData - 配送信息数据
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<Object>} - 创建的配送信息
   */
  async createShippingInfo(shippingData, tx) {
    try {
      const prismaClient = tx || this.prisma;
      
      // 生成雪花ID
      const shippingId = generateSnowflakeId();
      
      // 创建配送信息
      const newShippingInfo = await prismaClient.order_shipping_info.create({
        data: {
          id: shippingId,
          orders: {
            connect: {
              id: BigInt(shippingData.orderId.toString())
            }
          },
          recipient_name: shippingData.recipientName,
          recipient_phone: shippingData.recipientPhone,
          region_province_id: shippingData.regionProvinceId,
          region_city_id: shippingData.regionCityId,
          region_district_id: shippingData.regionDistrictId,
          region_path_name: shippingData.regionPathName,
          street_address: shippingData.streetAddress,
          postal_code: shippingData.postalCode
          // 已移除物流相关字段
        }
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(newShippingInfo);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('创建订单配送信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新订单配送信息
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} shippingData - 配送信息数据
   * @returns {Promise<Object>} - 更新后的配送信息
   */
  async updateShippingInfo(orderId, shippingData) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);

      // 更新配送信息
      // 根据order_id查找shipping_info
      const shippingInfo = await this.prisma.order_shipping_info.findFirst({
        where: {
          order_id: orderIdBigInt
        }
      });
      
      if (!shippingInfo) {
        throw new Error(`未找到订单ID为${orderId}的配送信息`);
      }
      
      // 使用shipping_info的id进行更新
      const updatedShippingInfo = await this.prisma.order_shipping_info.update({
        where: {
          id: shippingInfo.id
        },
        data: {
          recipient_name: shippingData.recipientName,
          recipient_phone: shippingData.recipientPhone,
          region_province_id: shippingData.regionProvinceId,
          region_city_id: shippingData.regionCityId,
          region_district_id: shippingData.regionDistrictId,
          region_path_name: shippingData.regionPathName,
          street_address: shippingData.streetAddress,
          postal_code: shippingData.postalCode,
          // 移除物流相关字段
          updated_at: BigInt(Date.now())
        }
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(updatedShippingInfo);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('更新订单配送信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新物流信息
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Object} logisticsData - 物流信息数据
   * @returns {Promise<Object>} - 更新后的配送信息
   */
  async updateLogisticsInfo(orderId, logisticsData) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);

      // 更新物流信息
      const updateData = {
        shipping_company_code: logisticsData.shippingCompanyCode,
        shipping_company_name: logisticsData.shippingCompanyName,
        tracking_number: logisticsData.trackingNumber,
        updated_at: BigInt(Date.now())
      };
      
      // 如果提供了配送方式，添加到更新数据中
      if (logisticsData.shippingMethod !== undefined) {
        updateData.shipping_method = logisticsData.shippingMethod;
      }
      
      // 如果提供了附件图片URL，添加到更新数据中
      if (logisticsData.imagesUrl) {
        updateData.images_url = logisticsData.imagesUrl;
      }
      
      // 更新物流信息
      // 根据order_id查找shipping_info
      const shippingInfo = await this.prisma.order_shipping_info.findFirst({
        where: {
          order_id: orderIdBigInt
        }
      });
      
      if (!shippingInfo) {
        throw new Error(`未找到订单ID为${orderId}的配送信息`);
      }
      
      // 使用shipping_info的id进行更新
      const updatedShippingInfo = await this.prisma.order_shipping_info.update({
        where: {
          id: shippingInfo.id
        },
        data: updateData
      });

      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(updatedShippingInfo);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('更新物流信息失败:', error);
      throw error;
    }
  }
}

module.exports = OrderShippingInfoModel;
