const prismaManager = require('../../../core/prisma');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    return Number(data);
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = handleBigInt(data[key]);
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 商品视频模型
 */
class GoodsVideoModel {
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 根据SPU ID获取商品视频
   * @param {string|number} spuId 商品SPU ID
   * @returns {Promise<Object>} 商品视频
   */
  async getVideoBySpuId(spuId) {
    try {
      const video = await this.prisma.goodsVideo.findFirst({
        where: {
          goods_spu_id: BigInt(spuId)
        }
      });
      
      return video ? convertKeysToCamelCase(handleBigInt(video)) : null;
    } catch (error) {
      console.error(`获取商品SPU(ID:${spuId})的视频失败:`, error);
      throw error;
    }
  }

  /**
   * 添加商品视频
   * @param {Object} videoData 视频数据
   * @returns {Promise<Object>} 添加的视频
   */
  async addVideo(videoData) {
    try {
      const video = await this.prisma.goodsVideo.create({
        data: {
          goods_spu_id: BigInt(videoData.goodsSpuId),
          video_url: videoData.videoUrl,
          cover_image_url: videoData.coverImageUrl || null,
          title: videoData.title || null,
          duration: videoData.duration || null
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(video));
    } catch (error) {
      console.error('添加商品视频失败:', error);
      throw error;
    }
  }

  /**
   * 更新商品视频
   * @param {string|number} id 视频ID
   * @param {Object} videoData 视频数据
   * @returns {Promise<Object>} 更新后的视频
   */
  async updateVideo(id, videoData) {
    try {
      const video = await this.prisma.goodsVideo.update({
        where: {
          id: BigInt(id)
        },
        data: {
          video_url: videoData.videoUrl,
          cover_image_url: videoData.coverImageUrl,
          title: videoData.title,
          duration: videoData.duration
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(video));
    } catch (error) {
      console.error(`更新商品视频(ID:${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 删除商品视频
   * @param {string|number} id 视频ID
   * @returns {Promise<void>}
   */
  async deleteVideo(id) {
    try {
      await this.prisma.goodsVideo.delete({
        where: {
          id: BigInt(id)
        }
      });
    } catch (error) {
      console.error(`删除商品视频(ID:${id})失败:`, error);
      throw error;
    }
  }
}

module.exports = new GoodsVideoModel();
