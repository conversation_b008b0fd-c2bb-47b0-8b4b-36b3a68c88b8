/**
 * 支付方式模型
 */
const { prisma } = require('../../../core/database/prisma');

class PaymentMethodModel {
  /**
   * 获取所有支付方式
   * @returns {Promise<Array>} 支付方式列表
   */
  static async getAllEnabledMethods() {
    try {
      return await prisma.PaymentMethods.findMany({
        where: {
          deleted_at: null
        },
        select: {
          id: true,
          name: true,
          created_at: true,
          updated_at: true,
          created_by: true,
          updated_by: true
        }
      });
    } catch (error) {
      console.error('获取支付方式列表失败:', error);
      throw new Error('获取支付方式列表失败');
    }
  }
}

module.exports = PaymentMethodModel;
