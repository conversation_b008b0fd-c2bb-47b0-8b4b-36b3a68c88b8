const { prisma } = require('../../../core/database/prisma');
const { recursiveSnakeToCamel } = require('../../../shared/utils/format');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 发票记录模型
 * 处理已开具的发票记录的创建、查询、更新等操作
 */
class InvoiceModel {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 确保 BigInt 类型
   * @param {string|number|BigInt} id
   * @returns {BigInt}
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    if (typeof id === 'string' || typeof id === 'number') {
      return BigInt(id);
    }
    throw new Error('Invalid ID type');
  }

  /**
   * 创建发票记录
   * @param {Object} invoiceData - 发票数据
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<Object>} 创建的发票信息
   */
  async createInvoice(invoiceData, tx = null) {
    try {
      console.log(`[InvoiceModel] 创建发票记录:`, invoiceData);

      const prismaClient = tx || this.prisma;

      const invoice = await prismaClient.invoices.create({
        data: {
          ...invoiceData,
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });

      console.log(`[InvoiceModel] 发票记录创建成功:`, invoice);
      return recursiveSnakeToCamel(handleBigInt(invoice));
    } catch (error) {
      console.error('创建发票记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据订单ID获取发票记录
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object|null>} 发票信息
   */
  async getInvoiceByOrderId(orderId) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);
      console.log(`[InvoiceModel] 查询订单发票记录，订单ID: ${orderId}, BigInt: ${orderIdBigInt}`);

      const invoice = await this.prisma.invoices.findFirst({
        where: {
          order_id: orderIdBigInt,
          deleted_at: null
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      console.log(`[InvoiceModel] 查询结果:`, invoice);

      const result = invoice ? recursiveSnakeToCamel(handleBigInt(invoice)) : null;
      console.log(`[InvoiceModel] 处理后的结果:`, result);

      return result;
    } catch (error) {
      console.error('获取订单发票记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据申请ID获取发票记录
   * @param {string|number|BigInt} applicationId - 申请ID
   * @returns {Promise<Array>} 发票记录列表
   */
  async getInvoicesByApplicationId(applicationId) {
    try {
      const applicationIdBigInt = this.ensureBigInt(applicationId);

      const invoices = await this.prisma.invoices.findMany({
        where: {
          application_id: applicationIdBigInt,
          deleted_at: null
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      return recursiveSnakeToCamel(handleBigInt(invoices));
    } catch (error) {
      console.error('获取申请发票记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新发票信息
   * @param {string|number|BigInt} id - 发票ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object|null>} 更新后的发票信息
   */
  async updateInvoice(id, updateData) {
    try {
      const idBigInt = this.ensureBigInt(id);

      const invoice = await this.prisma.invoices.update({
        where: { id: idBigInt },
        data: {
          ...updateData,
          updated_at: BigInt(Date.now())
        }
      });

      return recursiveSnakeToCamel(handleBigInt(invoice));
    } catch (error) {
      console.error('更新发票信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新发票状态
   * @param {string|number|BigInt} id - 发票ID
   * @param {number} status - 发票状态
   * @param {Object} additionalData - 额外更新数据
   * @returns {Promise<Object|null>} 更新后的发票信息
   */
  async updateInvoiceStatus(id, status, additionalData = {}) {
    try {
      const idBigInt = this.ensureBigInt(id);

      const updateData = {
        invoice_status: status,
        updated_at: BigInt(Date.now()),
        ...additionalData
      };

      const invoice = await this.prisma.invoices.update({
        where: { id: idBigInt },
        data: updateData
      });

      return recursiveSnakeToCamel(handleBigInt(invoice));
    } catch (error) {
      console.error('更新发票状态失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取发票详情
   * @param {string|number|BigInt} id - 发票ID
   * @returns {Promise<Object|null>} 发票详情
   */
  async getInvoiceById(id) {
    try {
      const idBigInt = this.ensureBigInt(id);

      const invoice = await this.prisma.invoices.findUnique({
        where: { id: idBigInt }
      });

      return invoice ? recursiveSnakeToCamel(handleBigInt(invoice)) : null;
    } catch (error) {
      console.error('获取发票详情失败:', error);
      throw error;
    }
  }

  /**
   * 作废发票
   * @param {string|number|BigInt} id - 发票ID
   * @param {string} reason - 作废原因
   * @param {string|number|BigInt} voidedBy - 作废人ID
   * @returns {Promise<Object>} 作废后的发票信息
   */
  async voidInvoice(id, reason, voidedBy) {
    try {
      const idBigInt = this.ensureBigInt(id);
      const voidedByBigInt = this.ensureBigInt(voidedBy);

      const invoice = await this.prisma.invoices.update({
        where: { id: idBigInt },
        data: {
          invoice_status: 2, // 已作废
          void_reason: reason,
          voided_by: voidedByBigInt,
          voided_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });

      return recursiveSnakeToCamel(handleBigInt(invoice));
    } catch (error) {
      console.error('作废发票失败:', error);
      throw error;
    }
  }

  /**
   * 软删除发票
   * @param {string|number|BigInt} id - 发票ID
   * @returns {Promise<Object>} 删除后的发票信息
   */
  async deleteInvoice(id) {
    try {
      const idBigInt = this.ensureBigInt(id);

      const invoice = await this.prisma.invoices.update({
        where: { id: idBigInt },
        data: {
          deleted_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });

      return recursiveSnakeToCamel(handleBigInt(invoice));
    } catch (error) {
      console.error('删除发票失败:', error);
      throw error;
    }
  }
}

module.exports = InvoiceModel;
