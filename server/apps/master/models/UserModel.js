const prismaManager = require('../../../core/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class MasterUser {
  constructor() {
    this.prisma = prismaManager.getClient('master');
  }

  /**
   * 处理数据返回，将 BigInt 转为字符串
   */
  processResult(data) {
    if (!data) return data;
    if (Array.isArray(data)) {
      return data.map(item => this.processResult(item));
    }
    const result = {};
    for (const [key, value] of Object.entries(data)) {
      result[key] = typeof value === 'bigint' ? value.toString() : value;
    }
    return result;
  }

  /**
   * 创建用户
   */
  async create(data) {
    const now = Date.now(); // 获取当前时间戳（毫秒）
    const result = await this.prisma.user.create({
      data: {
        id: generateSnowflakeId(), // 生或16位雪花ID
        ...data,
        created_at: now,
        updated_at: now
      }
    });
    return this.processResult(result);
  }

  /**
   * 更新用户
   */
  async update(id, data) {
    const result = await this.prisma.user.update({
      where: { id: BigInt(id) },
      data: {
        ...data,
        updated_at: Date.now(),
        updated_by: data.updated_by ? BigInt(data.updated_by) : null
      }
    });
    return this.processResult(result);
  }

  /**
   * 软删除用户
   */
  async delete(id, operator) {
    const result = await this.prisma.user.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: Date.now(),
        updated_by: operator ? BigInt(operator) : null
      }
    });
    return this.processResult(result);
  }

  /**
   * 查找单个用户（排除已删除）
   */
  async findById(id) {
    const result = await this.prisma.user.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });
    return this.processResult(result);
  }

  /**
   * 查找多个用户（分页，排除已删除）
   */
  async findMany(params = {}) {
    const { 
      page = 1, 
      pageSize = 10, 
      where = {}, 
      orderBy = { created_at: 'desc' } 
    } = params;

    // 确保排除已删除记录
    const finalWhere = {
      ...where,
      deleted_at: null
    };
    
    const skip = (page - 1) * pageSize;
    
    const [total, items] = await Promise.all([
      this.prisma.user.count({ where: finalWhere }),
      this.prisma.user.findMany({
        skip,
        take: pageSize,
        where: finalWhere,
        orderBy,
        select: {
          id: true,
          user_type: true,
          username: true,
          nickname: true,
          phone: true,
          email: true,
          avatar: true,
          dept_id: true,
          role_id: true,
          status: true,
          login_ip: true,
          login_time: true,
          remark: true,
          created_at: true,
          updated_at: true
        }
      })
    ]);

    return {
      items: this.processResult(items),
      total,
      page,
      pageSize
    };
  }

  /**
   * 通过用户名查找用户（排除已删除）
   */
  async findByUsername(username) {
    const result = await this.prisma.user.findFirst({
      where: { 
        username,
        deleted_at: null
      }
    });
    return this.processResult(result);
  }

  /**
   * 更新用户登录信息
   */
  async updateLoginInfo(id, ip, token) {
    const result = await this.prisma.user.update({
      where: { id: BigInt(id) },
      data: {
        login_ip: ip,
        login_time: Date.now(),
        login_token: token,
        updated_at: Date.now()
      }
    });
    return this.processResult(result);
  }
}

module.exports = MasterUser;
