// 使用动态导入，避免SSR问题
const encrypt = async (data, publicKey) => {
  try {
    // 动态导入jsencrypt
    const { default: JSEncrypt } = await import('jsencrypt');
    const encryptor = new JSEncrypt();
    encryptor.setPublicKey(publicKey);
  
    // 加密数据
    const encrypted = encryptor.encrypt(data);
    
    // 检查加密结果
    if (encrypted === false) {

      throw new Error('RSA加密失败：返回了false');
    }

    return encrypted;
  } catch (error) {

    throw new Error('RSA加密失败: ' + error.message);
  }
};

/**
 * RSA加密工具
 */
export default {
  /**
   * 使用RSA公钥加密数据
   * @param {string} data - 需要加密的数据
   * @param {string} publicKey - RSA公钥
   * @returns {Promise<string>} 加密后的数据
   */
  encrypt
}
