/* 首页新闻模块样式 - 与news.html保持一致 */
.home-page-container {
    position: relative;
    margin-top: 100vh;
}

.home-page-container .news {
    position: relative;
    top: 0;
}

.home-page-container .news .news-container {
    max-width: 1381px;
    margin: 0 auto;
    margin-top: -150px;
    padding: 0 15px;
}

.home-news-title {
    color: #ffffff;
    font-family: AlibabaPuHuiTi;
    font-size: 1.875rem;
    height: 5rem;
    margin: 0 auto 1.25rem;
    padding-top: 2.5rem;
    width: 100%;
}

/* 新闻网格 */
.news-grid {
    display: grid;
    grid-template-columns: repeat(2, 436px);
    gap: 22px;
    margin-bottom: 50px;
    justify-content: center;
}

/* 新闻项目 */
.news-item {
    background: #fff;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    width: 436px;
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    background-color: #0052d9;
}

.news-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

/* 第一个新闻项目的图片尺寸 */
.news-grid .news-item:first-child {
    grid-column: span 2;
    width: 894px;
}

.news-grid .news-item:first-child .news-image {
    width: 894px;
    height: 320px;
    overflow: hidden;
}

.news-grid .news-item:first-child .news-image img {
    width: 894px;
    height: 320px;
    object-fit: cover;
}

/* 其他新闻项目的图片尺寸 */
.news-image {
    width: 436px;
    height: 240px;
    overflow: hidden;
}

.news-image img {
    width: 436px;
    height: 240px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-item:hover .news-image img {
    transform: scale(1.05);
}

.news-content {
    padding: 20px;
}

.news-content h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-content p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
}

.news-date {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #999999;
}

.news-item:hover .news-content h3 {
    color: #fff;
}

.news-item:hover .news-content p {
    color: #fff;
}

.news-item:hover .news-date {
    color: #fff;
    border-top-color: rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (min-width: 1201px) and (max-width: 1350px) {
    .home-page-container .news .news-container {
        max-width: 90%;
        margin: 0 auto;
        margin-top: -150px;
    }
    
    .news-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .news-grid .news-item:first-child {
        width: 100%;
    }
    
    .news-grid .news-item:first-child .news-image {
        width: 100%;
        height: 280px;
    }
    
    .news-grid .news-item:first-child .news-image img {
        width: 100%;
        height: 280px;
    }
    
    .news-image {
        width: 100%;
        height: 240px;
    }
    
    .news-image img {
        width: 100%;
        height: 240px;
    }
    
    .news-item {
        width: 100%;
    }
}

@media (max-width: 1200px) {
    .home-page-container .news .news-container {
        max-width: 90%;
        margin: 0 auto;
        margin-top: -150px;
    }
    
    .news-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .news-grid .news-item:first-child {
        width: 100%;
    }
    
    .news-grid .news-item:first-child .news-image {
        width: 100%;
        height: 260px;
    }
    
    .news-grid .news-item:first-child .news-image img {
        width: 100%;
        height: 260px;
    }
    
    .news-image {
        width: 100%;
        height: 220px;
    }
    
    .news-image img {
        width: 100%;
        height: 220px;
    }
    
    .news-item {
        width: 100%;
    }
}

@media (max-width: 992px) {
    .home-page-container .news .news-container {
        max-width: 95%;
        margin: 0 auto;
        margin-top: -150px;
        padding: 35px 0;
    }
    
    .news-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .news-grid .news-item:first-child .news-image {
        height: 220px;
    }
    
    .news-grid .news-item:first-child .news-image img {
        height: 220px;
    }
    
    .news-image {
        height: 180px;
    }
    
    .news-image img {
        height: 180px;
    }
    
    .news-content {
        padding: 15px;
    }
    
    .news-content h3 {
        font-size: 16px;
    }
    
    .news-content p {
        font-size: 13px;
    }
    
    .news-date {
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .home-page-container {
        margin-top: 100vh;
    }
    
    .home-page-container .news .news-container {
        margin-top: -80px;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
    }
    
    .news-grid .news-item:first-child {
        grid-column: span 1;
    }
    
    .news-grid .news-item:first-child .news-image {
        width: 100%;
        height: 220px;
    }
    
    .news-grid .news-item:first-child .news-image img {
        width: 100%;
        height: 220px;
    }
    
    .news-item {
        width: 100%;
    }
    
    .news-image {
        width: 100%;
        height: 220px;
    }
    
    .news-image img {
        width: 100%;
        height: 220px;
    }
    
    /* 在移动端隐藏新闻标题 */
    .home-news-title {
        display: none;
    }
}

@media (max-width: 576px) {
    .home-page-container {
        margin-top: 95vh;
    }
    
    .home-page-container .news .news-container {
        margin-top: -60px;
        padding: 25px 10px;
    }
    
    .home-news-title {
        display: none;
    }
    
    .news-grid .news-item:first-child .news-image {
        height: 180px;
    }
    
    .news-grid .news-item:first-child .news-image img {
        height: 180px;
    }
    
    .news-image {
        height: 180px;
    }
    
    .news-image img {
        height: 180px;
    }
    
    .news-content {
        padding: 12px;
    }
    
    .news-content h3 {
        font-size: 15px;
        margin-bottom: 8px;
    }
    
    .news-content p {
        font-size: 12px;
        margin-bottom: 10px;
    }
    
    .news-date {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .home-page-container {
        margin-top: 90vh;
    }
    
    .home-page-container .news .news-container {
        margin-top: -40px;
    }
    
    .home-news-title {
        display: none;
    }
    
    .news-grid .news-item:first-child .news-image {
        height: 160px;
    }
    
    .news-grid .news-item:first-child .news-image img {
        height: 160px;
    }
    
    .news-image {
        height: 160px;
    }
    
    .news-image img {
        height: 160px;
    }
}
