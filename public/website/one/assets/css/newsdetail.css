/* 面包屑导航样式 */
.breadcrumb-container {
    background-color: #f8f9fa;
    padding: 15px 0;
    margin-top: 56px;
}

.breadcrumb {
    margin-bottom: 0;
}

.breadcrumb-item a {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 15px;
    color: #333333;
    line-height: 21px;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #333333;
}

/* 新闻详情内容样式 */
.news-detail-container {
    background-color: white;
    padding: 40px 0 60px;
}

.news-detail-content {
    max-width: 1200px;
    margin: 0 auto;
    /* padding: 0 15px; */
}

.news-title {
    font-family: PingFangSC, PingFang SC;
font-weight: 400;
font-size: 46px;
color: #333333;
line-height: 65px;
    margin-bottom: 20px;

}

.news-meta {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e6e6e6;
}

.news-date {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #999999;
    line-height: 22px;
}

.news-text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 32px;
}

.news-text p {
    font-size: 16px;
    margin-bottom: 20px;
}

.news-image {
    margin: 30px 0;
    text-align: center;
}

.news-image img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .news-detail-content {
        padding: 0 20px;
    }

    .news-title {
        font-size: 20px;
    }

    .news-text p {
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .breadcrumb-container {
        padding: 10px 0;
    }

    .news-detail-container {
        padding: 30px 0;
    }

    .news-title {
        font-size: 18px;
    }
}