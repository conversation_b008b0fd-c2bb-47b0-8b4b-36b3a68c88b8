/* 加入我们页面样式 */
.joinus-banner{
    position: relative;
    width: 100%;
    height: 450px;
    background-image: url('../images/news-bg.png');
    background-size: cover;
    background-position: center;
    margin-top: 56px;
    display: flex;
    align-items: center;
}

.joinus-banner-title {
    color: white;
    font-weight: 500;
    font-size: 36px;
    margin-left: 283px;
}

.joinus-tabs {
    background-color: white;
    border-bottom: 1px solid #e1e5eb;
}

.joinus-tabs .nav-link {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    padding: 1rem 2rem;
    border: none;
    border-bottom: 2px solid transparent;
}

.joinus-tabs .nav-link.active {
    border-bottom: 2px solid #0052d9;
    background-color: transparent;
}

/* 新闻横幅 */
.news-banner {
    background: #f5f5f5;
    text-align: center;
    padding: 120px 0 60px;
}

.news-banner h1 {
    font-size: 2.5rem;
    color: #333;
    margin: 0;
}

/* 新闻容器 */
.news-container {
    max-width: 894px;
    margin: 0 auto;
    padding: 50px 15px;
}

/* 新闻网格 */
.news-grid {
    display: grid;
    grid-template-columns: repeat(2, 436px);
    gap: 22px;
    margin-bottom: 50px;
    justify-content: center;
}

/* 新闻项目 */
.news-item {
    background: #fff;
    /* border-radius: 10px; */
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    width: 436px;
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    background-color: #0052d9;
}

.news-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

/* 第一个新闻项目的图片尺寸 */
.news-grid .news-item:first-child {
    grid-column: span 2;
    width: 894px;
}

.news-grid .news-item:first-child .news-image {
    width: 894px;
    height: 320px;
    overflow: hidden;
}

.news-grid .news-item:first-child .news-image img {
    width: 894px;
    height: 320px;
    object-fit: cover;
}

/* 其他新闻项目的图片尺寸 */
.news-image {
    width: 436px;
    height: 240px;
    overflow: hidden;
}

.news-image img {
    width: 436px;
    height: 240px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-item:hover .news-image img {
    transform: scale(1.05);
}

.news-content {
    padding: 20px;
}

.news-content h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-content p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
}

.news-date {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #999999;
}

.news-item:hover .news-content h3 {
    color: #fff;
}

.news-item:hover .news-content p {
    color: #fff;
}

.news-item:hover .news-date {
    color: #fff;
    border-top-color: rgba(255, 255, 255, 0.2);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.pagination ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 10px;
    flex-wrap: wrap;
}

.pagination li {
    display: inline-block;
}

.pagination a,
.pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    padding: 0 12px;
    border-radius: 4px;
    background: #fff;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.pagination a:hover {
    background: #f5f5f5;
    border-color: #d0d0d0;
}


.pagination a.active {
    background: #0052d9;
    color: #fff;
    border-color: #0052d9;
}

.pagination span {
    color: #999;
    background: #f5f5f5;
}

/* 导航栏占位符样式，确保在移动端不被覆盖 */
#navbar-placeholder {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1031;
}

/* 大屏幕平板 (1201px - 1350px) */
@media (min-width: 1201px) and (max-width: 1350px) {
    .news-container {
        max-width: 90%;
        margin: 0 auto;
    }
    
    .news-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        justify-content: flex-start;
    }
    
    .news-item {
        flex: 0 0 calc(50% - 30px);
        max-width: calc(50% - 30px);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .news-grid .news-item:first-child {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .news-grid .news-item:last-child:nth-child(odd) {
        margin-right: auto;
    }
    
    .news-item:hover {
        transform: translateY(-8px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        background-color: #0052d9;
    }
    
    /* 确保所有新闻项目图片大小一致 */
    .news-image {
        height: 240px;
        overflow: hidden;
    }
    
    .news-grid .news-item:first-child .news-image {
        height: 280px;
    }
    
    .news-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .news-item:hover .news-image img {
        transform: scale(1.05);
    }
    
    .news-content {
        padding: 24px;
        background: #fff;
        position: relative;
    }
    
    .news-content h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 14px;
        color: #222;
        transition: color 0.3s ease;
    }
    
    .news-content p {
        font-size: 15px;
        margin-bottom: 20px;
        line-height: 1.6;
        color: #555;
    }
    
    .news-date {
        font-size: 15px;
        color: #777;
        display: inline-block;
        padding: 6px 0;
        border-top: 1px solid #eee;
    }
}

/* 中等屏幕平板 (992px - 1200px) */
@media (min-width: 992px) and (max-width: 1200px) {
    .news-container {
        max-width: 90%;
        margin: 0 auto;
    }
    
    .news-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 25px;
        justify-content: flex-start;
    }
    
    .news-item {
        flex: 0 0 calc(50% - 25px);
        max-width: calc(50% - 25px);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .news-grid .news-item:first-child {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .news-grid .news-item:last-child:nth-child(odd) {
        margin-right: auto;
    }
    
    .news-item:hover {
        transform: translateY(-8px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        background-color: #0052d9;
    }
    
    /* 确保所有新闻项目图片大小一致 */
    .news-image {
        height: 220px;
        overflow: hidden;
    }
    
    .news-grid .news-item:first-child .news-image {
        height: 260px;
    }
    
    .news-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .news-item:hover .news-image img {
        transform: scale(1.05);
    }
    
    .news-content {
        padding: 22px;
        background: #fff;
        position: relative;
    }
    
    .news-content h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #222;
        transition: color 0.3s ease;
    }
    
    .news-content p {
        font-size: 14px;
        margin-bottom: 18px;
        line-height: 1.6;
        color: #555;
    }
    
    .news-date {
        font-size: 14px;
        color: #777;
        display: inline-block;
        padding: 5px 0;
        border-top: 1px solid #eee;
    }
}

/* 小屏幕平板 (768px - 992px) */
@media (min-width: 768px) and (max-width: 992px) {
    .news-container {
        max-width: 95%;
        margin: 0 auto;
        padding: 35px 0;
    }
    
    .news-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        justify-content: flex-start;
    }
    
    .news-item {
        flex: 0 0 calc(50% - 20px);
        max-width: calc(50% - 20px);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .news-grid .news-item:first-child {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .news-grid .news-item:last-child:nth-child(odd) {
        margin-right: auto;
    }
    
    .news-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        background-color: #0052d9;
    }
    
    /* 确保所有新闻项目图片大小一致 */
    .news-image {
        height: 180px;
        overflow: hidden;
    }
    
    .news-grid .news-item:first-child .news-image {
        height: 220px;
    }
    
    .news-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .news-item:hover .news-image img {
        transform: scale(1.05);
    }
    
    .news-content {
        padding: 18px;
        background: #fff;
        position: relative;
    }
    
    .news-content h3 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #222;
        transition: color 0.3s ease;
    }
    
    .news-content p {
        font-size: 13px;
        margin-bottom: 15px;
        line-height: 1.5;
        color: #555;
    }
    
    .news-date {
        font-size: 13px;
        color: #777;
        display: inline-block;
        padding: 4px 0;
        border-top: 1px solid #eee;
    }
    
    .joinus-tabs .nav-link {
        padding: 0.8rem 1.5rem;
        font-size: 15px;
    }
}

/* 手机屏幕 (576px - 768px) */
@media (max-width: 768px) {    
    .joinus-banner {
        height: 250px;
    }
    
    .joinus-banner-title {
        margin-left: 30px;
        font-size: 26px;
    }
    
    .news-container {
        max-width: 95%;
        padding: 30px 15px;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .news-grid .news-item:first-child {
        grid-column: span 1;
        width: 100%;
    }
    
    .news-item {
        width: 100%;
    }
    
    .news-image,
    .news-grid .news-item:first-child .news-image,
    .news-image img,
    .news-grid .news-item:first-child .news-image img {
        width: 100%;
        height: 220px;
        object-fit: cover;
    }
    
    .news-content h3 {
        font-size: 16px;
    }
    
    .news-content p {
        font-size: 13px;
    }
    
    .news-date {
        font-size: 14px;
    }
    
    .pagination {
        margin-top: 30px;
    }
    
    .pagination a, 
    .pagination span {
        min-width: 34px;
        height: 34px;
        padding: 0 10px;
    }
    
    .joinus-tabs .nav-link {
        padding: 0.7rem 1.2rem;
        font-size: 14px;
    }
}

/* 小型手机屏幕 (小于576px) */
@media (max-width: 576px) {
    .joinus-banner {
        height: 200px;
    }
    
    .joinus-banner-title {
        margin-left: 20px;
        font-size: 22px;
    }
    
    .news-container {
        padding: 25px 10px;
    }
    
    .news-grid {
        gap: 12px;
        margin-bottom: 30px;
    }
    
    .news-image,
    .news-grid .news-item:first-child .news-image,
    .news-image img,
    .news-grid .news-item:first-child .news-image img {
        width: 100%;
        height: 180px;
        object-fit: cover;
    }
    
    .news-content {
        padding: 12px;
    }
    
    .news-content h3 {
        font-size: 15px;
        margin-bottom: 6px;
    }
    
    .news-content p {
        font-size: 12px;
        margin-bottom: 10px;
        line-height: 1.3;
    }
    
    .news-date {
        font-size: 12px;
    }
    
    .pagination {
        margin-top: 25px;
    }
    
    .pagination ul {
        gap: 5px;
    }
    
    .pagination a,
    .pagination span {
        min-width: 32px;
        height: 32px;
        padding: 0 8px;
        font-size: 14px;
    }
    
    .joinus-tabs .nav-link {
        padding: 0.6rem 1rem;
        font-size: 13px;
    }
}

/* 超小型手机屏幕 (小于480px) */
@media (max-width: 480px) {
    .joinus-banner {
        height: 180px;
    }
    
    .joinus-banner-title {
        margin-left: 15px;
        font-size: 20px;
    }
    
    .news-content h3 {
        font-size: 14px;
    }
    
    .news-content p {
        font-size: 12px;
        -webkit-line-clamp: 2;
        line-clamp: 2;
    }
    
    .news-date {
        font-size: 11px;
    }
    
    .news-image,
    .news-grid .news-item:first-child .news-image,
    .news-image img,
    .news-grid .news-item:first-child .news-image img {
        width: 100%;
        height: 160px;
        object-fit: cover;
    }
    
    .pagination a,
    .pagination span {
        min-width: 30px;
        height: 30px;
        padding: 0 6px;
        font-size: 13px;
    }
    
    .joinus-tabs .nav-link {
        padding: 0.5rem 0.8rem;
        font-size: 12px;
    }
}