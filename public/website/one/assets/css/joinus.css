/* 加入我们页面样式 */
.joinus-banner {
    position: relative;
    width: 100%;
    height: 450px;
    background-image: url('../images/joinus-bg.png');
    background-size: cover;
    background-position: center;
    margin-top: 56px;
    display: flex;
    align-items: center;
}

.joinus-banner-title {
    color: white;
    font-weight: 500;
    font-size: 36px;
    margin-left: 283px;
}

.joinus-tabs {
    background-color: white;
    border-bottom: 1px solid #e1e5eb;
}

.joinus-tabs .nav-link {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    text-align: center;
    font-style: normal;
    padding: 1rem 2rem;
    border: none;
    border-bottom: 3px solid transparent;
}

.joinus-tabs .nav-link.active {
    border-bottom: 2px solid #0052d9;
    background-color: transparent;
}

.job-list {
    /* padding: 1rem 0; */
    background-color: #f5f7fa;
}

.job-list-title {
    font-style: normal;
    font-size: 28px;
    font-weight: 600;
    margin: 2rem 0rem;
    color: #333333;
}

.job-item {
    background-color: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.job-item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.job-content {
    flex: 1;
    cursor: pointer;
    transition: all 0.2s ease;
}

.job-content:hover {
    opacity: 0.9;
}

.job-title {
    font-style: normal;
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: #333;
}

.job-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 1rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
}

.job-tags span {
    display: inline-block;
}

.job-description {
    margin-bottom: 1rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 13px;
    color: #999999;
    line-height: 1.8;
}

.job-buttons {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 10px;
    margin-left: 20px;
    z-index: 2;
    position: relative;
}

.apply-btn {
    background-color: #0052d9;
    color: white;
    border: none;
    padding: 0.5rem 0;
    border-radius: 4px;
    font-weight: 500;
    width: 100px;
    text-align: center;
    cursor: pointer;
}

.share-btn {
    background-color: white;
    color: #333;
    border: 1px solid #e1e5eb;
    padding: 0.5rem 0;
    border-radius: 4px;
    font-weight: 500;
    width: 100px;
    text-align: center;
    margin-left: 0;
    cursor: pointer;
}

/* 移动端适配 */
@media (max-width: 1200px) {
    .job-list-title {
        margin: 1.8rem 0rem;
        font-size: 26px;
    }
}

@media (max-width: 992px) {
    .joinus-banner {
        height: 350px;
        margin-top: 60px;
    }
    
    .joinus-banner-title {
        margin-left: 50px;
        font-size: 30px;
    }
    
    .joinus-tabs .nav-link {
        padding: 0.75rem 1.5rem;
        font-size: 14px;
    }
    
    .job-list-title {
        margin: 1.5rem 0rem;
        font-size: 24px;
    }
    
    .job-item {
        flex-direction: column;
        padding: 1.5rem;
    }
    
    .job-title {
        font-size: 20px;
    }
    
    .job-tags {
        font-size: 14px;
    }
    
    .job-buttons {
        flex-direction: row;
        justify-content: center;
        margin-left: 0;
        margin-top: 15px;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .joinus-banner {
        height: 250px;
    }
    
    .joinus-banner-title {
        margin-left: 30px;
        font-size: 26px;
    }
    
    .joinus-tabs .nav-link {
        padding: 0.5rem 1rem;
        font-size: 13px;
    }
    
    .job-list-title {
        margin: 1.2rem 0rem;
        font-size: 22px;
    }
    
    .job-item {
        padding: 1.25rem;
    }
    
    .job-title {
        font-size: 18px;
    }
    
    .job-tags {
        font-size: 12px;
    }
    
    .job-description {
        font-size: 12px;
    }
    
    .job-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }
    
    .apply-btn, .share-btn {
        width: 90px;
    }
}

@media (max-width: 576px) {
    .joinus-banner {
        height: 200px;
    }
    
    .joinus-banner-title {
        margin-left: 20px;
        font-size: 22px;
    }
    
    .joinus-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 12px;
    }
    
    .job-list-title {
        margin: 1rem 0;
        font-size: 20px;
    }
    
    .job-item {
        padding: 1rem;
    }
    
    .job-title {
        font-size: 16px;
        margin-bottom: 0.5rem;
    }
    
    .job-tags {
        font-size: 11px;
        gap: 4px;
    }
    
    .job-description {
        font-size: 11px;
        line-height: 1.6;
    }
    
    .job-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 8px;
        margin-top: 12px;
    }
    
    .apply-btn, .share-btn {
        width: 80px;
        font-size: 12px;
        padding: 0.4rem 0;
        border-radius: 3px;
    }
}
