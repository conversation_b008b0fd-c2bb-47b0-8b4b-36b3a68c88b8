/* 职位详情页面样式 */
.job-detail-container {
    max-width: 1200px;
    margin: 90px auto 50px;
    padding: 40px 50px;
    background-color: #fff;
}

.job-detail-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    line-height: 42px;
    margin-bottom: 15px;
}

.job-detail-tags {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 25px;
    margin-bottom: 30px;
}

.job-detail-tags span {
    display: inline-block;
    margin-right: 5px;
}

.job-detail-section {
    margin-bottom: 30px;
}

.job-detail-section h3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 20px;
    color: #333333;
    line-height: 29px;
    margin-bottom: 15px;
    position: relative;
    padding-left: 15px;
}

.job-detail-section h3:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: #113DEA;
}

.job-detail-section ul {
    list-style-type: none;
    padding-left: 0;
    margin-bottom: 0;
}

.job-detail-section ul li {
    position: relative;
    padding-left: 20px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 28px;
    margin-bottom: 10px;
}

.job-detail-section ul li:before {
    content: '';
    position: absolute;
    left: 5px;
    top: 12px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #666;
}

.job-detail-buttons {
    display: flex;
    gap: 20px;
    margin-top: 40px;
    margin-bottom: 40px;
}

.apply-btn, .share-btn {
    width: 200px;
    height: 46px;
    border-radius: 4px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    line-height: 46px;
    text-align: center;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.apply-btn {
    background-color: #113DEA;
    color: #fff;
    border: none;
}

.apply-btn:hover {
    background-color: #0e34c7;
}

.share-btn {
    background-color: #fff;
    color: #666666;
    border: 1px solid #666666;
}

.share-btn:hover {
    background-color: #f5f5f5;
}

/* 大屏幕平板 (max-width: 992px) */
@media (max-width: 992px) {
    .job-detail-container {
        margin: 80px auto 40px;
        padding: 30px 40px;
    }
    
    .job-detail-title {
        font-size: 26px;
        line-height: 36px;
    }
    
    .job-detail-tags {
        font-size: 15px;
        line-height: 22px;
    }
    
    .job-detail-section h3 {
        font-size: 18px;
        line-height: 26px;
    }
    
    .job-detail-section ul li {
        font-size: 15px;
        line-height: 26px;
    }
    
    .apply-btn, .share-btn {
        width: 180px;
        height: 42px;
        line-height: 42px;
        font-size: 15px;
    }
}

/* 小屏幕平板 (max-width: 768px) */
@media (max-width: 768px) {
    .job-detail-container {
        margin: 70px auto 30px;
        padding: 25px 30px;
    }
    
    .job-detail-title {
        font-size: 22px;
        line-height: 32px;
    }
    
    .job-detail-tags {
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 25px;
    }
    
    .job-detail-section {
        margin-bottom: 25px;
    }
    
    .job-detail-section h3 {
        font-size: 17px;
        line-height: 24px;
        padding-left: 12px;
    }
    
    .job-detail-section h3:before {
        width: 3px;
        height: 16px;
    }
    
    .job-detail-section ul li {
        font-size: 14px;
        line-height: 24px;
        padding-left: 16px;
        margin-bottom: 8px;
    }
    
    .job-detail-section ul li:before {
        left: 4px;
        top: 10px;
        width: 5px;
        height: 5px;
    }
    
    .job-detail-buttons {
        gap: 15px;
        margin-top: 30px;
        margin-bottom: 30px;
        justify-content: center;
    }
    
    .apply-btn, .share-btn {
        width: 160px;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
    }
}

/* 手机屏幕 (max-width: 576px) */
@media (max-width: 576px) {
    .job-detail-container {
        margin: 60px auto 20px;
        padding: 20px 15px;
    }
    
    .job-detail-title {
        font-size: 20px;
        line-height: 28px;
    }
    
    .job-detail-tags {
        font-size: 13px;
        line-height: 18px;
        margin-bottom: 20px;
    }
    
    .job-detail-section {
        margin-bottom: 20px;
    }
    
    .job-detail-section h3 {
        font-size: 16px;
        line-height: 22px;
        padding-left: 10px;
        margin-bottom: 12px;
    }
    
    .job-detail-section h3:before {
        width: 3px;
        height: 14px;
    }
    
    .job-detail-section ul li {
        font-size: 13px;
        line-height: 22px;
        padding-left: 14px;
        margin-bottom: 6px;
    }
    
    .job-detail-section ul li:before {
        left: 3px;
        top: 9px;
        width: 4px;
        height: 4px;
    }
    
    .job-detail-buttons {
        flex-direction: row;
        gap: 10px;
        margin-top: 25px;
        margin-bottom: 25px;
    }
    
    .apply-btn, .share-btn {
        width: 130px;
        height: 38px;
        line-height: 38px;
        font-size: 13px;
    }
}