@font-face {
    font-family: 'iconfont';  /* Project id 4847539 */
    src: url('https://at.alicdn.com/t/c/font_4847539_87lpba1s08n.woff2?t=1743064482487') format('woff2'),
         url('https://at.alicdn.com/t/c/font_4847539_87lpba1s08n.woff?t=1743064482487') format('woff'),
         url('https://at.alicdn.com/t/c/font_4847539_87lpba1s08n.ttf?t=1743064482487') format('truetype');
  }
  
  .iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #E8E8E8;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .iconfont:hover {
    background-color: #d0d0d0;
  }
  
  .icon-weixin:before {
    content: "\e600";
  }
  
  .icon-douyin:before {
    content: "\e8db";
  }

  .icon-videonumber:before {
    content: "\e820";
  }
  

.navbar {
    background-color: rgba(0, 0, 0, 0.4);
    box-shadow: 0 1px 0 rgba(255, 255, 255, 0.6);
    height: 56px;
    z-index: 1030;
    transition: all 0.3s ease;
}

/* 非首页导航栏样式 */
.navbar.not-homepage {
    background-color: white;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

.navbar.not-homepage .navbar-nav .nav-link {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666 !important;
}

.navbar.not-homepage .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.7) !important;
    font-family: 'PingFang SC', sans-serif;
    font-size: 16px;
    padding: 0 1rem !important;
    position: relative;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link.active {
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500;
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 1rem;
    right: 1rem;
    height: 2px;
    background-color: #0047cc;
    transition: all 0.3s ease;
}

.navbar.not-homepage .navbar-nav .nav-link.active {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #333333 !important;
}

.navbar-brand img {
    height: 46px;
    width: auto;
}
body {
    background-color: #f8f9fa;
}
@media (max-width: 991.98px) {
    .navbar-brand {
        order: 2;
        margin: 0;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .navbar-brand img {
        height: 36px;
        width: auto;
    }
    .navbar-toggler {
        order: 1;
        margin: 0;
        border: none;
        padding: 0;
        width: 56px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .navbar-collapse {
        order: 3;
        background-color: rgba(0, 0, 0, 0.4);
        padding: 1rem 0;
        position: absolute;
        top: 56px;
        left: 0;
        right: 0;
    }
    .navbar.not-homepage .navbar-collapse {
        background-color: white;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }
    .navbar-nav {
        text-align: center;
    }
    .navbar-nav .nav-item {
        padding: 0.5rem 0;
    }
    
    /* 移动端下移除导航高亮效果 */
    .navbar-nav .nav-link.active::after {
        display: none;
    }
    
    .navbar-nav .nav-link.active {
        font-weight: normal;
    }
    
    .navbar.not-homepage .navbar-nav .nav-link.active {
        color: #0047cc!important;
        font-weight: normal;
    }
    
    .navbar .navbar-nav .nav-link.active {
        color: rgba(255, 255, 255, 0.7) ;
    }
}.home-page-container {
            position: relative;
            margin-top: 100vh;
        }
        .home-page-container .news {
            position: relative;
            top: 0;
        }
        .home-page-container .news .news-container {
            padding: 0 calc((100% - 1353px) / 2);
            margin-top: -150px;
        }
        .news-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: auto auto;
            gap: 23px;
        }
        .news-item {
            background: #FFFFFF;
            position: relative;
            overflow: hidden;
            border-radius: .375rem;
        }

        .home-news-title {
            color: #ffffff;
            font-family: AlibabaPuHuiTi;
            font-size: 1.875rem;
            height: 5rem;
            margin: 0 auto 1.25rem;
            padding-top: 2.5rem;
            width: 100%;
        }
        .news-item:first-child {
            grid-row: 1;
            grid-column: 1 / 3;
        }
        .news-item:nth-child(2) {
            grid-row: 1;
            grid-column: 3;
        }
        .news-item:nth-child(3) {
            grid-row: 2;
            grid-column: 1;
        }
        .news-item:nth-child(4) {
            grid-row: 2;
            grid-column: 2;
        }
        .news-item:nth-child(5) {
            grid-row: 2;
            grid-column: 3;
        }
        .news-image {
            
            overflow: hidden;
        }
        .news-item:first-child .news-image {
            height: 28.125rem;
        }
        .news-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .news-content {
            padding: 32px;
        }
        .news-item {
            background: #FFFFFF;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .news-item:hover {
            background-color: #113DEA;
        }
        .news-item:hover .news-content h3 {
            color: #FFFFFF;
        }
        .news-item:hover .news-content .news-date {
            color: #FFFFFF !important;
        }
        .news-content h3 {
            font-family: 'PingFang SC';
            font-size: 22px;
            font-weight: 500;
            color: #333333;
            margin: 0;
            line-height: 1.5;
        }
        .news-item-blue .news-content h3 {
            color: #FFFFFF;
        }
        .news-date {
            font-family: 'PingFang SC';
            font-size: 16px;
            color: #999999;
            margin-top: 16px;
        }
        @media (max-width: 1400px) {
            .home-page-container .news .news-container {
                padding: 0 24px;
            }
        }
        @media (max-width: 1350px) {
            /* 设置所有新闻图片高度一致 */
            .news-item .news-image {
                /* height: 240px; */
            }
            
            .news-item:first-child .news-image {
                height: 240px; /* 确保与其他图片高度一致 */
            }
            
            /* 调整网格布局为均等的3列 */
            .news-grid {
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: auto auto;
                gap: 20px;
            }
            
            /* 重新排列新闻项目 */
            .news-item:first-child {
                grid-column: 1 / 2;
                grid-row: 1;
            }
            
            .news-item:nth-child(2) {
                grid-column: 2 / 3;
                grid-row: 1;
            }
            
            .news-item:nth-child(3) {
                grid-column: 3 / 4;
                grid-row: 1;
            }
            
            .news-item:nth-child(4) {
                grid-column: 1 / 2;
                grid-row: 2;
            }
            
            .news-item:nth-child(5) {
                grid-column: 2 / 3;
                grid-row: 2;
            }
        }
        @media (max-width: 768px) {
            .news-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
                gap: 16px;
            }
            .news-item,
            .news-item:first-child,
            .news-item:nth-child(2),
            .news-item:nth-child(3),
            .news-item:nth-child(4),
            .news-item:nth-child(5) {
                grid-column: 1;
                grid-row: auto;
            }
        }
        .home-page-container .slide-top-bg {
            -webkit-animation: slide-top-bg .9s cubic-bezier(.25, .46, .45, .94) both;
            animation: slide-top-bg .9s cubic-bezier(.25, .46, .45, .94) both;
        }
        .home-page-container .slide-top {
            -webkit-animation: slide-top .9s cubic-bezier(.25, .46, .45, .94) both;
            animation: slide-top .9s cubic-bezier(.25, .46, .45, .94) both;
        }
        @keyframes slide-bottom-bg {
            0% {
                transform: translateY(-500px);
            }
            100% {
                transform: translateY(0);
            }
        }
        @keyframes slide-top-bg {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-500px);
            }
        }
        @keyframes slide-top {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-150px);
            }
        }

        .carousel {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin-top: 0;
            height: 100vh;
        }
        .carousel-item {
            position: relative;
            height: 100vh;
        }
        .carousel-item img,
        .carousel-item video {
            object-fit: cover;
            height: 100%;
            width: 100%;
        }
        .carousel-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            /* background-color: rgba(0, 0, 0, 0.4); */
        }
        .carousel-caption {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            text-align: left;
            max-width: 800px;
            margin-left: 10%;
            bottom: auto;
        }
        .carousel-caption .main-title {
            font-size: 2.5rem;
            font-weight: bold;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease forwards;
        }
        .carousel-caption .sub-title {
            font-size: 1.2rem;
            margin-top: 1rem;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease forwards 0.4s;
        }
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @media (max-width: 768px) {
            .carousel-caption {
                margin-left: 5%;
                max-width: 90%;
            }
            .carousel-caption .main-title {
                font-size: 1.8rem;
            }
            .carousel-caption .sub-title {
                font-size: 1rem;
            }
        }
.footer {
    background-color: #FBFBFB;
    padding: 40px 0;
}

.footer-logo {
    height: 54px;
}

.footer h5 {
    color: #333;
    font-weight: normal;
    font-size: 18px;
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 0;
}

.social-icons {
    display: inline-flex;
    align-items: center;
}

.footer ul li a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 14px;
}

.footer hr {
    border-color: #ddd;
}

.footer ul li {
    margin-bottom: 10px;
}

.footer ul li a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer ul li a:hover {
    color: #333;
}

.footer .text-center p {
    font-size: 12px;
    color: #999999;
}
@media (max-width: 768px) {
    .footer {
        padding: 20px 0;
    }
    .footer-logo {
        height: 60px;
        /* margin-bottom: 20px; */
    }
    .footer .col-md-6 {
        text-align: center !important;
    }
    .footer h5 {
        font-size: 16px;
        margin-bottom: 10px;
        display: block;
    }
    .social-icons {
        justify-content: center;
    }
    .footer .row > div {
        /* margin-bottom: 20px; */
    }
    .footer ul li a {
        font-size: 14px;
    }
    .footer .text-end {
        text-align: center !important;
    }
}

.footer ul li a:hover {
    color: #333;
}

.footer .text-center p {
    font-size: 12px;
    color: #999999;
}
@media (max-width: 768px) {
    .footer .text-end {
        text-align: left !important;
        margin-top: 20px;
    }
}
.social-icons .iconfont {
    position: relative;
}

.social-icons .iconfont:hover .qr-code {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.qr-code {
    position: absolute;
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    background-color: #fff;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    text-align: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.qr-code img {
    width: 120px;
    height: 120px;
    margin-bottom: 8px;
}

.qr-code p {
    margin: 0;
    color: #333;
    font-size: 14px;
}