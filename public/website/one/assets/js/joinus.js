// 加入我们页面的交互功能
document.addEventListener('DOMContentLoaded', function() {
    // 确保组件已加载
    if (typeof loadComponents === 'function') {
        loadComponents();
    }
    
    // 添加职位项目点击事件
    const jobItems = document.querySelectorAll('.job-item');
    jobItems.forEach(item => {
        // 获取内容区域
        const jobContent = item.querySelector('.job-content');
        
        // 添加点击事件
        jobContent.addEventListener('click', function(e) {
            // 阻止事件冒泡，避免点击按钮时也触发跳转
            if (e.target.closest('.job-buttons')) {
                return;
            }
            
            // 获取职位信息
            const title = item.querySelector('.job-title').textContent;
            const tags = item.querySelector('.job-tags').innerHTML;
            const desc = item.querySelector('.job-description').innerHTML;
            
            // 构建URL参数
            const params = new URLSearchParams();
            params.append('title', title);
            params.append('tags', tags);
            params.append('desc', desc);
            
            // 跳转到详情页
            window.location.href = `detail.html`;
        });
    });
    
    // 为按钮添加单独的点击事件，阻止事件冒泡
    const buttons = document.querySelectorAll('.job-buttons button');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            
            // 根据按钮类型执行不同操作
            if (this.classList.contains('apply-btn')) {
                alert('申请职位功能即将上线，敬请期待！');
            } else if (this.classList.contains('share-btn')) {
                alert('分享功能即将上线，敬请期待！');
            }
        });
    });
});
